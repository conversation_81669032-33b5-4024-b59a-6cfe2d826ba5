﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
		Microsoft ResX Schema

		Version 1.3

		The primary goals of this format is to allow a simple XML format 
		that is mostly human readable. The generation and parsing of the 
		various data types are done through the TypeConverter classes 
		associated with the data types.

		Example:

		... ado.net/XML headers & schema ...
		<resheader name="resmimetype">text/microsoft-resx</resheader>
		<resheader name="version">1.3</resheader>
		<resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
		<resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
		<data name="Name1">this is my long string</data>
		<data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
		<data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
			[base64 mime encoded serialized .NET Framework object]
		</data>
		<data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
			[base64 mime encoded string representing a byte array form of the .NET Framework object]
		</data>

		There are any number of "resheader" rows that contain simple 
		name/value pairs.

		Each data row contains a name, and value. The row also contains a 
		type or mimetype. Type corresponds to a .NET class that support 
		text/value conversion through the TypeConverter architecture. 
		Classes that don't support this are serialized and stored with the 
		mimetype set.

		The mimetype is used for serialized objects, and tells the 
		ResXResourceReader how to depersist the object. This is currently not 
		extensible. For a given mimetype the value must be set accordingly:

		Note - application/x-microsoft.net.object.binary.base64 is the format 
		that the ResXResourceWriter will generate, however the reader can 
		read any of the formats listed below.

		mimetype: application/x-microsoft.net.object.binary.base64
		value   : The object must be serialized with 
			: System.Serialization.Formatters.Binary.BinaryFormatter
			: and then encoded with base64 encoding.

		mimetype: application/x-microsoft.net.object.soap.base64
		value   : The object must be serialized with 
			: System.Runtime.Serialization.Formatters.Soap.SoapFormatter
			: and then encoded with base64 encoding.

		mimetype: application/x-microsoft.net.object.bytearray.base64
		value   : The object must be serialized into a byte array 
			: using a System.ComponentModel.TypeConverter
			: and then encoded with base64 encoding.
	-->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>1.3</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.3500.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.3500.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Test" xml:space="preserve">
    <value>Тест</value>
  </data>
  <data name="AddNews" xml:space="preserve">
    <value>Добавить новость</value>
  </data>
  <data name="NewsTitleDesc" xml:space="preserve">
    <value>Новости</value>
  </data>
  <data name="BtnEdit" xml:space="preserve">
    <value>Редактировать</value>
  </data>
  <data name="BtnDetails" xml:space="preserve">
    <value>Просмотреть</value>
  </data>
  <data name="BtnDelete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Итого</value>
  </data>
  <data name="News" xml:space="preserve">
    <value>Новости</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Контакты</value>
  </data>
  <data name="OwnerTitle" xml:space="preserve">
    <value>AroMarket</value>
  </data>
  <data name="EditTitle" xml:space="preserve">
    <value>Редактирование</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Назад к списку</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="Lang" xml:space="preserve">
    <value>ru</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Язык</value>
  </data>
  <data name="LangCode" xml:space="preserve">
    <value>Ru</value>
  </data>
  <data name="LangDesc" xml:space="preserve">
    <value>Русский</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Ширина</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Регионы</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>Создать запись</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Код</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Название</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="MapZoom" xml:space="preserve">
    <value>Приближение карты</value>
  </data>
  <data name="MapCenterY" xml:space="preserve">
    <value>Центр карты Y</value>
  </data>
  <data name="MapCenterX" xml:space="preserve">
    <value>Центр карты X</value>
  </data>
  <data name="RegionsTitleDesc" xml:space="preserve">
    <value>Регион в мобильном приложении</value>
  </data>
  <data name="TitleDetails" xml:space="preserve">
    <value>Просмотр</value>
  </data>
  <data name="CreateTitle" xml:space="preserve">
    <value>Новая запись</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Добавить</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Вы уверены, что хотите удалить эту запись?</value>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>Удаление</value>
  </data>
  <data name="DividerOr" xml:space="preserve">
    <value>или</value>
  </data>
  <data name="AddRegion" xml:space="preserve">
    <value>Добавить регион</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Добавить</value>
  </data>
  <data name="YourSecCode" xml:space="preserve">
    <value>Ваш секретный код: </value>
  </data>
  <data name="EmailFrom" xml:space="preserve">
    <value>Панель управления AppoMobi</value>
  </data>
  <data name="EmailCreateAccSubject" xml:space="preserve">
    <value>AppoMobi: подтвердите создание учетной записи</value>
  </data>
  <data name="AccCreationTitle" xml:space="preserve">
    <value>Создание учетной записи</value>
  </data>
  <data name="AccCeationConfirmEmail" xml:space="preserve">
    <value>Мы выслали вам электронное письмо для подтверждения адреса электронной почты на ящик {0}.
Пожалуйста, следуйте инструкциям в этом письме для завершения создания учетной записи.</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Регион</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Время</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Текст</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Действие</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>Параметры действия</value>
  </data>
  <data name="ImageURL" xml:space="preserve">
    <value>Изображение (ссылка)</value>
  </data>
  <data name="Author" xml:space="preserve">
    <value>Автор</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>Редактировал(а)</value>
  </data>
  <data name="EditedTime" xml:space="preserve">
    <value>Последняя редакция</value>
  </data>
  <data name="ImageHeight" xml:space="preserve">
    <value>Высота изображения</value>
  </data>
  <data name="ImageWidth" xml:space="preserve">
    <value>Ширина изображения</value>
  </data>
  <data name="ThankYouForConfirmingYourEmailPlease" xml:space="preserve">
    <value>Спасибо за подтверждение адреса электронной почты. Пожалуйста,</value>
  </data>
  <data name="ClickHereToLogIn" xml:space="preserve">
    <value>кликните здесь чтоб войти</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="YouVeSuccessfullyAuthenticatedWith" xml:space="preserve">
    <value>Вы успешно прошли аутентификацию с помощью</value>
  </data>
  <data name="PleaseEnterAUserNameForThisSiteBelow" xml:space="preserve">
    <value>Пожалуйста, введите ваше имя пользователя для использования на сайте и нажмите кнопку Зарегистрироваться, чтобы завершить регистрацию.</value>
  </data>
  <data name="RegisterTitle" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="AssociateYourAccount" xml:space="preserve">
    <value>Свяжите свою учетную запись {0}.</value>
  </data>
  <data name="UnsuccessfulLoginWithService" xml:space="preserve">
    <value>Неудачный вход со внешним сервисом.</value>
  </data>
  <data name="LoginFailure" xml:space="preserve">
    <value>Ошибка входа в систему</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>Вход в систему</value>
  </data>
  <data name="OrUseAnotherServiceToLogIn" xml:space="preserve">
    <value>Или используйте внешний сервис для входа</value>
  </data>
  <data name="UseALocalAccountToLogIn" xml:space="preserve">
    <value>Использовать локальную учетную запись</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Запомнить меня</value>
  </data>
  <data name="BtnLogIn" xml:space="preserve">
    <value>Войти</value>
  </data>
  <data name="RegisterAsANewUser" xml:space="preserve">
    <value>Создать учетную запись</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Пароль</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Подтверждение пароля</value>
  </data>
  <data name="CreateANewAccount" xml:space="preserve">
    <value>Создайте новую учетную запись</value>
  </data>
  <data name="BtnRegister" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="ToolbarLogin" xml:space="preserve">
    <value>Вход</value>
  </data>
  <data name="ToolbarRegister" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="ToolbarHello" xml:space="preserve">
    <value>Привет, </value>
  </data>
  <data name="ToolbarLogoff" xml:space="preserve">
    <value>(Не забудьте) Выйти</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="MoreInfo" xml:space="preserve">
    <value>Дополнительно</value>
  </data>
  <data name="OnMap" xml:space="preserve">
    <value>На карте</value>
  </data>
  <data name="Centers" xml:space="preserve">
    <value>Центры</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>Адрес</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Заметки</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Сайт</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Телефон</value>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>Почта</value>
  </data>
  <data name="Metro" xml:space="preserve">
    <value>Метро</value>
  </data>
  <data name="ExportedBy" xml:space="preserve">
    <value>Экспортировал(а)</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Статус</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Активный</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Архив</value>
  </data>
  <data name="ExportedTime" xml:space="preserve">
    <value>Экспортировано</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>Подназвание</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Город</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Страна</value>
  </data>
  <data name="Uploads" xml:space="preserve">
    <value>Файлы</value>
  </data>
  <data name="UploadImage" xml:space="preserve">
    <value>Загрузить изображение</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>Поиск по имени</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Экспорт</value>
  </data>
  <data name="NotAllowed" xml:space="preserve">
    <value>Не разрешен</value>
  </data>
  <data name="Allowed" xml:space="preserve">
    <value>Разрешен</value>
  </data>
  <data name="Needed" xml:space="preserve">
    <value>, требуется!</value>
  </data>
  <data name="ToBeExported" xml:space="preserve">
    <value>На экспорт</value>
  </data>
  <data name="HelpAllowToBeExportedForMobileAppOrNot" xml:space="preserve">
    <value>Следует ли экспортировать эту запись в мобильное приложение</value>
  </data>
  <data name="SortList" xml:space="preserve">
    <value>Сортировать</value>
  </data>
  <data name="SortDate" xml:space="preserve">
    <value>По дате изменения</value>
  </data>
  <data name="NewsController_Create_ERRORUImageURLNotValid" xml:space="preserve">
    <value>ОШИБКА: ссылка на изображение не рабочая!</value>
  </data>
  <data name="OwnerTitleShort" xml:space="preserve">
    <value>AroMarket</value>
  </data>
  <data name="AppoMobiControlPanel" xml:space="preserve">
    <value>Панель управления</value>
  </data>
  <data name="Exports" xml:space="preserve">
    <value>Экспорт</value>
  </data>
  <data name="CreateExportFor" xml:space="preserve">
    <value>Экспортировать:</value>
  </data>
  <data name="ExportType" xml:space="preserve">
    <value>Тип экспорта</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>Доступ запрещен</value>
  </data>
  <data name="DonTHaveTheRights" xml:space="preserve">
    <value>Похоже, что у вас нет прав доступа к разделу. Если вы считаете, что это ошибкa, пожалуйста, обратитесь в службу поддержки.</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Экспортирование</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>Вы уверены, что хотите экспортировать?</value>
  </data>
  <data name="ExportsController_Index_ExportComplete" xml:space="preserve">
    <value>Экспорт успешно произведен!</value>
  </data>
  <data name="BaseURL" xml:space="preserve">
    <value>Базовый URL</value>
  </data>
  <data name="SalonList" xml:space="preserve">
    <value>Список центров</value>
  </data>
  <data name="InSection" xml:space="preserve">
    <value>в разделе</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Все</value>
  </data>
  <data name="ShouldNotBeExported" xml:space="preserve">
    <value>Не идет на экспорт</value>
  </data>
  <data name="WasWellExported" xml:space="preserve">
    <value>Экспортировано</value>
  </data>
  <data name="ShouldBeExported" xml:space="preserve">
    <value>Идет на экспорт</value>
  </data>
  <data name="InRegion" xml:space="preserve">
    <value>в регионе</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>по</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Продукция</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>Категории</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>Служебное</value>
  </data>
  <data name="RoleSuperuser" xml:space="preserve">
    <value>Разработчик</value>
  </data>
  <data name="RoleAdmin" xml:space="preserve">
    <value>Администратор</value>
  </data>
  <data name="RoleEditor" xml:space="preserve">
    <value>Редактор</value>
  </data>
  <data name="RoleNoRole" xml:space="preserve">
    <value>Посетитель</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Принадлежность</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Приоритет</value>
  </data>
  <data name="SortDefault" xml:space="preserve">
    <value>Стандартно</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Информация</value>
  </data>
  <data name="Subcategories" xml:space="preserve">
    <value>Подкатегории</value>
  </data>
  <data name="ParentElementToInsertCategoryInto" xml:space="preserve">
    <value>Родительская категория, в которую вставить этот элемент</value>
  </data>
  <data name="RootCategory" xml:space="preserve">
    <value>Базовая категория</value>
  </data>
  <data name="CatNewsSlider" xml:space="preserve">
    <value>Новостной слайдер</value>
  </data>
  <data name="CatSecRoot" xml:space="preserve">
    <value>Вторичная базовая категория</value>
  </data>
  <data name="UploadMiniImage" xml:space="preserve">
    <value>Загрузить МИНИ-изображение</value>
  </data>
  <data name="ImageURLForMini" xml:space="preserve">
    <value>Ссылка на МИНИ-изображение</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Категория</value>
  </data>
  <data name="Volume" xml:space="preserve">
    <value>Объем</value>
  </data>
  <data name="ILike" xml:space="preserve">
    <value>Мне нравится</value>
  </data>
  <data name="Units" xml:space="preserve">
    <value>Единицы измерения</value>
  </data>
  <data name="Keywords" xml:space="preserve">
    <value>Ключевые слова</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Новинка</value>
  </data>
  <data name="ShowList" xml:space="preserve">
    <value>Показать</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Поиск..</value>
  </data>
  <data name="ProductsController_CreateDropdownList_ANYCAT" xml:space="preserve">
    <value>Все категории</value>
  </data>
  <data name="SortCode" xml:space="preserve">
    <value>По коду</value>
  </data>
  <data name="SortCat" xml:space="preserve">
    <value>По категории</value>
  </data>
  <data name="RoleMerchandiser" xml:space="preserve">
    <value>Товаровед</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Сброс</value>
  </data>
  <data name="CatRoot2" xml:space="preserve">
    <value>Базовая категория 2</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>Напишите причину</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>Изображение анонса</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>Тело</value>
  </data>
  <data name="Face" xml:space="preserve">
    <value>Лицо</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Да</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Нет</value>
  </data>
  <data name="OurChoice" xml:space="preserve">
    <value>Наш выбор</value>
  </data>
  <data name="ForgotYourPassword" xml:space="preserve">
    <value>Забыли пароль?</value>
  </data>
  <data name="NoRUTranslation" xml:space="preserve">
    <value>Нет перевода RU</value>
  </data>
  <data name="ErrorNotFound" xml:space="preserve">
    <value>Not Found Error</value>
  </data>
  <data name="ErrorUnknown" xml:space="preserve">
    <value>Неизвестная ошибка</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>Мы в Интернет</value>
  </data>
  <data name="Redirect" xml:space="preserve">
    <value>Ссылка</value>
  </data>
  <data name="Clicks" xml:space="preserve">
    <value>Клики</value>
  </data>
  <data name="AreYouSureToDelete" xml:space="preserve">
    <value>Действительно удалить</value>
  </data>
  <data name="Treatment" xml:space="preserve">
    <value>Уход</value>
  </data>
  <data name="Treatments" xml:space="preserve">
    <value>Уходы</value>
  </data>
  <data name="ErrorPleaseCheckRequirementsForFieldsBelow" xml:space="preserve">
    <value>ОШИБКА заполнения полей, см. ниже:</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Товары</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>Управление</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Пользователи</value>
  </data>
  <data name="ResetChanges" xml:space="preserve">
    <value>Отменить правки</value>
  </data>
  <data name="DoNotSave" xml:space="preserve">
    <value>Вернуться</value>
  </data>
  <data name="List" xml:space="preserve">
    <value>Список</value>
  </data>
  <data name="EditorSNotesInternalUseOnly" xml:space="preserve">
    <value>Ваши заметки, видны лишь в панели.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Ошибка</value>
  </data>
  <data name="ControlPanelHtml" xml:space="preserve">
    <value>&lt;strong&gt;AppoMobi&lt;/strong&gt; Панель управления</value>
  </data>
  <data name="RememberMe2" xml:space="preserve">
    <value>Запомнить?</value>
  </data>
  <data name="DidYouRememberYourPassword" xml:space="preserve">
    <value>Вспомнили пароль?</value>
  </data>
  <data name="BtnResetPassword" xml:space="preserve">
    <value>Сбросить пароль</value>
  </data>
  <data name="PleaseCheckYourEmailToResetYourPassword" xml:space="preserve">
    <value>Вы выслали вам ссылку сброса пароля на электронную почту.</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Сброс пароля</value>
  </data>
  <data name="DoYouHaveAnAccount" xml:space="preserve">
    <value>Уже есть учетная запись?</value>
  </data>
  <data name="RegisterAccount" xml:space="preserve">
    <value>Зарегистрироваться</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Фамилия</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Имя</value>
  </data>
  <data name="AgreeToTerms" xml:space="preserve">
    <value>Согласен c &lt;strong&gt;условиями&lt;/strong&gt;</value>
  </data>
  <data name="YouMustAcceptTermsAndConditions" xml:space="preserve">
    <value>Вы должны принять условия пользовательского соглашения.</value>
  </data>
  <data name="PasswordAndConfirmationPasswordDoNotMatch" xml:space="preserve">
    <value>Пароль и пароль подтверждения не совпадают.</value>
  </data>
  <data name="StringLengthError" xml:space="preserve">
    <value>Строка {0} должна быть длиной от {2} символов.</value>
  </data>
  <data name="BadUsernameOrPassword" xml:space="preserve">
    <value>Неверное имя пользователя или пароль.</value>
  </data>
  <data name="EmailAlreadyTaken" xml:space="preserve">
    <value>Электронный адрес '{0}' уже занят.</value>
  </data>
  <data name="MailSubjectResetPassword" xml:space="preserve">
    <value>Сброс пароля</value>
  </data>
  <data name="ResetYourPasswordMailBody" xml:space="preserve">
    <value>Вы можете сбросить свой пароль, нажав на &lt;a href="{0}"&gt;эту ссылку&lt;/a&gt;.</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Новый пароль</value>
  </data>
  <data name="PleaseEnterYourNewPasswordBelow" xml:space="preserve">
    <value>Придумайте ваш новый пароль и введите ниже.</value>
  </data>
  <data name="DidYouRememberYourOLDPassword" xml:space="preserve">
    <value>Вспомнили старый пароль?</value>
  </data>
  <data name="AccountController_ResetPassword_InvalidToken" xml:space="preserve">
    <value>Ссылка для восстановления пароля устарела.</value>
  </data>
  <data name="YourNewPasswordHasBeenSet" xml:space="preserve">
    <value>Ваш новый пароль был установлен.</value>
  </data>
  <data name="Class" xml:space="preserve">
    <value>Класс</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Конфиденциальность</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Версия от</value>
  </data>
  <data name="FastLoginWith" xml:space="preserve">
    <value>Быстрый вход с</value>
  </data>
  <data name="OrEnterYourCredentials" xml:space="preserve">
    <value>или введите ваши данные</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Настройки</value>
  </data>
  <data name="ExternalLogins" xml:space="preserve">
    <value>Вход через соцсеть</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>Нет</value>
  </data>
  <data name="ChangeYourPassword" xml:space="preserve">
    <value>Сменить пароль</value>
  </data>
  <data name="ManageAccount" xml:space="preserve">
    <value>Управление учетной записью</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>Изменить</value>
  </data>
  <data name="TwoFactorAuthentication" xml:space="preserve">
    <value>Двух-факторная аутентификация</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Ваш телефон</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Отключено</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Вкл.</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Включить</value>
  </data>
  <data name="Manage" xml:space="preserve">
    <value>Настроить</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Выключить</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="ManageYourExternalLogins" xml:space="preserve">
    <value>Вход с помощью социальной сети</value>
  </data>
  <data name="Theme" xml:space="preserve">
    <value>Скин</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Ключ</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Закрыть</value>
  </data>
  <data name="ThankYou" xml:space="preserve">
    <value>Спасибо!</value>
  </data>
  <data name="EmailConfirmed" xml:space="preserve">
    <value>Благодарим вас за подтверждение вашей электронной почты. Теперь вы можете использовать свои учетные данные для входа в систему.</value>
  </data>
  <data name="AccNotActiveForCLient" xml:space="preserve">
    <value>Ваша учетная запись еще не была назначена существующему клиенту AppoMobi. Обратитесь в службу технической поддержки.</value>
  </data>
  <data name="NothingWasFound" xml:space="preserve">
    <value>Ничего не найдено!</value>
  </data>
  <data name="ClientControlPanel" xml:space="preserve">
    <value>Клиентская панель управления</value>
  </data>
  <data name="ThankYouForBeingPatient" xml:space="preserve">
    <value>Мы проводим некоторые работы на сайте, и он скоро вернется. Благодарим за терпение!</value>
  </data>
  <data name="UnderConstruction" xml:space="preserve">
    <value>В разработке</value>
  </data>
  <data name="SorryWeReDoingSomeWorkOnTheSite" xml:space="preserve">
    <value>Мы работаем над сайтом</value>
  </data>
  <data name="Desktop" xml:space="preserve">
    <value>Рабочий стол</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Содержит</value>
  </data>
  <data name="ProductElements" xml:space="preserve">
    <value>Компоненты продукции</value>
  </data>
  <data name="Specifications" xml:space="preserve">
    <value>Компоненты</value>
  </data>
  <data name="DescriptionRU" xml:space="preserve">
    <value>Описание RU</value>
  </data>
  <data name="DescriptionEN" xml:space="preserve">
    <value>Описание EN</value>
  </data>
  <data name="DescriptionFR" xml:space="preserve">
    <value>Описание FR</value>
  </data>
  <data name="DisabledEntryDesc" xml:space="preserve">
    <value>Если включено, то запись не будет экспортирована из панели</value>
  </data>
  <data name="UploadFileFieldDesc" xml:space="preserve">
    <value>Вы можете загрузить файл или ввести существующий URL-адрес в следующем поле</value>
  </data>
  <data name="NoAction" xml:space="preserve">
    <value>Без действия</value>
  </data>
  <data name="OpenProductInApp" xml:space="preserve">
    <value>Открыть товар в программе</value>
  </data>
  <data name="NavigateToUrl" xml:space="preserve">
    <value>Открыть веб-ссылку</value>
  </data>
  <data name="FieldMustBeUnique" xml:space="preserve">
    <value>Значение поля '{0}' должно быть уникально</value>
  </data>
  <data name="ContentLanguages" xml:space="preserve">
    <value>Языки контента</value>
  </data>
  <data name="Enter2LettersLanguageCodes" xml:space="preserve">
    <value>Введите 2-буквенные коды языков</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Неизвестно</value>
  </data>
  <data name="CompanyInfo" xml:space="preserve">
    <value>О Компании</value>
  </data>
  <data name="OrUploadFromDisk" xml:space="preserve">
    <value>Изменить..</value>
  </data>
  <data name="SortOutDate" xml:space="preserve">
    <value>По дате выпуска</value>
  </data>
  <data name="SortPriority" xml:space="preserve">
    <value>По приоритету сортировки</value>
  </data>
  <data name="ShowOnPage" xml:space="preserve">
    <value>На странице</value>
  </data>
  <data name="ExportSection" xml:space="preserve">
    <value>Быстрый экспорт</value>
  </data>
  <data name="PlsConfirmExport" xml:space="preserve">
    <value>Подтверждаете экспорт раздела?</value>
  </data>
  <data name="BaseControllerContent__IndexGet_ExportCompletedWithSuccess" xml:space="preserve">
    <value>Экспорт прошел успешно.</value>
  </data>
  <data name="CEOSiteDesc" xml:space="preserve">
    <value>AppoMobi is a worldwide provider for business mobile applications solutions.</value>
  </data>
  <data name="SiteLoading" xml:space="preserve">
    <value>Загрузка</value>
  </data>
  <data name="PushMessages" xml:space="preserve">
    <value>Пуш-уведомления</value>
  </data>
  <data name="NewsMenu" xml:space="preserve">
    <value>Ваши новости</value>
  </data>
  <data name="NavigateToWww" xml:space="preserve">
    <value>Открыть веб-ссылку внутри программы</value>
  </data>
  <data name="SimpleMessage" xml:space="preserve">
    <value>Простое сообщение</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>Отправить сейчас же</value>
  </data>
  <data name="SaveForLater" xml:space="preserve">
    <value>Сохранить черновик</value>
  </data>
  <data name="PushEngagedUsers" xml:space="preserve">
    <value>Активные пользователи</value>
  </data>
  <data name="PushActiveUsers" xml:space="preserve">
    <value>Заходили недавно</value>
  </data>
  <data name="PushInactiveUsers" xml:space="preserve">
    <value>Заходили давно</value>
  </data>
  <data name="OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage" xml:space="preserve">
    <value>Необходимо заполнить текстовое поле для английского языка.</value>
  </data>
  <data name="Android" xml:space="preserve">
    <value>Андроид</value>
  </data>
  <data name="AppleIOS" xml:space="preserve">
    <value>Apple iOS</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>Разработчик</value>
  </data>
  <data name="DevicesTotal" xml:space="preserve">
    <value>Всего устройств</value>
  </data>
  <data name="SavedForLater" xml:space="preserve">
    <value>Черновик</value>
  </data>
  <data name="maskSentToDevices" xml:space="preserve">
    <value>Отправлено на {0}</value>
  </data>
  <data name="PushMessagesWereNotConfigured" xml:space="preserve">
    <value>Push-сообщения еще не были для вас настроены.</value>
  </data>
  <data name="Tenants" xml:space="preserve">
    <value>Держатели</value>
  </data>
  <data name="ClientGlobalSettings" xml:space="preserve">
    <value>Общие клиентские настройки</value>
  </data>
  <data name="ChangesSaved" xml:space="preserve">
    <value>Изменения сохранены</value>
  </data>
  <data name="NewsController_NewsController_ByRegion" xml:space="preserve">
    <value>По региону показа</value>
  </data>
  <data name="MessagableDevicesTotal" xml:space="preserve">
    <value>Количество активных устройств</value>
  </data>
  <data name="TotalInstallations" xml:space="preserve">
    <value>Общее количество установок</value>
  </data>
  <data name="CreateAPassword" xml:space="preserve">
    <value>Придумайте пароль</value>
  </data>
  <data name="YourSecCodeLoginMask" xml:space="preserve">
    <value>Ваш секретный код {0} для входа в панель управления AppoMobi. </value>
  </data>
  <data name="IncorrectEmailAddressOrPhoneNumber" xml:space="preserve">
    <value>Некорректный email адрес или номер телефона.</value>
  </data>
  <data name="AUserWithThisPhoneNumberWasNotFoundPleaseRegister" xml:space="preserve">
    <value>Пользователь с таким номером телефона не найден. Пожалуйста, зарегистрируйтесь.</value>
  </data>
  <data name="PleaseCheckYouDevice" xml:space="preserve">
    <value>Проверьте ваш телефон</value>
  </data>
  <data name="WeHaveSentAVerificationCodeToYourNumber" xml:space="preserve">
    <value>Мы выслали проверочный код на указанный вами номер</value>
  </data>
  <data name="UserWithThisPhoneNumberAlreadyRegistered" xml:space="preserve">
    <value>Пользователь с указанным номером телефона уже зарегистрирован.</value>
  </data>
  <data name="WrongCodeEntered" xml:space="preserve">
    <value>Неверный проверочный код.</value>
  </data>
  <data name="StatusConfirmed" xml:space="preserve">
    <value>Подтверждено</value>
  </data>
  <data name="StatusPendingConfirmation" xml:space="preserve">
    <value>Ожидает подтверждения</value>
  </data>
  <data name="StatusDisapproved" xml:space="preserve">
    <value>Отклонено</value>
  </data>
  <data name="BookingSystem" xml:space="preserve">
    <value>Система бронирования</value>
  </data>
  <data name="BookingFrontDesk" xml:space="preserve">
    <value>Окно заявок</value>
  </data>
  <data name="BookingSchedule" xml:space="preserve">
    <value>Рабочий график</value>
  </data>
  <data name="BookingRequests" xml:space="preserve">
    <value>Запросы на бронирование</value>
  </data>
  <data name="BookingObjects" xml:space="preserve">
    <value>Объекты бронирования</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Создать событие</value>
  </data>
  <data name="InsertEventName" xml:space="preserve">
    <value>Введите название события</value>
  </data>
  <data name="DragAndDropEventsOnTheCalendar" xml:space="preserve">
    <value>Перетащите событие на календарь</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>Ошибка связи</value>
  </data>
  <data name="NavigateToCenter" xml:space="preserve">
    <value>Открыть карточку центра в программе</value>
  </data>
  <data name="LatestMobileAppVersion" xml:space="preserve">
    <value>Последняя версия моб.приложения</value>
  </data>
  <data name="OutdatedMobileAppVersion" xml:space="preserve">
    <value>Устаревшая версия моб.приложения</value>
  </data>
  <data name="StartEvent" xml:space="preserve">
    <value>Начало</value>
  </data>
  <data name="EndEvent" xml:space="preserve">
    <value>Конец</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>Весь день</value>
  </data>
  <data name="_2Weeks" xml:space="preserve">
    <value>2 недели</value>
  </data>
  <data name="AppoController_Bookable_BlockDayForBooking" xml:space="preserve">
    <value>Бронирование запрещено</value>
  </data>
  <data name="AreYouSureToDeleteThisEvent" xml:space="preserve">
    <value>Вы уверены, что хотите удалить событие?</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>Секундочку..</value>
  </data>
  <data name="EventCard" xml:space="preserve">
    <value>Карточка события</value>
  </data>
  <data name="Confirned" xml:space="preserve">
    <value>подтверждено</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>требуется подтверждение</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Объект</value>
  </data>
  <data name="ServicesCategories" xml:space="preserve">
    <value>Категории услуг</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Услуги</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Услуга</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Клиент</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Подробности</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Полное имя</value>
  </data>
  <data name="MapX" xml:space="preserve">
    <value>GPS координата X</value>
  </data>
  <data name="KeyHint" xml:space="preserve">
    <value>Обязательный уникальный ключ, для использования в мобильном приложении.</value>
  </data>
  <data name="ReservedField" xml:space="preserve">
    <value>Не используется</value>
  </data>
  <data name="DbNews_UrlProductCodeEtc" xml:space="preserve">
    <value>Ссылка, или код продукта (поле "ключ") итп..</value>
  </data>
  <data name="DbNews_WhatToDoWhenNewsFrameIsClickedInApp" xml:space="preserve">
    <value>Возможное действие при клике по новости</value>
  </data>
  <data name="DbNews_NewsText" xml:space="preserve">
    <value>Текст новости</value>
  </data>
  <data name="DbNews_LanguageAreaTheNewsWillBeShownIn" xml:space="preserve">
    <value>Языковой регион, для которого предназначена новость</value>
  </data>
  <data name="DbNews_ImageToBeShownInTheNews" xml:space="preserve">
    <value>Картинка новостей</value>
  </data>
  <data name="InternationalTitlesLanguage" xml:space="preserve">
    <value>Язык для международных названий</value>
  </data>
  <data name="PriorityDesc" xml:space="preserve">
    <value>Чем выше приоритет, тем выше в списке будет показана запись</value>
  </data>
  <data name="EnabledModules" xml:space="preserve">
    <value>Включенные модули</value>
  </data>
  <data name="NeedAllUsersRelog" xml:space="preserve">
    <value>Разлогинить пользователей</value>
  </data>
  <data name="NeedAllUsersRelogDesc" xml:space="preserve">
    <value>Заставить всех пользователей данного клиента панели управления перелогиниться, чтобы визуальные изменения вступили в силу.</value>
  </data>
  <data name="HowToUse" xml:space="preserve">
    <value>Как использовать</value>
  </data>
  <data name="RefCodeDesc" xml:space="preserve">
    <value>Артикул</value>
  </data>
  <data name="TargetPlatfrom" xml:space="preserve">
    <value>Платформа</value>
  </data>
  <data name="TitleDesc" xml:space="preserve">
    <value>Показываемый заголовок</value>
  </data>
  <data name="MessageTextDesc" xml:space="preserve">
    <value>Текст сообщения</value>
  </data>
  <data name="TargetSegment" xml:space="preserve">
    <value>Получатели</value>
  </data>
  <data name="TenantNameDesc" xml:space="preserve">
    <value>Имя клиента в панели управления</value>
  </data>
  <data name="NewRecord" xml:space="preserve">
    <value>Новая запись</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Цвет</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Цена</value>
  </data>
  <data name="Bookable" xml:space="preserve">
    <value>Предложить для записи</value>
  </data>
  <data name="BookableHint" xml:space="preserve">
    <value>Запись будет видна в клиентам в приложении для записи</value>
  </data>
  <data name="LinkedServicesHint" xml:space="preserve">
    <value>Связанные услуги, которые будут предложены для бронирования в приложении</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Рабочие дни</value>
  </data>
  <data name="WorkingTimeStart" xml:space="preserve">
    <value>Рабочие часы с</value>
  </data>
  <data name="WorkingTimeEnd" xml:space="preserve">
    <value>Рабочие часы до</value>
  </data>
  <data name="LandingForClients" xml:space="preserve">
    <value>Если вы клиент, то</value>
  </data>
  <data name="LandingEnterHere" xml:space="preserve">
    <value>вход здесь</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Команды</value>
  </data>
  <data name="Goalkeepers" xml:space="preserve">
    <value>Вратари</value>
  </data>
  <data name="EditEvent" xml:space="preserve">
    <value>Редактирование события</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Тренеры</value>
  </data>
  <data name="Since" xml:space="preserve">
    <value>Дата основания</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Рейтинг</value>
  </data>
  <data name="PlayersLvl" xml:space="preserve">
    <value>Уровень игроков</value>
  </data>
  <data name="SortRating" xml:space="preserve">
    <value>по рейтингу</value>
  </data>
  <data name="Arenas" xml:space="preserve">
    <value>Спортивные площадки</value>
  </data>
  <data name="Sport" xml:space="preserve">
    <value>Спорт</value>
  </data>
  <data name="VK" xml:space="preserve">
    <value>ВКонтакте</value>
  </data>
  <data name="ArenaFeatures" xml:space="preserve">
    <value>Свойства площадок</value>
  </data>
  <data name="ArenaFeaturesDesc" xml:space="preserve">
    <value>Доп. инфо</value>
  </data>
  <data name="Practices" xml:space="preserve">
    <value>Тренировки</value>
  </data>
  <data name="Practice" xml:space="preserve">
    <value>Тренировка</value>
  </data>
  <data name="PracticeElements" xml:space="preserve">
    <value>Элементы тренировки</value>
  </data>
  <data name="DifficultyLevel" xml:space="preserve">
    <value>Уровень сложности</value>
  </data>
  <data name="PriceDetailsDesc" xml:space="preserve">
    <value>"за час" итп..</value>
  </data>
  <data name="PriceDetails" xml:space="preserve">
    <value>Детали стоимости</value>
  </data>
  <data name="WeekDays" xml:space="preserve">
    <value>Дни недели</value>
  </data>
  <data name="GenerateDropDowns_Unknown" xml:space="preserve">
    <value>Не известно</value>
  </data>
  <data name="SexRestriction" xml:space="preserve">
    <value>Ограничение по полу</value>
  </data>
  <data name="MenOnly" xml:space="preserve">
    <value>Только мужчины</value>
  </data>
  <data name="WomenOnly" xml:space="preserve">
    <value>Только женщины</value>
  </data>
  <data name="TimeStart" xml:space="preserve">
    <value>Время начала</value>
  </data>
  <data name="TimeEnd" xml:space="preserve">
    <value>Время окончания</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Команда</value>
  </data>
  <data name="Arena" xml:space="preserve">
    <value>Спортивная площадка</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>Детали мероприятия</value>
  </data>
  <data name="_Empty" xml:space="preserve">
    <value> </value>
  </data>
  <data name="ArenasShort" xml:space="preserve">
    <value>Площадки</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>События</value>
  </data>
  <data name="EventsElements" xml:space="preserve">
    <value>Элементы событий</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>Организации</value>
  </data>
  <data name="Organization" xml:space="preserve">
    <value>Организация</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>Тип события</value>
  </data>
  <data name="BaseControllerContent_GenerateDropDowns_Rally" xml:space="preserve">
    <value>Сборы</value>
  </data>
  <data name="Championship" xml:space="preserve">
    <value>Соревнование</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Другое</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Тип расписания</value>
  </data>
  <data name="ByDaysOfWeek" xml:space="preserve">
    <value>По дням недели</value>
  </data>
  <data name="WithFixedDate" xml:space="preserve">
    <value>С фиксированными датами</value>
  </data>
  <data name="ScheduleTypeDesc" xml:space="preserve">
    <value>Если с фиксированными датами, то дни недели не используются и наоборот.</value>
  </data>
  <data name="Schedules" xml:space="preserve">
    <value>Расписания</value>
  </data>
  <data name="NeedRelogUser" xml:space="preserve">
    <value>Разлогинить пользователя</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Дата рождения</value>
  </data>
  <data name="ValidUsernameRequired" xml:space="preserve">
    <value>Требуется нормальное имя пользователя</value>
  </data>
  <data name="WorkingTimePauseEnd" xml:space="preserve">
    <value>Перерыв до</value>
  </data>
  <data name="WorkingTimePauseStart" xml:space="preserve">
    <value>Перерыв с</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Рабочие часы</value>
  </data>
  <data name="BookingStatus_Unknown" xml:space="preserve">
    <value>Не установлено</value>
  </data>
  <data name="BookingStatus_Pending" xml:space="preserve">
    <value>Ожидает подтверждения</value>
  </data>
  <data name="BookingStatus_Confirmed" xml:space="preserve">
    <value>Подтверждено</value>
  </data>
  <data name="BookingStatus_Rejected" xml:space="preserve">
    <value>Отклонено</value>
  </data>
  <data name="BookingStatus_Archived" xml:space="preserve">
    <value>В архиве</value>
  </data>
  <data name="BookingRequest" xml:space="preserve">
    <value>Запрос на бронирование</value>
  </data>
  <data name="DaysOfWeek_Monday" xml:space="preserve">
    <value>Понедельник</value>
  </data>
  <data name="DaysOfWeek_Sunday" xml:space="preserve">
    <value>Воскресенье</value>
  </data>
  <data name="DaysOfWeek_Saturday" xml:space="preserve">
    <value>Суббота</value>
  </data>
  <data name="DaysOfWeek_Friday" xml:space="preserve">
    <value>Пятница</value>
  </data>
  <data name="DaysOfWeek_Thursday" xml:space="preserve">
    <value>Четверг</value>
  </data>
  <data name="DaysOfWeek_Tuesday" xml:space="preserve">
    <value>Вторник</value>
  </data>
  <data name="DaysOfWeek_Wednesday" xml:space="preserve">
    <value>Среда</value>
  </data>
  <data name="WorkingTimeDetailed" xml:space="preserve">
    <value>Рабочие часы детально</value>
  </data>
  <data name="AppoConfirmAuto" xml:space="preserve">
    <value>Автоподтверждение бронирований</value>
  </data>
  <data name="AppoConfirmAutoDesc" xml:space="preserve">
    <value>Автоматически подтверждать бронирования на основании исходных данных</value>
  </data>
  <data name="AppoExplicitBookableDesc" xml:space="preserve">
    <value>Явно указывать доступное для бронирования время для каждого объекта</value>
  </data>
  <data name="AppoExplicitBookable" xml:space="preserve">
    <value>Требуется указание доступного времени для бронирования</value>
  </data>
  <data name="btnBook" xml:space="preserve">
    <value>Запись</value>
  </data>
  <data name="Gallery" xml:space="preserve">
    <value>Галерея</value>
  </data>
  <data name="YourName" xml:space="preserve">
    <value>Ваше имя</value>
  </data>
  <data name="BtnBookNow" xml:space="preserve">
    <value>Записаться!</value>
  </data>
  <data name="BookOnline" xml:space="preserve">
    <value>Онлайн-запись</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Назад</value>
  </data>
  <data name="NameTitle" xml:space="preserve">
    <value>Обращение</value>
  </data>
  <data name="YourFName" xml:space="preserve">
    <value>Ваше имя</value>
  </data>
  <data name="YourLName" xml:space="preserve">
    <value>Фамилия</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>Строка</value>
  </data>
  <data name="UpdatingData" xml:space="preserve">
    <value>Обновление данных..</value>
  </data>
  <data name="AppoNoTimeDesc" xml:space="preserve">
    <value>Для заданных условий нет доступного времени. Попробуйте изменить условия ниже:</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>Ой!</value>
  </data>
  <data name="Canceled" xml:space="preserve">
    <value>Отменено</value>
  </data>
  <data name="ClientId" xml:space="preserve">
    <value>Клиентский ID</value>
  </data>
  <data name="AppoTimeDescWho" xml:space="preserve">
    <value>{0} ждет вас в {1}</value>
  </data>
  <data name="BookingDateTimeDescFormat" xml:space="preserve">
    <value>ждем вас в {0}</value>
  </data>
  <data name="AppoTimeDescPending" xml:space="preserve">
    <value>Ожидайте подтверждения на {0}</value>
  </data>
  <data name="ConfirmationPendingTitle" xml:space="preserve">
    <value>Подтверждение ожидается</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Изображение</value>
  </data>
  <data name="PatternUrl" xml:space="preserve">
    <value>PatternUrl</value>
  </data>
  <data name="WallpaperUrl" xml:space="preserve">
    <value>WallpaperUrl</value>
  </data>
  <data name="ControlPanel" xml:space="preserve">
    <value>Панель управления</value>
  </data>
  <data name="AppStrings" xml:space="preserve">
    <value>Тексты</value>
  </data>
  <data name="TweakApp" xml:space="preserve">
    <value>Моб. приложение</value>
  </data>
  <data name="NoTimeAvailable" xml:space="preserve">
    <value>Нет доступного времени</value>
  </data>
  <data name="ForBookingOnly" xml:space="preserve">
    <value>Только для онлайн-записи</value>
  </data>
  <data name="Sections" xml:space="preserve">
    <value>Разделы</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>Статья</value>
  </data>
  <data name="SeeAlso" xml:space="preserve">
    <value>Смотрите также: </value>
  </data>
  <data name="PriceMask" xml:space="preserve">
    <value>Маска вывода цен</value>
  </data>
  <data name="Appearence" xml:space="preserve">
    <value>Оформление</value>
  </data>
  <data name="SortNotes" xml:space="preserve">
    <value>По заметкам</value>
  </data>
  <data name="OurContacts" xml:space="preserve">
    <value>Наши контакты</value>
  </data>
  <data name="HowToGet" xml:space="preserve">
    <value>Как добраться</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>Назад</value>
  </data>
  <data name="BookingObjectsShort" xml:space="preserve">
    <value>Объекты</value>
  </data>
  <data name="ExplainDate_Today" xml:space="preserve">
    <value>Сегодня</value>
  </data>
  <data name="ExplainDate_Tomm" xml:space="preserve">
    <value>Завтра</value>
  </data>
  <data name="ExplainDate_X" xml:space="preserve">
    <value>Через {0} дней</value>
  </data>
  <data name="ExplainDate_X1" xml:space="preserve">
    <value>Через {0} день</value>
  </data>
  <data name="ExplainDate_X2" xml:space="preserve">
    <value>Через {0} дня</value>
  </data>
  <data name="Authenticating" xml:space="preserve">
    <value>Аутентификация..</value>
  </data>
  <data name="YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins" xml:space="preserve">
    <value>Вы пробовали слишком много раз, повторите попытку через {0} мин.</value>
  </data>
  <data name="RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater" xml:space="preserve">
    <value>Регистрация не удалась. Проверьте, что вы указали действительный номер телефона или повторите попытку позже.</value>
  </data>
  <data name="ПроверьтеКорректностьВведенныхДанных" xml:space="preserve">
    <value>Проверьте корректность введенных данных.</value>
  </data>
  <data name="BookingFailed" xml:space="preserve">
    <value>Запись не удалась.</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>Проверка кода...</value>
  </data>
  <data name="WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking" xml:space="preserve">
    <value>Мы отправили вам код подтверждения по SMS. Пожалуйста, введите его ниже для оформления бронирования:</value>
  </data>
  <data name="BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry" xml:space="preserve">
    <value>Бронирование не удалось. Возможно, кто-то уже занял это время, повторите попытку</value>
  </data>
  <data name="FailedToVerifyCode" xml:space="preserve">
    <value>Не удалось подтвердить данный код.</value>
  </data>
  <data name="ReloadingBookingData" xml:space="preserve">
    <value>Перезагрузка данных..</value>
  </data>
  <data name="BookingDateTimeDesc" xml:space="preserve">
    <value>{0} в {1}</value>
  </data>
  <data name="CodeFromSMS" xml:space="preserve">
    <value>Код из SMS</value>
  </data>
  <data name="BookingFrontDeskStatusType_Confirmed" xml:space="preserve">
    <value>Подтверждено</value>
  </data>
  <data name="BookingFrontDeskStatusType_Canceled" xml:space="preserve">
    <value>Отменено</value>
  </data>
  <data name="BookingFrontDeskStatusType_Pending" xml:space="preserve">
    <value>Ожидает подтверждения</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>Выбор языка</value>
  </data>
  <data name="ClickToUploadOrDropFileHere" xml:space="preserve">
    <value>Нажмите или перетащите файл в это поле..</value>
  </data>
  <data name="LoadingOriginalImage" xml:space="preserve">
    <value>Загружаю оригинал..</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>Открыть</value>
  </data>
  <data name="WithoutDescription" xml:space="preserve">
    <value>Без описания.</value>
  </data>
  <data name="Galleries" xml:space="preserve">
    <value>Галереи</value>
  </data>
  <data name="SystemNameHint" xml:space="preserve">
    <value>Не отображается в мобильном приложении, служебное имя, используемое для выбора этого элемента в списках и т.д.</value>
  </data>
  <data name="ExplainDateWithInterval" xml:space="preserve">
    <value>Ждем вас {0}</value>
  </data>
  <data name="BookingTimeDescAt" xml:space="preserve">
    <value>В {0}</value>
  </data>
  <data name="Blog" xml:space="preserve">
    <value>Статьи</value>
  </data>
  <data name="OpenBlogArticle" xml:space="preserve">
    <value>Открыть статью</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>Время выпуска</value>
  </data>
  <data name="SplashLogo" xml:space="preserve">
    <value>Лого заставки</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Лого компании</value>
  </data>
  <data name="DisplayedOverOurContacts" xml:space="preserve">
    <value>Показывается над Наши Контакты</value>
  </data>
  <data name="SortByStatus" xml:space="preserve">
    <value>По состоянию</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Выход</value>
  </data>
  <data name="PromoStatus_Other" xml:space="preserve">
    <value>Другое</value>
  </data>
  <data name="PromoStatus_Incoming" xml:space="preserve">
    <value>Планируется</value>
  </data>
  <data name="PromoStatus_Closed" xml:space="preserve">
    <value>Закрыто</value>
  </data>
  <data name="PromoStatus_Open" xml:space="preserve">
    <value>Активно</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Состояние</value>
  </data>
  <data name="PromoAction" xml:space="preserve">
    <value>Акция</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Скидка</value>
  </data>
  <data name="CorrectAnswersPercent" xml:space="preserve">
    <value>Процент правильных ответов</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Бренд</value>
  </data>
  <data name="PleaseSaveThisRecordToBeAbleToAddSubRecords" xml:space="preserve">
    <value>Сохраните текущую запись, чтобы иметь возможность добавить к ней дочерние.</value>
  </data>
  <data name="SearchKeywords" xml:space="preserve">
    <value>Ключевые слова для поиска этого элемента</value>
  </data>
  <data name="ExcludeQuestionsWithTags" xml:space="preserve">
    <value>Исключить с тэгами</value>
  </data>
  <data name="IncludeQuestionsWithTags" xml:space="preserve">
    <value>Включить с тэгами</value>
  </data>
  <data name="PromoActons" xml:space="preserve">
    <value>Акции</value>
  </data>
  <data name="Brands" xml:space="preserve">
    <value>Бренды</value>
  </data>
  <data name="QRCodeImageUrl" xml:space="preserve">
    <value>QR-Код ссылка на изображение</value>
  </data>
  <data name="SortByLevel" xml:space="preserve">
    <value>По сложности</value>
  </data>
  <data name="Correct" xml:space="preserve">
    <value>Правильно</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Ответ</value>
  </data>
  <data name="Answers" xml:space="preserve">
    <value>Ответы</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Уровень</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Вопрос</value>
  </data>
  <data name="MenuPageAbout" xml:space="preserve">
    <value>О программе</value>
  </data>
  <data name="MenuPageContacts" xml:space="preserve">
    <value>Контакты</value>
  </data>
  <data name="MenuPageNews" xml:space="preserve">
    <value>Новости</value>
  </data>
  <data name="MenuPageSalons" xml:space="preserve">
    <value>Список центров</value>
  </data>
  <data name="PageNewsTitle" xml:space="preserve">
    <value>Последние новости</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value> </value>
  </data>
  <data name="ButtonRegionChange" xml:space="preserve">
    <value>Сменить регион</value>
  </data>
  <data name="ButtonNavigate" xml:space="preserve">
    <value>Маршрут</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Избранное</value>
  </data>
  <data name="ErrorConnRegions" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, повторите позднее.</value>
  </data>
  <data name="ErrorConnSalons" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, повторите позднее.</value>
  </data>
  <data name="ErrorConnNews" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, повторите позднее.</value>
  </data>
  <data name="ErrorConnection" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, проверьте ваши настройки подключения к интернету и попробуйте еще раз.</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="FavoriteEmpty1" xml:space="preserve">
    <value>Добро пожаловать!</value>
  </data>
  <data name="NeedInternet" xml:space="preserve">
    <value>При загрузке данных произошла ошибка.
Проверьте ваше подключение к сети.</value>
  </data>
  <data name="ErrorCannotNavigate" xml:space="preserve">
    <value>Необходима внешняя программа для навигации.</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Загрузка...</value>
  </data>
  <data name="BrowseSite" xml:space="preserve">
    <value>Вебсайт</value>
  </data>
  <data name="ShowOnMap" xml:space="preserve">
    <value>Мы на карте</value>
  </data>
  <data name="Call" xml:space="preserve">
    <value>Позвонить</value>
  </data>
  <data name="SaveItToFavorites" xml:space="preserve">
    <value>Сохраните его для быстрого доступа!</value>
  </data>
  <data name="ButtonAddToFavs" xml:space="preserve">
    <value>Добавить в избранное</value>
  </data>
  <data name="ButtonConnect" xml:space="preserve">
    <value>Повторить попытку</value>
  </data>
  <data name="ButtonHowToGetToUs" xml:space="preserve">
    <value>Как найти</value>
  </data>
  <data name="AreYouSureRemoveFromFavs" xml:space="preserve">
    <value>Действительно удалить центр из избранного?</value>
  </data>
  <data name="RemoveFromFavs" xml:space="preserve">
    <value>Убрать из избранного</value>
  </data>
  <data name="FavDescBlabla" xml:space="preserve">
    <value>Теперь у вас есть быстрый доступ к данным этого центра из вкладки Избранное.</value>
  </data>
  <data name="AboutSalon" xml:space="preserve">
    <value>О центре</value>
  </data>
  <data name="GPSPermissionsNeedOn" xml:space="preserve">
    <value>Программе необходим доступ к вашей геопозиции, чтобы помочь нас найти. Включить доступ сейчас?</value>
  </data>
  <data name="GPSPleaseTurnOn" xml:space="preserve">
    <value>Ваш GPS отключен, пожалуйста, включите его, чтобы мы могли помочь вам с гео-поиском.</value>
  </data>
  <data name="HowToGetThereMetroTitle" xml:space="preserve">
    <value>На метро:</value>
  </data>
  <data name="PageContactsInfo" xml:space="preserve">
    <value>Информация</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Свяжитесь с нами</value>
  </data>
  <data name="WeOnMap" xml:space="preserve">
    <value>Мы на карте</value>
  </data>
  <data name="DevCopyright" xml:space="preserve">
    <value>Работает на платформе AppoMobi © 2018</value>
  </data>
  <data name="GettingGPSCoords" xml:space="preserve">
    <value>Определяем местоположение..</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="ButtonProSalons" xml:space="preserve">
    <value>Салонам</value>
  </data>
  <data name="ButtonProPpl" xml:space="preserve">
    <value>Специалистам</value>
  </data>
  <data name="ButtonProPartners" xml:space="preserve">
    <value>Вход для партнеров</value>
  </data>
  <data name="PageHowToGetThereInstructions" xml:space="preserve">
    <value>Как нас найти</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="NavigateTo" xml:space="preserve">
    <value>Проложить маршрут</value>
  </data>
  <data name="FavReplaceConfirm" xml:space="preserve">
    <value>Заменить избранный центр на этот?</value>
  </data>
  <data name="ToSalonList" xml:space="preserve">
    <value>К списку центров</value>
  </data>
  <data name="km" xml:space="preserve">
    <value> км</value>
  </data>
  <data name="ButtonFindYourSalon" xml:space="preserve">
    <value>Найти свой салон</value>
  </data>
  <data name="FavGratz" xml:space="preserve">
    <value>Поздравляем!</value>
  </data>
  <data name="ButtonGotIt" xml:space="preserve">
    <value>Отлично</value>
  </data>
  <data name="ErrorTitle" xml:space="preserve">
    <value>Ошибка</value>
  </data>
  <data name="ErrorConSalon" xml:space="preserve">
    <value>Ошибка связи. Пожалуйста, повторите позднее.</value>
  </data>
  <data name="MyFav_iOS" xml:space="preserve">
    <value>Мой салон</value>
  </data>
  <data name="iSalonList" xml:space="preserve">
    <value>Центры</value>
  </data>
  <data name="iAboutUs" xml:space="preserve">
    <value>Контакты</value>
  </data>
  <data name="iRegion" xml:space="preserve">
    <value>На карте</value>
  </data>
  <data name="PageTitleSettings" xml:space="preserve">
    <value>Настройки</value>
  </data>
  <data name="SettingsInterface" xml:space="preserve">
    <value>Внешний вид</value>
  </data>
  <data name="SettingsShowTabbedTitles" xml:space="preserve">
    <value>Нижнее меню без текста</value>
  </data>
  <data name="SettingsStartFav" xml:space="preserve">
    <value>Показывать страницу Мой салон при запуске</value>
  </data>
  <data name="MenuPageHome" xml:space="preserve">
    <value>Начальная страница</value>
  </data>
  <data name="SettingsAnimation" xml:space="preserve">
    <value>Отключить фоновую анимацию для экономии энергии</value>
  </data>
  <data name="BackToSalonList" xml:space="preserve">
    <value>Обратно к списку</value>
  </data>
  <data name="SettingsTutorial" xml:space="preserve">
    <value>Показывать слайды приветствия при запуске программы</value>
  </data>
  <data name="MenuSomeMore" xml:space="preserve">
    <value>А ещё..</value>
  </data>
  <data name="ShowWelcomeSlides" xml:space="preserve">
    <value>Посмотреть заставку</value>
  </data>
  <data name="StartUp" xml:space="preserve">
    <value>НАЧАТЬ</value>
  </data>
  <data name="UpdateNeded" xml:space="preserve">
    <value>Вышла новая версия программы, пожалуйста, обновитесь!</value>
  </data>
  <data name="Bye" xml:space="preserve">
    <value>До свидания!</value>
  </data>
  <data name="SilentPush" xml:space="preserve">
    <value>Беззвучные push-уведомления</value>
  </data>
  <data name="AskHideWelcome" xml:space="preserve">
    <value>Скрыть это приветствие?</value>
  </data>
  <data name="WebBack" xml:space="preserve">
    <value>Назад</value>
  </data>
  <data name="SortAbc" xml:space="preserve">
    <value>По алфавиту</value>
  </data>
  <data name="SortKm" xml:space="preserve">
    <value>По расстоянию</value>
  </data>
  <data name="OnMapSalon" xml:space="preserve">
    <value>На карте</value>
  </data>
  <data name="PageSettings_PageSettings_Version" xml:space="preserve">
    <value>версия</value>
  </data>
  <data name="MenuProducts" xml:space="preserve">
    <value>Каталог продукции</value>
  </data>
  <data name="SubCatsHere" xml:space="preserve">
    <value>Смотрите подкатегории:</value>
  </data>
  <data name="AllProductsHere" xml:space="preserve">
    <value>Вся продукция из раздела</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>Результаты поиска</value>
  </data>
  <data name="TapToRead" xml:space="preserve">
    <value>читать</value>
  </data>
  <data name="SearchProd" xml:space="preserve">
    <value>Поиск продукции</value>
  </data>
  <data name="EnterString" xml:space="preserve">
    <value>Поиск</value>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>ПОПУЛЯРНОЕ</value>
  </data>
  <data name="YouHaveSearched" xml:space="preserve">
    <value>Вы искали</value>
  </data>
  <data name="PleaseEnterMoreCharacters" xml:space="preserve">
    <value>Введите большее количество букв!</value>
  </data>
  <data name="SearchSalonLabel" xml:space="preserve">
    <value>Поиск центров</value>
  </data>
  <data name="BtnAppSettings" xml:space="preserve">
    <value>Системные настройки</value>
  </data>
  <data name="ButtonLater" xml:space="preserve">
    <value>Позже</value>
  </data>
  <data name="GPSBtn_TurnGPSOn" xml:space="preserve">
    <value>Включить</value>
  </data>
  <data name="SliderAnnounce" xml:space="preserve">
    <value>НЕ ПРОПУСТИТЕ</value>
  </data>
  <data name="AboutTheCompany" xml:space="preserve">
    <value>О компании</value>
  </data>
  <data name="AskForConfirmationWhenRemovingItemFromWishList" xml:space="preserve">
    <value>Подтверждать удаления из списков</value>
  </data>
  <data name="OtherCategories" xml:space="preserve">
    <value>ДРУГИЕ КАТЕГОРИИ</value>
  </data>
  <data name="GotoProducts" xml:space="preserve">
    <value>Перейти в каталог</value>
  </data>
  <data name="Поделиться" xml:space="preserve">
    <value>Поделиться</value>
  </data>
  <data name="MenuProductsShort" xml:space="preserve">
    <value>Продукция</value>
  </data>
  <data name="MySalonTab" xml:space="preserve">
    <value>Мой салон</value>
  </data>
  <data name="INTHECATEGORY" xml:space="preserve">
    <value>ПЕРЕЙТИ В КАТЕГОРИЮ</value>
  </data>
  <data name="CardProductFull_SetupCell_Ref" xml:space="preserve">
    <value>Артикул</value>
  </data>
  <data name="PageWishList_UpdateFavs_ToCatalogue" xml:space="preserve">
    <value>В каталог</value>
  </data>
  <data name="PageWishList_OnBtnShare_МойСписокЖеланийTHALION" xml:space="preserve">
    <value>Мой список желаний</value>
  </data>
  <data name="ClearList" xml:space="preserve">
    <value>Очистить список</value>
  </data>
  <data name="WhereToBuy" xml:space="preserve">
    <value>Где найти</value>
  </data>
  <data name="ContactUs2" xml:space="preserve">
    <value>Связаться с нами</value>
  </data>
  <data name="CardProductFull_Fav_OnDown_ConfirmFavDelete" xml:space="preserve">
    <value>Удалить из списка желаний?</value>
  </data>
  <data name="PageWishList_OnBtnClearList_ConfirmClearList" xml:space="preserve">
    <value>Вы уверены, что хотите очистить ваш Список желаний?</value>
  </data>
  <data name="GPSPleaseTurnOniOS" xml:space="preserve">
    <value>Чтобы рассчитать расстояние до нас, нам потребуются ваши координаты.</value>
  </data>
  <data name="NumDesc_Items_Format" xml:space="preserve">
    <value>В вашем списке {0} {1}.</value>
  </data>
  <data name="NumDesc_Items_0" xml:space="preserve">
    <value>продуктов</value>
  </data>
  <data name="NumDesc_Items_1" xml:space="preserve">
    <value>продукт</value>
  </data>
  <data name="NumDesc_Items_with1" xml:space="preserve">
    <value>продукт</value>
  </data>
  <data name="NumDesc_Items_with2" xml:space="preserve">
    <value>продукта</value>
  </data>
  <data name="NumDesc_Items_with0" xml:space="preserve">
    <value>продуктов</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>Узнать больше</value>
  </data>
  <data name="ItemAddedToWishList" xml:space="preserve">
    <value>Добавлено в список желаний</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Нажмите еще раз для выхода из программы</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Сброс</value>
  </data>
  <data name="CatRoot" xml:space="preserve">
    <value>Весь каталог..</value>
  </data>
  <data name="ToCatRoot" xml:space="preserve">
    <value>В оглавление</value>
  </data>
  <data name="iOSTabsStartup_Setup_WhereToFind" xml:space="preserve">
    <value>Где найти</value>
  </data>
  <data name="PrevCategory" xml:space="preserve">
    <value>Влево</value>
  </data>
  <data name="NextCategory" xml:space="preserve">
    <value>А еще..</value>
  </data>
  <data name="SeaAlso" xml:space="preserve">
    <value>СМОТРИТЕ ТАКЖЕ</value>
  </data>
  <data name="BackToCatalog" xml:space="preserve">
    <value>Каталог продукции</value>
  </data>
  <data name="MyWishList" xml:space="preserve">
    <value>Мой список желаний</value>
  </data>
  <data name="iOSTabsStartup_Setup_Favorites" xml:space="preserve">
    <value>Избранное</value>
  </data>
  <data name="iOSTabsStartup_Setup_MyPreferences" xml:space="preserve">
    <value>Мои предпочтения</value>
  </data>
  <data name="DoYouWantUsToGPS" xml:space="preserve">
    <value>Если вы хотите, чтобы мы могли найти ближайшие к вам центры THALION,  ответьте положительно в следующем окне.</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Привет</value>
  </data>
  <data name="btnTryAgain" xml:space="preserve">
    <value>Подключиться</value>
  </data>
  <data name="btnCheckSettings" xml:space="preserve">
    <value>Проверить настройки</value>
  </data>
  <data name="ProcessingYourBooking" xml:space="preserve">
    <value>Обработка заказа ..</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Имя</value>
  </data>
  <data name="AppoObjectType_Default" xml:space="preserve">
    <value>Обычный</value>
  </data>
  <data name="AppoObjectType_Blocked" xml:space="preserve">
    <value>Блокировка</value>
  </data>
  <data name="AppoObjectType_Any" xml:space="preserve">
    <value>Джокер</value>
  </data>
  <data name="AppoObjectType_System" xml:space="preserve">
    <value>Другое</value>
  </data>
  <data name="LegalTerms" xml:space="preserve">
    <value>Право</value>
  </data>
  <data name="AllSortedByDistance" xml:space="preserve">
    <value>Все по расстоянию</value>
  </data>
  <data name="HintCentersListGpsBtn" xml:space="preserve">
    <value>Рассчитайте расстояние до центров и сортируйте их с помощью этой кнопки</value>
  </data>
  <data name="OpenItemNotFound" xml:space="preserve">
    <value>Данные не найдены в вашем регионе.</value>
  </data>
  <data name="Auth_LoginUsingPhone" xml:space="preserve">
    <value>Войдите с вашим номером телефона</value>
  </data>
  <data name="Auth_LogoffConfirmation" xml:space="preserve">
    <value>Выйти из учетной записи? Вы сможете войти в нее снова позднее на любом устройстве, иcпользуя ту же учетную запись.</value>
  </data>
  <data name="Auth_EnterSmsCodeTitle" xml:space="preserve">
    <value>Мы отправили SMS-пароль на ваш телефон {0}</value>
  </data>
  <data name="Auth_MyProfile" xml:space="preserve">
    <value>Мой профиль</value>
  </data>
  <data name="Auth_PersonalData" xml:space="preserve">
    <value>Личные данные</value>
  </data>
  <data name="Settings_ChooseYourTabsMinMax" xml:space="preserve">
    <value>Выберите вкладки  ({0}/{1} минимум {2})</value>
  </data>
  <data name="Settings_Copyright" xml:space="preserve">
    <value>© 2019 AppoMobi и правообладатели</value>
  </data>
  <data name="Settings_FavsTabs" xml:space="preserve">
    <value>Выбор элементов нижнего меню</value>
  </data>
  <data name="Settings_NoTitlesInTabs" xml:space="preserve">
    <value>Нижнее меню без текста</value>
  </data>
  <data name="Settings_SelectLanguage" xml:space="preserve">
    <value>Выбор языка</value>
  </data>
  <data name="Settings_SelectTheme" xml:space="preserve">
    <value>Тема оформления</value>
  </data>
  <data name="Settings_SilentPush" xml:space="preserve">
    <value>Беззвучные push-уведомления</value>
  </data>
  <data name="Auth_DidnTGetTheCode" xml:space="preserve">
    <value>Не получили код?</value>
  </data>
  <data name="Auth_WeHaveSentYouAnotherSMS" xml:space="preserve">
    <value>Мы отправили вам еще одно SMS, пожалуйста, проверьте ваши сообщения.</value>
  </data>
  <data name="GPSNeedHighAccuracy" xml:space="preserve">
    <value>Нам не удалось получить вашу позицию GPS. Пожалуйста, включите высокую точность (Использовать GPS, Wi-Fi, Bluetooth или сотовые сети, чтобы определить местоположение) в настройках GPS.</value>
  </data>
  <data name="OpenProdCatInApp" xml:space="preserve">
    <value>Открыть категорию товаров</value>
  </data>
  <data name="CallUs" xml:space="preserve">
    <value>Позвонить нам</value>
  </data>
  <data name="OrdersPayed" xml:space="preserve">
    <value>Оплачено заказов</value>
  </data>
  <data name="OrdersTotal" xml:space="preserve">
    <value>Всего заказов</value>
  </data>
  <data name="LastSeenTime" xml:space="preserve">
    <value>Последний коннект</value>
  </data>
  <data name="CachePaymentsProhibited" xml:space="preserve">
    <value>Оплата наличными запрещена</value>
  </data>
  <data name="CachPaymentsAutoAuthorized" xml:space="preserve">
    <value>Автоподтверждение запроса оплаты наличными</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Заказ</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Заказы</value>
  </data>
  <data name="DeliveryStatus" xml:space="preserve">
    <value>Статус исполнения</value>
  </data>
  <data name="PaymentStatus" xml:space="preserve">
    <value>Статус оплаты</value>
  </data>
  <data name="Goods" xml:space="preserve">
    <value>Товары и услуги</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Позиция</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Количество</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Номер</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>Способ платежа</value>
  </data>
  <data name="CustomerOrderNotes" xml:space="preserve">
    <value>Комментарий клиента</value>
  </data>
  <data name="Payments" xml:space="preserve">
    <value>Платежи</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>Оплата</value>
  </data>
  <data name="SeatsTaken" xml:space="preserve">
    <value>Занято мест</value>
  </data>
  <data name="PublicNotes" xml:space="preserve">
    <value>Публичные заметки</value>
  </data>
  <data name="PublicNotesDesc" xml:space="preserve">
    <value>Видны клиентам в мобильном приложении</value>
  </data>
  <data name="MinTimeToBook" xml:space="preserve">
    <value>Минимальное время</value>
  </data>
  <data name="MinTimeToBookDesc" xml:space="preserve">
    <value>Минимальное время мероприятия для бронирования</value>
  </data>
  <data name="AppoColorDesc" xml:space="preserve">
    <value>Цвет для отображения в календаре</value>
  </data>
  <data name="MaxSeatsDesc" xml:space="preserve">
    <value>Максимум мест для брони в туре. Если 0, то не ограничено.</value>
  </data>
  <data name="MaxSeats" xml:space="preserve">
    <value>Мест</value>
  </data>
  <data name="Popularity" xml:space="preserve">
    <value>Популярность</value>
  </data>
  <data name="PopularityDesc" xml:space="preserve">
    <value>Заказано мест за все время</value>
  </data>
  <data name="Reconnect" xml:space="preserve">
    <value>Подключиться</value>
  </data>
  <data name="Duration" xml:space="preserve">
    <value>Длительность</value>
  </data>
  <data name="CurrenciesConversions" xml:space="preserve">
    <value>Курсы валют</value>
  </data>
  <data name="Conversion" xml:space="preserve">
    <value>Конвертация</value>
  </data>
  <data name="X_NotesFromCustomer" xml:space="preserve">
    <value>Сообщение от покупателя</value>
  </data>
  <data name="Unavalable" xml:space="preserve">
    <value>Недоступно</value>
  </data>
  <data name="OrderPaymentMethod_ApplePay" xml:space="preserve">
    <value>ApplePay</value>
  </data>
  <data name="OrderPaymentMethod_GooglePay" xml:space="preserve">
    <value>GooglePay</value>
  </data>
  <data name="OrderPaymentMethod_Card" xml:space="preserve">
    <value>Банковская карта</value>
  </data>
  <data name="OrderPaymentMethod_Cash" xml:space="preserve">
    <value>Наличные</value>
  </data>
  <data name="OrderPaymentMethod_None" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="OrderPaymentMethod_Other" xml:space="preserve">
    <value>Другое</value>
  </data>
  <data name="OrderPaymentMethod_SamsungPay" xml:space="preserve">
    <value>SamsungPay</value>
  </data>
  <data name="PaymentMethods" xml:space="preserve">
    <value>Способы оплаты</value>
  </data>
  <data name="OrderDelveryStatus_None" xml:space="preserve">
    <value>Создан</value>
  </data>
  <data name="OrderDelveryStatus_Processing" xml:space="preserve">
    <value>В обработке</value>
  </data>
  <data name="OrderDelveryStatus_Delivering" xml:space="preserve">
    <value>Выполняется</value>
  </data>
  <data name="OrderDelveryStatus_Delivered" xml:space="preserve">
    <value>Исполнен</value>
  </data>
  <data name="OrderDelveryStatus_Canceled" xml:space="preserve">
    <value>Отменен</value>
  </data>
  <data name="OrderDelveryStatus_Expired" xml:space="preserve">
    <value>Истек</value>
  </data>
  <data name="OrderPaymentStatus_Pending" xml:space="preserve">
    <value>Ожидается</value>
  </data>
  <data name="OrderPaymentStatus_Payed" xml:space="preserve">
    <value>Оплачен</value>
  </data>
  <data name="Editor" xml:space="preserve">
    <value>Редактор</value>
  </data>
  <data name="OrderPaymentStatus_None" xml:space="preserve">
    <value>Неоплачено</value>
  </data>
  <data name="OrderPaymentStatus_Processing" xml:space="preserve">
    <value>В обработке</value>
  </data>
  <data name="Many" xml:space="preserve">
    <value>Много</value>
  </data>
  <data name="AvailableSeats" xml:space="preserve">
    <value>Доступные места</value>
  </data>
  <data name="IntervalsTime" xml:space="preserve">
    <value>Время:</value>
  </data>
  <data name="YouHaveActiveOrder" xml:space="preserve">
    <value>У вас есть активный заказ!</value>
  </data>
  <data name="NeedSeats" xml:space="preserve">
    <value>Необходимо мест</value>
  </data>
  <data name="BtnOrderNow" xml:space="preserve">
    <value>ЗАКАЗАТЬ</value>
  </data>
  <data name="ClosestDate" xml:space="preserve">
    <value>Ближайшая дата</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Дата</value>
  </data>
  <data name="NumberOfSeats0" xml:space="preserve">
    <value>Количество мест: {0}</value>
  </data>
  <data name="_0CharactersLeft" xml:space="preserve">
    <value>{0} символов осталось</value>
  </data>
  <data name="ContactUsL" xml:space="preserve">
    <value>Связаться с нами :</value>
  </data>
  <data name="YourOrder" xml:space="preserve">
    <value>Ваш заказ</value>
  </data>
  <data name="Conditions" xml:space="preserve">
    <value>Условия</value>
  </data>
  <data name="LinkMoreInfo" xml:space="preserve">
    <value>Ссылка c инфо</value>
  </data>
  <data name="OrderProcessed" xml:space="preserve">
    <value>Заказ оформлен</value>
  </data>
  <data name="BtnConfirmPay" xml:space="preserve">
    <value>ПОДТВЕРДИТЬ И ОПЛАТИТЬ</value>
  </data>
  <data name="OrderCaution" xml:space="preserve">
    <value>Убедитесь в том,что указанные данные корректны. Важно: проверьте что вы указали действительный номер телефона. Вы так же можете оставить сообщение к заказу ниже.</value>
  </data>
  <data name="CustomerInfo" xml:space="preserve">
    <value>Данные покупателя</value>
  </data>
  <data name="ProcessingOrder" xml:space="preserve">
    <value>Оформление заказа</value>
  </data>
  <data name="ErrorSomething" xml:space="preserve">
    <value>Что-то пошло не так</value>
  </data>
  <data name="PageLogin_LoginAuth_ConnectionFailedPleaseTryAgain" xml:space="preserve">
    <value>Не удалось установить связь. Пожалуйста, повторите попытку.</value>
  </data>
  <data name="PageLogin_LoginAuth_ForgotPassword" xml:space="preserve">
    <value>Забыли пароль?</value>
  </data>
  <data name="Auth_IncorrectEmailOrPassword" xml:space="preserve">
    <value>Неверное имя пользователя или пароль</value>
  </data>
  <data name="PageLogin_LoginAuth_ResetPassword" xml:space="preserve">
    <value>Сброс пароля</value>
  </data>
  <data name="ModelAuth_ConnectingWithProviderPleaseWait" xml:space="preserve">
    <value>Соединение с сервером. Пожалуйста, подождите..</value>
  </data>
  <data name="ModelAuth_DoRegistrationCheck" xml:space="preserve">
    <value>Чтобы воспользоваться всеми возможностями программы, пожалуйста, зарегистрируйтесь, это - быстро и бесплатно.</value>
  </data>
  <data name="ModelAuth_ServerErrorMessageDesc_InvalidEmailAdressProvided" xml:space="preserve">
    <value>Некорректный адрес электронной почты</value>
  </data>
  <data name="ModelAuth_ServerErrorMessageDesc_RegistrationFailedUsingAnotherEmailAdressMightHelp" xml:space="preserve">
    <value>Ошибка регистрации, может помочь использование другого адреса электронной почты</value>
  </data>
  <data name="ModelAuth_ServerErrorMessageDesc_ThePasswordMustBeAtLeast6CharactersLong" xml:space="preserve">
    <value>Пароль должен содержать не менее 6 символов</value>
  </data>
  <data name="Auth_XamarinAuthTitle" xml:space="preserve">
    <value>Аутентификация</value>
  </data>
  <data name="LoginProvider" xml:space="preserve">
    <value>Способ входа</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Пользователь</value>
  </data>
  <data name="Auth_LoginSuccess" xml:space="preserve">
    <value>Вы успешно вошли в систему</value>
  </data>
  <data name="Auth_LoggedOff" xml:space="preserve">
    <value>Вы вышли из системы</value>
  </data>
  <data name="Auth_LogOff" xml:space="preserve">
    <value>Выйти из аккаунта</value>
  </data>
  <data name="WelcomeGuest" xml:space="preserve">
    <value>Добро пожаловать, Гость!</value>
  </data>
  <data name="Hello0" xml:space="preserve">
    <value>Привет, {0}</value>
  </data>
  <data name="Country_AF" xml:space="preserve">
    <value>Афганистан</value>
  </data>
  <data name="Country_AL" xml:space="preserve">
    <value>Албания</value>
  </data>
  <data name="Country_DZ" xml:space="preserve">
    <value>Алжир</value>
  </data>
  <data name="Country_AS" xml:space="preserve">
    <value>Американское Самоа</value>
  </data>
  <data name="Country_AD" xml:space="preserve">
    <value>Андорра</value>
  </data>
  <data name="Country_AO" xml:space="preserve">
    <value>Ангола</value>
  </data>
  <data name="Country_AI" xml:space="preserve">
    <value>Ангилья</value>
  </data>
  <data name="Country_AQ" xml:space="preserve">
    <value>Антарктида</value>
  </data>
  <data name="Country_AG" xml:space="preserve">
    <value>Антигуа и Барбуда</value>
  </data>
  <data name="Country_AR" xml:space="preserve">
    <value>Аргентина</value>
  </data>
  <data name="Country_AM" xml:space="preserve">
    <value>Армения</value>
  </data>
  <data name="Country_AW" xml:space="preserve">
    <value>Аруба</value>
  </data>
  <data name="Country_AU" xml:space="preserve">
    <value>Австралия</value>
  </data>
  <data name="Country_AT" xml:space="preserve">
    <value>Австрия</value>
  </data>
  <data name="Country_AZ" xml:space="preserve">
    <value>Азербайджан</value>
  </data>
  <data name="Country_BH" xml:space="preserve">
    <value>Бахрейн</value>
  </data>
  <data name="Country_BD" xml:space="preserve">
    <value>Бангладеш</value>
  </data>
  <data name="Country_BB" xml:space="preserve">
    <value>Барбадос</value>
  </data>
  <data name="Country_BY" xml:space="preserve">
    <value>Беларусь</value>
  </data>
  <data name="Country_BE" xml:space="preserve">
    <value>Бельгия</value>
  </data>
  <data name="Country_BZ" xml:space="preserve">
    <value>Белиз</value>
  </data>
  <data name="Country_BJ" xml:space="preserve">
    <value>Бенин</value>
  </data>
  <data name="Country_BM" xml:space="preserve">
    <value>Бермудские острова</value>
  </data>
  <data name="Country_BT" xml:space="preserve">
    <value>Бутан</value>
  </data>
  <data name="Country_BO" xml:space="preserve">
    <value>Боливия</value>
  </data>
  <data name="Country_BA" xml:space="preserve">
    <value>Босния и Герцеговина</value>
  </data>
  <data name="Country_BW" xml:space="preserve">
    <value>Ботсвана</value>
  </data>
  <data name="Country_BV" xml:space="preserve">
    <value>Остров Буве</value>
  </data>
  <data name="Country_BR" xml:space="preserve">
    <value>Бразилия</value>
  </data>
  <data name="Country_IO" xml:space="preserve">
    <value>Британская территория Индийского океана</value>
  </data>
  <data name="Country_VG" xml:space="preserve">
    <value>Британские Виргинские острова</value>
  </data>
  <data name="Country_BN" xml:space="preserve">
    <value>Бруней</value>
  </data>
  <data name="Country_BG" xml:space="preserve">
    <value>Болгария</value>
  </data>
  <data name="Country_BF" xml:space="preserve">
    <value>Буркина-Фасо</value>
  </data>
  <data name="Country_BI" xml:space="preserve">
    <value>Бурунди</value>
  </data>
  <data name="Country_CI" xml:space="preserve">
    <value>Берег Слоновой Кости</value>
  </data>
  <data name="Country_KH" xml:space="preserve">
    <value>Камбоджа</value>
  </data>
  <data name="Country_CM" xml:space="preserve">
    <value>Камерун</value>
  </data>
  <data name="Country_CA" xml:space="preserve">
    <value>Канада</value>
  </data>
  <data name="Country_CV" xml:space="preserve">
    <value>Кабо-Верде</value>
  </data>
  <data name="Country_KY" xml:space="preserve">
    <value>Каймановы острова</value>
  </data>
  <data name="Country_CF" xml:space="preserve">
    <value>Центрально-Африканская Республика</value>
  </data>
  <data name="Country_TD" xml:space="preserve">
    <value>Чад</value>
  </data>
  <data name="Country_CL" xml:space="preserve">
    <value>Чили</value>
  </data>
  <data name="Country_CN" xml:space="preserve">
    <value>Китай</value>
  </data>
  <data name="Country_CX" xml:space="preserve">
    <value>Остров Рождества</value>
  </data>
  <data name="Country_CC" xml:space="preserve">
    <value>Кокосовые (Килинг) острова</value>
  </data>
  <data name="Country_CO" xml:space="preserve">
    <value>Колумбия</value>
  </data>
  <data name="Country_KM" xml:space="preserve">
    <value>Коморские острова</value>
  </data>
  <data name="Country_CG" xml:space="preserve">
    <value>Конго</value>
  </data>
  <data name="Country_CK" xml:space="preserve">
    <value>Острова Кука</value>
  </data>
  <data name="Country_CR" xml:space="preserve">
    <value>Коста-Рика</value>
  </data>
  <data name="Country_HR" xml:space="preserve">
    <value>Хорватия</value>
  </data>
  <data name="Country_CU" xml:space="preserve">
    <value>Куба</value>
  </data>
  <data name="Country_CY" xml:space="preserve">
    <value>Кипр</value>
  </data>
  <data name="Country_CZ" xml:space="preserve">
    <value>Чехия</value>
  </data>
  <data name="Country_CD" xml:space="preserve">
    <value>Демократическая Республика Конго</value>
  </data>
  <data name="Country_DK" xml:space="preserve">
    <value>Дания</value>
  </data>
  <data name="Country_DJ" xml:space="preserve">
    <value>Джибути</value>
  </data>
  <data name="Country_DM" xml:space="preserve">
    <value>Доминика</value>
  </data>
  <data name="Country_DO" xml:space="preserve">
    <value>Доминиканская Респблика</value>
  </data>
  <data name="Country_TL" xml:space="preserve">
    <value>Восточный Тимор</value>
  </data>
  <data name="Country_EC" xml:space="preserve">
    <value>Эквадор</value>
  </data>
  <data name="Country_EG" xml:space="preserve">
    <value>Египет</value>
  </data>
  <data name="Country_SV" xml:space="preserve">
    <value>Эль Сальвадор</value>
  </data>
  <data name="Country_GQ" xml:space="preserve">
    <value>Экваториальная Гвинея</value>
  </data>
  <data name="Country_ER" xml:space="preserve">
    <value>Эритрея</value>
  </data>
  <data name="Country_EE" xml:space="preserve">
    <value>Эстония</value>
  </data>
  <data name="Country_ET" xml:space="preserve">
    <value>Эфиопия</value>
  </data>
  <data name="Country_FO" xml:space="preserve">
    <value>Фарерские острова</value>
  </data>
  <data name="Country_FK" xml:space="preserve">
    <value>Фолклендские острова</value>
  </data>
  <data name="Country_FJ" xml:space="preserve">
    <value>Фиджи</value>
  </data>
  <data name="Country_FI" xml:space="preserve">
    <value>Финляндия</value>
  </data>
  <data name="Country_MK" xml:space="preserve">
    <value>Бывшая Югославская Республика Македония</value>
  </data>
  <data name="Country_FR" xml:space="preserve">
    <value>Франция</value>
  </data>
  <data name="Country_GF" xml:space="preserve">
    <value>Французская Гвиана</value>
  </data>
  <data name="Country_PF" xml:space="preserve">
    <value>Французская Полинезия</value>
  </data>
  <data name="Country_TF" xml:space="preserve">
    <value>Южные Французские Территории</value>
  </data>
  <data name="Country_GA" xml:space="preserve">
    <value>Габон</value>
  </data>
  <data name="Country_GE" xml:space="preserve">
    <value>Грузия</value>
  </data>
  <data name="Country_DE" xml:space="preserve">
    <value>Германия</value>
  </data>
  <data name="Country_GH" xml:space="preserve">
    <value>Гана</value>
  </data>
  <data name="Country_GI" xml:space="preserve">
    <value>Гибралтар</value>
  </data>
  <data name="Country_GR" xml:space="preserve">
    <value>Греция</value>
  </data>
  <data name="Country_GL" xml:space="preserve">
    <value>Гренландия</value>
  </data>
  <data name="Country_GD" xml:space="preserve">
    <value>Гренада</value>
  </data>
  <data name="Country_GP" xml:space="preserve">
    <value>Гваделупа</value>
  </data>
  <data name="Country_GU" xml:space="preserve">
    <value>Гуам</value>
  </data>
  <data name="Country_GT" xml:space="preserve">
    <value>Гватемала</value>
  </data>
  <data name="Country_GN" xml:space="preserve">
    <value>Гвинея</value>
  </data>
  <data name="Country_GW" xml:space="preserve">
    <value>Гвинея-Бисау</value>
  </data>
  <data name="Country_GY" xml:space="preserve">
    <value>Гайана</value>
  </data>
  <data name="Country_HT" xml:space="preserve">
    <value>Гаити</value>
  </data>
  <data name="Country_HM" xml:space="preserve">
    <value>Остров Херд и острова Макдональд</value>
  </data>
  <data name="Country_HN" xml:space="preserve">
    <value>Гондурас</value>
  </data>
  <data name="Country_HK" xml:space="preserve">
    <value>Гонконг</value>
  </data>
  <data name="Country_HU" xml:space="preserve">
    <value>Венгрия</value>
  </data>
  <data name="Country_IS" xml:space="preserve">
    <value>Исландия</value>
  </data>
  <data name="Country_IN" xml:space="preserve">
    <value>Индия</value>
  </data>
  <data name="Country_ID" xml:space="preserve">
    <value>Индонезия</value>
  </data>
  <data name="Country_IR" xml:space="preserve">
    <value>Иран</value>
  </data>
  <data name="Country_IQ" xml:space="preserve">
    <value>Ирак</value>
  </data>
  <data name="Country_IE" xml:space="preserve">
    <value>Ирландия</value>
  </data>
  <data name="Country_IL" xml:space="preserve">
    <value>Израиль</value>
  </data>
  <data name="Country_IT" xml:space="preserve">
    <value>Италия</value>
  </data>
  <data name="Country_JM" xml:space="preserve">
    <value>Ямайка</value>
  </data>
  <data name="Country_JP" xml:space="preserve">
    <value>Япония</value>
  </data>
  <data name="Country_JO" xml:space="preserve">
    <value>Иордания</value>
  </data>
  <data name="Country_KZ" xml:space="preserve">
    <value>Казахстан</value>
  </data>
  <data name="Country_KE" xml:space="preserve">
    <value>Кения</value>
  </data>
  <data name="Country_KI" xml:space="preserve">
    <value>Кирибати</value>
  </data>
  <data name="Country_KW" xml:space="preserve">
    <value>Кувейт</value>
  </data>
  <data name="Country_KG" xml:space="preserve">
    <value>Киргизия</value>
  </data>
  <data name="Country_LA" xml:space="preserve">
    <value>Лаос</value>
  </data>
  <data name="Country_LV" xml:space="preserve">
    <value>Латвия</value>
  </data>
  <data name="Country_LB" xml:space="preserve">
    <value>Ливан</value>
  </data>
  <data name="Country_LS" xml:space="preserve">
    <value>Лесото</value>
  </data>
  <data name="Country_LR" xml:space="preserve">
    <value>Либерия</value>
  </data>
  <data name="Country_LY" xml:space="preserve">
    <value>Ливия</value>
  </data>
  <data name="Country_LI" xml:space="preserve">
    <value>Лихтенштейн</value>
  </data>
  <data name="Country_LT" xml:space="preserve">
    <value>Литва</value>
  </data>
  <data name="Country_LU" xml:space="preserve">
    <value>Люксембург</value>
  </data>
  <data name="Country_MO" xml:space="preserve">
    <value>Макао</value>
  </data>
  <data name="Country_MG" xml:space="preserve">
    <value>Мадагаскар</value>
  </data>
  <data name="Country_MW" xml:space="preserve">
    <value>Малави</value>
  </data>
  <data name="Country_MY" xml:space="preserve">
    <value>Малайзия</value>
  </data>
  <data name="Country_MV" xml:space="preserve">
    <value>Мальдивы</value>
  </data>
  <data name="Country_ML" xml:space="preserve">
    <value>Мали</value>
  </data>
  <data name="Country_MT" xml:space="preserve">
    <value>Мальта</value>
  </data>
  <data name="Country_MH" xml:space="preserve">
    <value>Маршалловы острова</value>
  </data>
  <data name="Country_MQ" xml:space="preserve">
    <value>Мартиника</value>
  </data>
  <data name="Country_MR" xml:space="preserve">
    <value>Мавритания</value>
  </data>
  <data name="Country_MU" xml:space="preserve">
    <value>Маврикий</value>
  </data>
  <data name="Country_YT" xml:space="preserve">
    <value>Майотта</value>
  </data>
  <data name="Country_MX" xml:space="preserve">
    <value>Мексика</value>
  </data>
  <data name="Country_FM" xml:space="preserve">
    <value>Микронезия</value>
  </data>
  <data name="Country_MD" xml:space="preserve">
    <value>Молдова</value>
  </data>
  <data name="Country_MC" xml:space="preserve">
    <value>Монако</value>
  </data>
  <data name="Country_MN" xml:space="preserve">
    <value>Монголия</value>
  </data>
  <data name="Country_MS" xml:space="preserve">
    <value>Монсеррат</value>
  </data>
  <data name="Country_MA" xml:space="preserve">
    <value>Марокко</value>
  </data>
  <data name="Country_MZ" xml:space="preserve">
    <value>Мозамбик</value>
  </data>
  <data name="Country_MM" xml:space="preserve">
    <value>Мьянма</value>
  </data>
  <data name="Country_NA" xml:space="preserve">
    <value>Намибия</value>
  </data>
  <data name="Country_NR" xml:space="preserve">
    <value>Науру</value>
  </data>
  <data name="Country_NP" xml:space="preserve">
    <value>Непал</value>
  </data>
  <data name="Country_NL" xml:space="preserve">
    <value>Нидерланды</value>
  </data>
  <data name="Country_AN" xml:space="preserve">
    <value>Нидерландские Антильские острова</value>
  </data>
  <data name="Country_NC" xml:space="preserve">
    <value>Новая Каледония</value>
  </data>
  <data name="Country_NZ" xml:space="preserve">
    <value>Новая Зеландия</value>
  </data>
  <data name="Country_NI" xml:space="preserve">
    <value>Никарагуа</value>
  </data>
  <data name="Country_NE" xml:space="preserve">
    <value>Нигер</value>
  </data>
  <data name="Country_NG" xml:space="preserve">
    <value>Нигерия</value>
  </data>
  <data name="Country_NU" xml:space="preserve">
    <value>Ниу</value>
  </data>
  <data name="Country_NF" xml:space="preserve">
    <value>Остров Норфолк</value>
  </data>
  <data name="Country_KP" xml:space="preserve">
    <value>Северная Корея</value>
  </data>
  <data name="Country_MP" xml:space="preserve">
    <value>Северные Марианские</value>
  </data>
  <data name="Country_NO" xml:space="preserve">
    <value>Норвегия</value>
  </data>
  <data name="Country_OM" xml:space="preserve">
    <value>Оман</value>
  </data>
  <data name="Country_PK" xml:space="preserve">
    <value>Пакистан</value>
  </data>
  <data name="Country_PW" xml:space="preserve">
    <value>Палау</value>
  </data>
  <data name="Country_PA" xml:space="preserve">
    <value>Панама</value>
  </data>
  <data name="Country_PG" xml:space="preserve">
    <value>Папуа - Новая Гвинея</value>
  </data>
  <data name="Country_PY" xml:space="preserve">
    <value>Парагвай</value>
  </data>
  <data name="Country_PE" xml:space="preserve">
    <value>Перу</value>
  </data>
  <data name="Country_PH" xml:space="preserve">
    <value>Филиппины</value>
  </data>
  <data name="Country_PN" xml:space="preserve">
    <value>Питкэрн</value>
  </data>
  <data name="Country_PL" xml:space="preserve">
    <value>Польша</value>
  </data>
  <data name="Country_PT" xml:space="preserve">
    <value>Португалия</value>
  </data>
  <data name="Country_PR" xml:space="preserve">
    <value>Пуэрто-Рико</value>
  </data>
  <data name="Country_QA" xml:space="preserve">
    <value>Катар</value>
  </data>
  <data name="Country_RE" xml:space="preserve">
    <value>Реюньон</value>
  </data>
  <data name="Country_RO" xml:space="preserve">
    <value>Румыния</value>
  </data>
  <data name="Country_RU" xml:space="preserve">
    <value>Россия</value>
  </data>
  <data name="Country_RW" xml:space="preserve">
    <value>Руанда</value>
  </data>
  <data name="Country_ST" xml:space="preserve">
    <value>Сан-Томе и Принсипи</value>
  </data>
  <data name="Country_SH" xml:space="preserve">
    <value>Остров Святой Елены</value>
  </data>
  <data name="Country_KN" xml:space="preserve">
    <value>Сент-Китс и Невис</value>
  </data>
  <data name="Country_LC" xml:space="preserve">
    <value>Санкт-Люсия</value>
  </data>
  <data name="Country_PM" xml:space="preserve">
    <value>Сен-Пьер и Микелон</value>
  </data>
  <data name="Country_VC" xml:space="preserve">
    <value>Святой Винсент и Гренадины</value>
  </data>
  <data name="Country_WS" xml:space="preserve">
    <value>Самоа</value>
  </data>
  <data name="Country_SM" xml:space="preserve">
    <value>Сан - Марино</value>
  </data>
  <data name="Country_SA" xml:space="preserve">
    <value>Саудовская Аравия</value>
  </data>
  <data name="Country_SN" xml:space="preserve">
    <value>Сенегал</value>
  </data>
  <data name="Country_SC" xml:space="preserve">
    <value>Сейшельские острова</value>
  </data>
  <data name="Country_SL" xml:space="preserve">
    <value>Сьерра-Леоне</value>
  </data>
  <data name="Country_SG" xml:space="preserve">
    <value>Сингапур</value>
  </data>
  <data name="Country_SK" xml:space="preserve">
    <value>Словакия</value>
  </data>
  <data name="Country_SI" xml:space="preserve">
    <value>Словения</value>
  </data>
  <data name="Country_SB" xml:space="preserve">
    <value>Соломоновы острова</value>
  </data>
  <data name="Country_SO" xml:space="preserve">
    <value>Сомали</value>
  </data>
  <data name="Country_ZA" xml:space="preserve">
    <value>Южная Африка</value>
  </data>
  <data name="Country_GS" xml:space="preserve">
    <value>Южная Джорджия и Южные Сандвичевы Острова</value>
  </data>
  <data name="Country_KR" xml:space="preserve">
    <value>Южная Корея</value>
  </data>
  <data name="Country_ES" xml:space="preserve">
    <value>Испания</value>
  </data>
  <data name="Country_LK" xml:space="preserve">
    <value>Шри Ланка</value>
  </data>
  <data name="Country_SD" xml:space="preserve">
    <value>Судан</value>
  </data>
  <data name="Country_SR" xml:space="preserve">
    <value>Суринам</value>
  </data>
  <data name="Country_SJ" xml:space="preserve">
    <value>Шпицберген и Ян-Майен</value>
  </data>
  <data name="Country_SZ" xml:space="preserve">
    <value>Свазиленд</value>
  </data>
  <data name="Country_SE" xml:space="preserve">
    <value>Швеция</value>
  </data>
  <data name="Country_CH" xml:space="preserve">
    <value>Швейцария</value>
  </data>
  <data name="Country_SY" xml:space="preserve">
    <value>Сирия</value>
  </data>
  <data name="Country_TW" xml:space="preserve">
    <value>Тайвань</value>
  </data>
  <data name="Country_TJ" xml:space="preserve">
    <value>Таджикистан</value>
  </data>
  <data name="Country_TZ" xml:space="preserve">
    <value>Танзания</value>
  </data>
  <data name="Country_TH" xml:space="preserve">
    <value>Таиланд</value>
  </data>
  <data name="Country_BS" xml:space="preserve">
    <value>Багамы</value>
  </data>
  <data name="Country_GM" xml:space="preserve">
    <value>Гамбия</value>
  </data>
  <data name="Country_TG" xml:space="preserve">
    <value>Идти</value>
  </data>
  <data name="Country_TK" xml:space="preserve">
    <value>Токелау</value>
  </data>
  <data name="Country_TO" xml:space="preserve">
    <value>Тонга</value>
  </data>
  <data name="Country_TT" xml:space="preserve">
    <value>Тринидад и Тобаго</value>
  </data>
  <data name="Country_TN" xml:space="preserve">
    <value>Тунис</value>
  </data>
  <data name="Country_TR" xml:space="preserve">
    <value>Турция</value>
  </data>
  <data name="Country_TM" xml:space="preserve">
    <value>Туркменистан</value>
  </data>
  <data name="Country_TC" xml:space="preserve">
    <value>Теркс и Кайкос</value>
  </data>
  <data name="Country_TV" xml:space="preserve">
    <value>Тувалу</value>
  </data>
  <data name="Country_VI" xml:space="preserve">
    <value>Американские Виргинские острова</value>
  </data>
  <data name="Country_UG" xml:space="preserve">
    <value>Уганда</value>
  </data>
  <data name="Country_UA" xml:space="preserve">
    <value>Украина</value>
  </data>
  <data name="Country_AE" xml:space="preserve">
    <value>Объединенные Арабские Эмираты</value>
  </data>
  <data name="Country_GB" xml:space="preserve">
    <value>Объединенное Королевство</value>
  </data>
  <data name="Country_US" xml:space="preserve">
    <value>Соединенные Штаты Америки</value>
  </data>
  <data name="Country_UM" xml:space="preserve">
    <value>США Внешние малые острова</value>
  </data>
  <data name="Country_UY" xml:space="preserve">
    <value>Уругвай</value>
  </data>
  <data name="Country_UZ" xml:space="preserve">
    <value>Узбекистан</value>
  </data>
  <data name="Country_VU" xml:space="preserve">
    <value>Вануату</value>
  </data>
  <data name="Country_VA" xml:space="preserve">
    <value>Ватикан</value>
  </data>
  <data name="Country_VE" xml:space="preserve">
    <value>Венесуэла</value>
  </data>
  <data name="Country_VN" xml:space="preserve">
    <value>Вьетнам</value>
  </data>
  <data name="Country_WF" xml:space="preserve">
    <value>Уоллис и Футуна</value>
  </data>
  <data name="Country_EH" xml:space="preserve">
    <value>Западная Сахара</value>
  </data>
  <data name="Country_YE" xml:space="preserve">
    <value>Йемен</value>
  </data>
  <data name="Country_YU" xml:space="preserve">
    <value>Югославия</value>
  </data>
  <data name="Country_ZM" xml:space="preserve">
    <value>Замбия</value>
  </data>
  <data name="Country_ZW" xml:space="preserve">
    <value>Зимбабве</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Обязательно</value>
  </data>
  <data name="PastOrders" xml:space="preserve">
    <value>Прошлые заказы</value>
  </data>
  <data name="ActiveOrders" xml:space="preserve">
    <value>Активные заказы</value>
  </data>
  <data name="EditDetails" xml:space="preserve">
    <value>Редактировать данные</value>
  </data>
  <data name="OneMoreStep" xml:space="preserve">
    <value>Остался один шаг</value>
  </data>
  <data name="ConnectWith" xml:space="preserve">
    <value>Войти с помощью</value>
  </data>
  <data name="Feedback" xml:space="preserve">
    <value>Отзывы</value>
  </data>
  <data name="LeaveMessage" xml:space="preserve">
    <value>Оставьте сообщение</value>
  </data>
  <data name="PaymentMessageSuccess" xml:space="preserve">
    <value>Спасибо, ваш заказ успешно оформлен и оплачен!</value>
  </data>
  <data name="YouHaveActiveOrders" xml:space="preserve">
    <value>У вас есть активные заказы!</value>
  </data>
  <data name="MoreInfoLink" xml:space="preserve">
    <value>Больше инфо по ссылке</value>
  </data>
  <data name="OrderPaymentStatus_Canceled" xml:space="preserve">
    <value>Отменено</value>
  </data>
  <data name="OrderPaymentStatus_Failed" xml:space="preserve">
    <value>Неудача</value>
  </data>
  <data name="PleaseFillEmptyFields" xml:space="preserve">
    <value>Пожалуйста, заполните пустые поля</value>
  </data>
  <data name="PleaseFillRequiredFields" xml:space="preserve">
    <value>Пожалуйста, заполните необходимые поля</value>
  </data>
  <data name="PaymentMessageFail" xml:space="preserve">
    <value>Нам не удалось обработать ваш платеж, пожалуйста, попробуйте еще раз с другой информацией.</value>
  </data>
  <data name="BtnDeleteSelected" xml:space="preserve">
    <value>Удалить отмеченное</value>
  </data>
  <data name="SecurePayment" xml:space="preserve">
    <value>Оплата</value>
  </data>
  <data name="BtnPayNow" xml:space="preserve">
    <value>Оплатить</value>
  </data>
  <data name="BtnLeaveFeedback" xml:space="preserve">
    <value>Оставить отзыв</value>
  </data>
  <data name="OrderPaymentStatus_Refunded" xml:space="preserve">
    <value>Возмещен</value>
  </data>
  <data name="PushOrderPayed" xml:space="preserve">
    <value>Спасибо, ваш заказ {0} успешно оплачен!</value>
  </data>
  <data name="PushOrderCreated" xml:space="preserve">
    <value>Заказ создан {0}.</value>
  </data>
  <data name="PushCashPaymentAllowed" xml:space="preserve">
    <value>Оплата наличных была разрешена для заказа {0}, сумма к оплате {1}.</value>
  </data>
  <data name="PushCashPaymentDeclined" xml:space="preserve">
    <value>Наличный платеж был отклонен для заказа {0}.</value>
  </data>
  <data name="PushOrderCanceled" xml:space="preserve">
    <value>Заказ был отменен: {0}.</value>
  </data>
  <data name="BtnEditFeedback" xml:space="preserve">
    <value>Редактировать отзыв</value>
  </data>
  <data name="ImportantNotes" xml:space="preserve">
    <value>Важные заметки</value>
  </data>
  <data name="Auth_CountryCode" xml:space="preserve">
    <value>Код страны</value>
  </data>
  <data name="Refunds" xml:space="preserve">
    <value>Политика возврата</value>
  </data>
  <data name="SortTitle" xml:space="preserve">
    <value>По названию</value>
  </data>
  <data name="BtnCancelOrder" xml:space="preserve">
    <value>Отменить заказ</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Вы уверены?</value>
  </data>
  <data name="SortPrice" xml:space="preserve">
    <value>По цене</value>
  </data>
  <data name="TargetSegmentParameters" xml:space="preserve">
    <value>Target Parameters</value>
  </data>
  <data name="CustomersElement" xml:space="preserve">
    <value>Те, кто купили</value>
  </data>
  <data name="VimeoId" xml:space="preserve">
    <value>VimeoId</value>
  </data>
  <data name="S_PrivacyMessage" xml:space="preserve">
    <value>Нажимая на любые кнопки на этой странице, вы даёте согласие на обработку персональных данных и соглашаетесь с нашей политикой конфиденциальности.</value>
  </data>
  <data name="SalaryType_Monthly" xml:space="preserve">
    <value>Оклад</value>
  </data>
  <data name="SalaryType_Hourly" xml:space="preserve">
    <value>Почасовая</value>
  </data>
  <data name="VacancyState_Draft" xml:space="preserve">
    <value>Черновик</value>
  </data>
  <data name="VacancyState_Active" xml:space="preserve">
    <value>Активна</value>
  </data>
  <data name="VacancyState_Closed" xml:space="preserve">
    <value>Закрыта</value>
  </data>
  <data name="OccupancyType_Partial" xml:space="preserve">
    <value>Частичная</value>
  </data>
  <data name="OccupancyType_Fulltime" xml:space="preserve">
    <value>Полная</value>
  </data>
  <data name="ExperienceType_Unneeded" xml:space="preserve">
    <value>не требуется</value>
  </data>
  <data name="ExperienceType_UpTo1" xml:space="preserve">
    <value>до года</value>
  </data>
  <data name="ExperienceType_From1To3" xml:space="preserve">
    <value>от 1 до 3 лет</value>
  </data>
  <data name="ExperienceType_From3To5" xml:space="preserve">
    <value>от 3 до 5 лет</value>
  </data>
  <data name="ExperienceType_From5" xml:space="preserve">
    <value>от 5 лет</value>
  </data>
  <data name="OrderPaymentMethod_CouponCode" xml:space="preserve">
    <value>Код купона</value>
  </data>
  <data name="FinancialReasonType_Balance" xml:space="preserve">
    <value>Пополнение</value>
  </data>
  <data name="FinancialReasonType_Subscription" xml:space="preserve">
    <value>Подписка</value>
  </data>
  <data name="FinancialReasonType_Charge" xml:space="preserve">
    <value>Оплата</value>
  </data>
  <data name="FinancialReasonType_Other" xml:space="preserve">
    <value>Другое</value>
  </data>
  <data name="FinancialReasonType_Deposit" xml:space="preserve">
    <value>Депозит</value>
  </data>
  <data name="OrderPaymentMethod_Balance" xml:space="preserve">
    <value>Счет</value>
  </data>
  <data name="ExplainDate_Yest" xml:space="preserve">
    <value>Вчера</value>
  </data>
  <data name="ExplainDate_X1past" xml:space="preserve">
    <value>{0} день назад</value>
  </data>
  <data name="ExplainDate_X2past" xml:space="preserve">
    <value>{0} дня назад</value>
  </data>
  <data name="ExplainDate_Xpast" xml:space="preserve">
    <value>{0} дней назад</value>
  </data>
</root>