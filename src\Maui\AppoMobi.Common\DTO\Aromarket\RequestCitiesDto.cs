﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public class RequestCitiesDto : WithTokenDto
    {
        /// <summary>
        /// Какие города возвращаем: 1 - только основные города (по умолчанию), 0 - все доступные 
        /// </summary>
        [JsonProperty("main")]
        public int Filter { get; set; }

        [JsonProperty("country_id")]
        public string Country { get; set; }

        [JsonProperty("search")]
        public string Search { get; set; }

        [JsonProperty("page")]
        public int PageFrom1 { get; set; }

        [JsonProperty("limit")]
        public int PageSize { get; set; } = 20;

    }
}