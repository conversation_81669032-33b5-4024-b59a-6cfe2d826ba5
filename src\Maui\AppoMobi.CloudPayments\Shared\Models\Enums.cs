﻿namespace AppoMobi.CloudPayments.Models
{
    public enum TransactionStatus
    {
        Unset,

        /// <summary>
        /// После перехода плательщика на сайт эмитента в ожидании результатов 3-D Secure
        /// </summary>
        AwaitingAuthentication,

        /// <summary>
        /// После получения авторизации
        /// </summary>
        Authorized,

        /// <summary>
        /// После подтверждения операции
        /// </summary>
        Completed,

        /// <summary>
        /// В случае отмены операции
        /// </summary>
        Cancelled,

        /// <summary>
        /// В случае невозможности провести операцию (нет денег на счете карты и т.п.)
        /// </summary>
        Declined
    }
    
    //todo below 

    public enum TransactionFailedReason
    {
        Approved, //0
        AuthenticationFailed //5206
    }

    public enum TransactionCardType
    {
        Visa, //0
        MasterCard //1
    }


}
