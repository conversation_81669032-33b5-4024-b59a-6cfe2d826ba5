using AppoMobi.CloudPayments.Plugin;
using AppoMobi.CloudPayments.Models;
using AppoMobi.CloudPayments;

namespace CloudPaymentsTest;

public partial class MainPage : ContentPage
{
	public MainPage()
	{
		InitializeComponent();
	}

	private async void OnTestApiClicked(object? sender, EventArgs e)
	{
		try
		{
			StatusLabel.Text = "Testing API...";
			var result = await CloudPaymentsSdk.Api.TestAsync();
			StatusLabel.Text = result ? "API Test: SUCCESS" : "API Test: FAILED";
		}
		catch (Exception ex)
		{
			StatusLabel.Text = $"API Test Error: {ex.Message}";
		}
	}

	private void OnShow3DSClicked(object? sender, EventArgs e)
	{
		if (View3DS.IsVisible)
		{
			View3DS.IsVisible = false;
			Show3DSBtn.Text = "Show 3DS View";
			StatusLabel.Text = "3DS View Hidden";
		}
		else
		{
			View3DS.IsVisible = true;
			Show3DSBtn.Text = "Hide 3DS View";
			StatusLabel.Text = "3DS View Shown";
			
			// Setup a mock transaction for testing
			View3DS.Transaction = new Transaction
			{
				AcsUrl = "https://demo.cloudpayments.ru/3ds/test",
				PayerAuthenticationRequest = "test_pa_req",
				TransactionId = "test_transaction_id"
			};
			
			View3DS.Request3DSCallback = (response) =>
			{
				StatusLabel.Text = response.Success ? "3DS Success" : $"3DS Failed: {response.Message}";
			};
			
			// Simulate starting 3DS
			View3DS.Go();
		}
	}
}
