using AppoMobi.CloudPayments.Plugin;
using AppoMobi.CloudPayments.Models;
using AppoMobi.CloudPayments;

namespace CloudPaymentsTest;

public partial class MainPage : ContentPage
{
	public MainPage()
	{
		InitializeComponent();
	}

	private async void OnTestApiClicked(object? sender, EventArgs e)
	{
		try
		{
			StatusLabel.Text = "Testing API...";
			var result = await CloudPaymentsSdk.Api.TestAsync();
			StatusLabel.Text = result ? "API Test: SUCCESS" : "API Test: FAILED";
		}
		catch (Exception ex)
		{
			StatusLabel.Text = $"API Test Error: {ex.Message}";
		}
	}

	private async void OnShow3DSClicked(object? sender, EventArgs e)
	{
		if (View3DS.IsVisible)
		{
			View3DS.IsVisible = false;
			Show3DSBtn.Text = "Show 3DS View";
			StatusLabel.Text = "3DS View Hidden";
		}
		else
		{
			try
			{
				StatusLabel.Text = "Creating payment to get 3DS data...";
				Show3DSBtn.IsEnabled = false;

				// Create a real payment request that will trigger 3DS
				var cardInfo = new CardInfo("****************", "1225", "123"); // Test card that requires 3DS
				var paymentRequest = new PayRequestArgs
				{
					Amount = "10.00", // 10 rubles
					Currency = "RUB",
					InvoiceId = Guid.NewGuid().ToString(),
					Description = "Test 3DS Payment",
					AccountId = "test_user_123",
					Email = "<EMAIL>",
					CardCryptogramPacket = cardInfo.CreateCryptogram(),
					Name = "Test User"
				};

				var paymentResult = await CloudPaymentsSdk.Api.ChargeNewCardAsync(paymentRequest);

				if (paymentResult.Success && paymentResult.Model != null)
				{
					var transaction = paymentResult.Model;

					// Check if 3DS is required
					if (!string.IsNullOrEmpty(transaction.AcsUrl) && !string.IsNullOrEmpty(transaction.PayerAuthenticationRequest))
					{
						View3DS.IsVisible = true;
						Show3DSBtn.Text = "Hide 3DS View";
						StatusLabel.Text = "3DS Authentication Required - Real Transaction Data";

						// Setup the real transaction for 3DS
						View3DS.Transaction = transaction;

						View3DS.Request3DSCallback = async (response) =>
						{
							if (response.Success && response.Model != null)
							{
								StatusLabel.Text = "3DS Success - Completing payment...";

								// Complete the payment with Post3DS
								try
								{
									var post3dsResult = await CloudPaymentsSdk.Api.Post3DSAsync(response.Model);
									if (post3dsResult.Success)
									{
										StatusLabel.Text = $"Payment Completed! Transaction ID: {post3dsResult.Model?.TransactionId}";
									}
									else
									{
										StatusLabel.Text = $"Payment Failed: {post3dsResult.Message}";
									}
								}
								catch (Exception ex)
								{
									StatusLabel.Text = $"Post3DS Error: {ex.Message}";
								}
							}
							else
							{
								StatusLabel.Text = $"3DS Failed: {response.Message}";
							}
						};

						// Start 3DS authentication
						View3DS.Go();
					}
					else
					{
						// Payment completed without 3DS
						StatusLabel.Text = $"Payment completed without 3DS! Transaction ID: {transaction.TransactionId}";
					}
				}
				else
				{
					StatusLabel.Text = $"Payment failed: {paymentResult.Message}";
				}
			}
			catch (Exception ex)
			{
				StatusLabel.Text = $"Error creating payment: {ex.Message}";
			}
			finally
			{
				Show3DSBtn.IsEnabled = true;
			}
		}
	}
}
