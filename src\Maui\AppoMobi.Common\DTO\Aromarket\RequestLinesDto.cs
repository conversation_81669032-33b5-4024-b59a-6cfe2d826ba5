﻿using System;
using System.Collections.Generic;
using System.Text;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    #region get_lines

    public class RequestLinesDto : WithTokenDto
    {


        /// <summary>
        /// ID бренда
        /// </summary>
        [JsonProperty("brand_id")]
        public string BrandId { get; set; }

        /// <summary>
        /// men - мужскоая, lady - женская, unisex - Юнисекс
        /// </summary>
        [JsonProperty("sex")]
        public string Sex { get; set; }

        [JsonProperty("catalog_id")]
        public string Section { get; set; }

        [JsonProperty("page")]
        public int PageFrom1 { get; set; }

        [JsonProperty("pagelimit")]
        public int PageSize { get; set; }

        /// <summary>
        /// Сортировка: rating - рейтинг, alpha - алфавит
        /// </summary>
        [JsonProperty("sort")]
        public string Sort { get; set; }

        [JsonProperty("available")]
        public int? Available { get; set; }
    }

    //returns LinesListDto



    #endregion
}
