﻿using System;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class UserRequestDto : UserProductDto
    {
        [JsonProperty("request_id")]
        public string RequestId { get; set; }

        [JsonProperty("request_time")]
        [JsonConverter(typeof(SecondsEpochConverter))]
        public DateTime RequestTime { get; set; }

        [JsonProperty("request_status")]
        public ApiRequestStatus RequestStatus { get; set; }

    }
}