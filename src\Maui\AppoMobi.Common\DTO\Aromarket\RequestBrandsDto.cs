﻿using System;
using System.Text;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    #region get_brands    

    public class RequestBrandsDto : WithTokenDto
    {


        [JsonProperty("letter")]
        public string Letter { get; set; }

        /// <summary>
        /// ID раздела каталога, по умолчанию - 1 (парфюм)
        /// </summary>
        [JsonProperty("catalog_id")]
        public string SectionId { get; set; }

        /// <summary>
        /// null, unisex, men, lady
        /// </summary>
        [JsonProperty("sex")]
        public string Sex{ get; set; }

    }

    #endregion
}
