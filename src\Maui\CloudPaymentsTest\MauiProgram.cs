﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace CloudPaymentsTest;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		var builder = MauiApp.CreateBuilder();
		builder
			.UseMauiApp<App>()
			.UseCloudPayments(options =>
			{
				options.ClientId = "test_api_00000000000000000000002";
				options.ApiSecret = "50dfb1df0cb5c84528e9d3772f7ce04e";
                options.OnApiException = ex => System.Diagnostics.Debug.WriteLine($"CloudPayments Error: {ex.Message}");
			})
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
				fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
			});

#if DEBUG
		builder.Logging.AddDebug();
#endif

		return builder.Build();
	}
}
