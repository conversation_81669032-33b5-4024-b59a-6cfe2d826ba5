﻿<?xml version="1.0" encoding="UTF-8" ?>
<views:Grid
    x:Class="AppoMobi.Forms.Controls.Input.PseudoEntry"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://xamarin.com/schemas/2014/forms/design"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:AppoMobi.Forms.Xam.Touch;assembly=AppoMobi.Forms"
    x:Name="This"
    Tapped="PseudoEntry_OnTapped"
    mc:Ignorable="d">

    <!--<Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>-->

    <Entry
        x:Name="EntryHidden"
        Focused="EntryHidden_OnFocused"
        Keyboard="Telephone"
        TextChanged="EntryHidden_OnTextChanged"
        TranslationY="-3000"
        Unfocused="EntryHidden_OnUnfocused" />

    <Label
        x:Name="LabelPlaceholder"
        IsVisible="{Binding Source={x:Reference This}, Path=IsPlaceholderVisible}"
        Text="{Binding Source={x:Reference This}, Path=Placeholder}"
        VerticalOptions="Center" />

    <StackLayout
        Orientation="Horizontal"
        Spacing="0"
        VerticalOptions="Fill">

        <Label
            x:Name="LabelDisplay"
            HorizontalOptions="Fill"
            IsVisible="{Binding Source={x:Reference This}, Path=IsTextVisible}"
            Text="{Binding Source={x:Reference This}, Path=FormattedText}"
            TextColor="{StaticResource ColorTextUI}"
            VerticalOptions="Center" />

        <!--  CURSOR  -->
        <Label
            x:Name="LabelCursor"
            Margin="0,0.5,0,0"
            FontSize="16"
            IsVisible="{Binding Source={x:Reference This}, Path=IsFocused}"
            Text="|"
            TextColor="{StaticResource ColorAccent}"
            VerticalOptions="Center" />

    </StackLayout>


</views:Grid>