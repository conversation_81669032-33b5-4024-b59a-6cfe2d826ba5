﻿using System;
using System.Text;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{

 

    public class RequestSearchDto : WithTokenDto
    {
        [JsonProperty("search")]
        public string Term { get; set; }

        [JsonProperty("catalog_id")]
        public string CatalogId { get; set; }

        [JsonProperty("page")]
        public int PageFrom1 { get; set; }

        [JsonProperty("pagelimit")]
        public int PageSize { get; set; }
    }
}
