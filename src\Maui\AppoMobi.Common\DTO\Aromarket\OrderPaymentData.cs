﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class OrderPaymentData
    {
        [JsonProperty("public_id")]
        public string GatewayClientId { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("card_info_url")]
        public string PolicyUrl { get; set; }

        //[JsonProperty("pay_url")]
        //public string PaymentUrl { get; set; }

        [JsonProperty("debit_warning")]
        public string DebitNotes { get; set; }

        [JsonProperty("debit_alternative")]
        public string DebitAlternative { get; set; }

   
    }
}