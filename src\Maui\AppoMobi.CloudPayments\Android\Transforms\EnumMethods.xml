﻿<enum-method-mappings>
  <!--
  This example changes the Java method:
    android.support.v4.app.Fragment.SavedState.writeToParcel (int flags)
  to be:
    android.support.v4.app.Fragment.SavedState.writeToParcel (Android.OS.ParcelableWriteFlags flags)
  when bound in C#.
  
  <mapping jni-class="android/support/v4/app/Fragment.SavedState">
    <method jni-name="writeToParcel" parameter="flags" clr-enum-type="Android.OS.ParcelableWriteFlags" />
  </mapping>
  -->
</enum-method-mappings>