﻿using System;
using System.Globalization;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Views;
using Android.Webkit;
using AppoMobi.CloudPayments;
using AppoMobi.CloudPayments.Models;
using AppoMobi.CloudPayments.Platform;
using AppoMobi.CloudPayments.Plugin;
using Java.Interop;
using Java.Net;
using Newtonsoft.Json.Linq;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using CookieManager = Android.Webkit.CookieManager;
using Object = Java.Lang.Object;


namespace AppoMobi.CloudPayments.Platform
{
    public class View3DSHandler : WebViewHandler, IView3DS
    {
        public static void Register()
        {
            Microsoft.Maui.Handlers.WebViewHandler.Mapper.AppendToMapping(nameof(View3DS), (handler, view) =>
            {
                if (view is View3DS view3ds)
                {
                    view3ds.Renderer = (IView3DS)handler;
                }
            });
        }

        protected override void ConnectHandler(Android.Webkit.WebView platformView)
        {
            base.ConnectHandler(platformView);

            if (VirtualView is View3DS formsControl)
            {
                FormsControl = formsControl;

                if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.Lollipop)
                {
                    CookieManager.Instance.SetAcceptCookie(true);
                    CookieManager.Instance.SetAcceptThirdPartyCookies(platformView, true);
                }

                platformView.SetWebViewClient(new CustomWebVew(this));

                if (FormsControl.BackgroundColor != Colors.Transparent)
                    platformView.SetBackgroundColor(FormsControl.BackgroundColor.ToPlatform());

                FormsControl.Renderer = this;

                platformView.Settings.SetSupportZoom(true);
            }
        }

        public View3DS FormsControl { get; set; }

        public void Post(string url, string body)
        {
            Console.WriteLine($"[CloudPayments] 3DS\nurl: {url}\nbody: {body}");

            PlatformView.PostUrl(url, Encoding.UTF8.GetBytes(body));
        }


        protected override void DisconnectHandler(Android.Webkit.WebView platformView)
        {
            FormsControl?.Dispose();
            base.DisconnectHandler(platformView);
        }

    }



    public class CustomWebVew : Android.Webkit.WebViewClient
    {
        private WeakReference<View3DSHandler> _weakRenderer;

        public View3DSHandler Renderer
        {
            get
            {
                if (!_weakRenderer.TryGetTarget(out View3DSHandler value))
                {
                    return default;
                }

                return value;
            }
        }



        public CustomWebVew(View3DSHandler handler)
        {
            _weakRenderer = new WeakReference<View3DSHandler>(handler);
        }

        public override void OnPageStarted(Android.Webkit.WebView view, string url, Bitmap favicon)
        {
            System.Diagnostics.Debug.WriteLine("[CloudPayments] OnPageStarted: " + url);

            bool invisibleSet=false;
            if (url.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                //result page, we can hide it..
                if (Renderer.FormsControl.HideResult)
                {
                    view.Visibility = ViewStates.Invisible;
                    invisibleSet = true;
                }
            }

            if (!invisibleSet)
                view.Visibility = ViewStates.Visible;

            base.OnPageStarted(view, url, favicon);
        }

        public override void OnPageFinished(Android.Webkit.WebView view, string url)
        {
            base.OnPageFinished(view, url);

            System.Diagnostics.Debug.WriteLine("[CloudPayments] onPageFinished: " + url);

            if (url.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                //3DS complete
                var js =
                    "(function() { return (document.getElementsByTagName('html')[0].innerHTML); })();";
                var html = new Callback3DSResultReceived(Renderer.FormsControl);
                view.EvaluateJavascript(js, html);
            }
        }


    }

    class Callback3DSResultReceived : Java.Lang.Object, Android.Webkit.IValueCallback
    {
        private WeakReference<View3DS> _weakParent;

        public View3DS Parent
        {
            get
            {
                if (!_weakParent.TryGetTarget(out View3DS value))
                {
                    return default;
                }
                return value;
            }
        }


        public Callback3DSResultReceived(View3DS rendererFormsControl)
        {
            _weakParent = new WeakReference<View3DS>(rendererFormsControl);
        }

        public void OnReceiveValue(Object value)
        {
            var html = System.Text.RegularExpressions.Regex.Unescape((string)value);
            string json = GetStrBetweenTags(html, "<body>", "</body>");

            var jsonObj = JObject.Parse(json);
            System.Diagnostics.Debug.WriteLine($"[CloudPayments] {json}");

            //set default
            var result = new PayApiResponse<Post3dsRequestArgs>
            {
                Message = "3DS failed"
            };

            try
            {
                var model = new Post3dsRequestArgs
                {
                    TransactionId = (string)jsonObj["MD"],
                    PaymentResponse = (string)jsonObj["PaRes"]
                };

                if (!string.IsNullOrEmpty(model.PaymentResponse))
                {
                    result = new PayApiResponse<Post3dsRequestArgs>
                    {
                        Success = true,
                        Model = model
                    };
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            Device.BeginInvokeOnMainThread(() =>
            {
                Parent.Request3DSCallback?.Invoke(result);
            });
        }

        public string GetStrBetweenTags(string value,
            string startTag,
            string endTag)
        {
            if (value.Contains(startTag) && value.Contains(endTag))
            {
                int index = value.IndexOf(startTag) + startTag.Length;
                return value.Substring(index, value.IndexOf(endTag) - index);
            }
            else
                return null;
        }
    }

}