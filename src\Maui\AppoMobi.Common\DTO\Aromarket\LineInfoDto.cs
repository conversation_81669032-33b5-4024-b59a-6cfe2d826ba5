﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class LineInfoDto
    {
        [JsonProperty("sex")]
        public string Sex { get; set; }

        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("name_desc")]
        public string NameDesc { get; set; }

        [JsonProperty("brand_id")]
        public string BrandId { get; set; }

        [JsonProperty("brand_name")]
        public string BrandName { get; set; }

        [JsonProperty("brand_img")]
        public string BrandImage { get; set; }

        [JsonProperty("brand_img_big")]
        public string BrandImageLarge { get; set; }

        [JsonProperty("family")]
        public List<string> Family { get; set; }

        [JsonProperty("functions")]
        public object Functions { get; set; }

        [JsonProperty("section")]
        public string Section { get; set; }

        [JsonProperty("line")]
        public string Line { get; set; }

        [JsonProperty("made")]
        public string Made { get; set; }

        [JsonProperty("images")]
        public List<string> Images { get; set; }

        [JsonProperty("video")]
        public RemoteVideo Video { get; set; }

        [JsonProperty("year")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Year { get; set; }

        [JsonProperty("desc")]
        public string Desc { get; set; }

        [JsonProperty("notes")]
        public ParfumNotes Notes { get; set; }
    }
}