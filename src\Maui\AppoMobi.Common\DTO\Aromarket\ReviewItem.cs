﻿using System;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class ReviewItem
    {
        [JsonProperty("id")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("text")]
        public string Text { get; set; }

        [Json<PERSON>roperty("reply")]
        public string Reply { get; set; }

        [JsonConverter(typeof(SecondsEpochConverter))]
        [JsonProperty("time")]
        public DateTime Time { get; set; }

        [JsonProperty("city")]
        public string City { get; set; }
    }
}