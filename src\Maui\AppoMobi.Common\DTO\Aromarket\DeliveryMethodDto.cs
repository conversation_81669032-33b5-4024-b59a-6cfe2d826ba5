﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class DeliveryMethodDto : WithErrorDto
    {
        [JsonProperty("id")] public string Id { get; set; }

        [JsonProperty("name")] public string Name { get; set; }

        [JsonProperty("img")] public string Image { get; set; }

        [JsonProperty("items")] public List<DeliveryMethodItemDto> Items { get; set; }

    }
}