﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace AppoMobi.Common.DTO.Aromarket
{
    public class DictionaryCanBeNullConverter<TKey, TValue> : CustomCreationConverter<Dictionary<TKey, TValue>>
    {
        public override Dictionary<TKey, TValue> Create(Type objectType)
        {
            return new Dictionary<TKey, TValue>();
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue,
            JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.StartArray)
            {
                return serializer.Deserialize(reader, objectType);
            }

            if (reader.TokenType == JsonToken.StartObject)
            {
                serializer.Deserialize(reader); // NOTE : value must be consumed otherwise an exception will be thrown
                return null;
            }

            throw new NotSupportedException("Should not occur, check J<PERSON><PERSON> for a new type of malformed syntax");
        }
    }
}