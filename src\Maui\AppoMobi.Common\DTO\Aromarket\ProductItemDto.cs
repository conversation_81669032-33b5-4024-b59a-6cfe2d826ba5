﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class ProductItemDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }


        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("desc")]
        public string Desc { get; set; }

        [JsonProperty("idline")]
        public string LineId { get; set; }

        [JsonProperty("idbrand")]
        public string BrandId { get; set; }

        //[JsonProperty("sex")]
        //public Sex Sex { get; set; }

        [JsonProperty("sex")]
        public string Sex { get; set; }

        [JsonProperty("volume")]
        public double Volume { get; set; }

        [JsonProperty("img")]
        public string Image { get; set; }

        [JsonProperty("price")]
        public double Price { get; set; }

        [JsonProperty("price2")]
        public double Price2 { get; set; }

        [JsonProperty("price3")]
        public double Price3 { get; set; }

        [JsonProperty("sprice")]
        public double Sprice { get; set; }

        [JsonProperty("available")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Available { get; set; }

        [JsonProperty("brand_name")]
        public string BrandName { get; set; }

        [JsonProperty("line_name")]
        public string Name { get; set; }

        [JsonProperty("root_catalog_id")]
        public string RootCatalogId { get; set; }
    }
}