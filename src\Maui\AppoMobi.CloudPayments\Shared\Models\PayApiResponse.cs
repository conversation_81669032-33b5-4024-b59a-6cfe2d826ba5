﻿namespace AppoMobi.CloudPayments.Models
{
    #region Возможные варианты ответа

    /*

     Возможные варианты ответа:

    Некорректно сформирован запрос:
    success — false
    message — описание ошибки

    Требуется 3-D Secure аутентификация (неприменимо для Apple Pay):
    success — false
    model — информация для проведения аутентификации
    
    Транзакция отклонена:
    success — false
    model — информация о транзакции и код ошибки
    
    Транзакция принята:
    success — true
    model — информация о транзакции

     */

    #endregion

    #region Пример ответа: некорректный запрос

    /*
     Пример ответа: некорректный запрос:

{"Success":false,"Message":"Amount is required"}

    */

    #endregion

    #region Пример ответа: требуется 3-D Secure аутентификация
    /*
     

    Пример ответа: требуется 3-D Secure аутентификация:

    {
        "Model": {
            "TransactionId": 504,
            "PaReq": "eJxVUdtugkAQ/RXDe9mLgo0Z1nhpU9PQasWmPhLYAKksuEChfn13uVR9mGTO7MzZM2dg3qSn0Q+X\nRZIJxyAmNkZcBFmYiMgxDt7zw6MxZ+DFkvP1ngeV5AxcXhR+xEdJ6BhpEZnEYLBdfPAzg56JKSKT\nAhqgGpFB7IuSgR+cl5s3NqFTG2NAPYSUy82aETqeWPYUUAdB+ClnwSmrwtz/TbkoC0BtDYKsEqX8\nZfZkDGgAUMkTi8synyFU17V5N2nKCpBuAHRVs610VijCJgmZu17UXTxhFWP34l7evYPlegsHkO6A\n0C85o5hMsI3piNIZHc+IBaitg59qJYzgdrUOQK7/WNy+3FZAeSqV5cMqAwLe5JlQwpny8T8HdFW8\netFuBqUyahV+Hjf27vWCaSx22fe+KY6kXKZfJLK1x22TZkyUS8QiHaUGgDQN6s+H+tOq7O7kf8hd\nt30=",
            "AcsUrl": "https://test.paymentgate.ru/acs/auth/start.do"
        },
        "Success": false,
        "Message": null
    }

*/
    #endregion

    #region Пример ответа: транзакция отклонена

    /*
    Пример ответа: транзакция отклонена. 
    В поле ReasonCode код ошибки (см. справочник):

{
    "Model": {
        "TransactionId": 504,
        "Amount": 10.00000,
        "Currency": "RUB",
        "CurrencyCode": 0,
        "PaymentAmount": 10.00000,
        "PaymentCurrency": "RUB",
        "PaymentCurrencyCode": 0,
        "InvoiceId": "1234567",
        "AccountId": "user_x",
        "Email": null,
        "Description": "Оплата товаров в example.com",
        "JsonData": null,
        "CreatedDate": "\/Date(*************)\/",
        "CreatedDateIso":"2014-08-09T11:49:41", //все даты в UTC
        "TestMode": true,
        "IpAddress": "*************",
        "IpCountry": "RU",
        "IpCity": "Уфа",
        "IpRegion": "Республика Башкортостан",
        "IpDistrict": "Приволжский федеральный округ",
        "IpLatitude": 54.7355,
        "IpLongitude": 55.991982,
        "CardFirstSix": "411111",
        "CardLastFour": "1111",
        "CardExpDate": "05/19",
        "CardType": "Visa",
        "CardTypeCode": 0,
        "Issuer": "Sberbank of Russia",
        "IssuerBankCountry": "RU",
        "Status": "Declined",
        "StatusCode": 5,
        "Reason": "InsufficientFunds", // причина отказа
        "ReasonCode": 5051, //код отказа
        "CardHolderMessage":"Недостаточно средств на карте", //сообщение для покупателя
        "Name": "CARDHOLDER NAME",
    },
    "Success": false,
    "Message": null
}

    */

    #endregion

    #region Пример ответа: транзакция принята

    /*

Пример ответа: транзакция принята:

{
    "Model": {
        "TransactionId": 504,
        "Amount": 10.00000,
        "Currency": "RUB",
        "CurrencyCode": 0,
        "InvoiceId": "1234567",
        "AccountId": "user_x",
        "Email": null,
        "Description": "Оплата товаров в example.com",
        "JsonData": null,
        "CreatedDate": "\/Date(*************)\/",
        "CreatedDateIso":"2014-08-09T11:49:41", //все даты в UTC
        "AuthDate": "\/Date(*************)\/",
        "AuthDateIso":"2014-08-09T11:49:42",
        "ConfirmDate": "\/Date(*************)\/",
        "ConfirmDateIso":"2014-08-09T11:49:42",
        "AuthCode": "123456",
        "TestMode": true,
        "IpAddress": "*************",
        "IpCountry": "RU",
        "IpCity": "Уфа",
        "IpRegion": "Республика Башкортостан",
        "IpDistrict": "Приволжский федеральный округ",
        "IpLatitude": 54.7355,
        "IpLongitude": 55.991982,
        "CardFirstSix": "411111",
        "CardLastFour": "1111",
        "CardExpDate": "05/19",
        "CardType": "Visa",
        "CardTypeCode": 0,
        "Issuer": "Sberbank of Russia",
        "IssuerBankCountry": "RU",
        "Status": "Completed",
        "StatusCode": 3,
        "Reason": "Approved",
        "ReasonCode": 0,
        "CardHolderMessage":"Оплата успешно проведена", //сообщение для покупателя
        "Name": "CARDHOLDER NAME",
        "Token": "a4e67841-abb0-42de-a364-d1d8f9f4b3c0"
    },
    "Success": true,
    "Message": null
}

     */


    #endregion

    // expect PayApiResponse<Transaction> ???

    public class PayApiResponse<T> where T : class
    {

        public bool Success { get; set; }

        public string Message { get; set; }

        public T Model { get; set; }


    }
}