<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <None Include="..\package.nuspec" Link="package.nuspec" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    <PackageReference Include="NuGet.CommandLine" Version="5.7.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Portable.BouncyCastle" Version="1.8.1.3" />
    <PackageReference Include="Xamarin.Forms" Version="4.8.0.1687" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="View3DS.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>

</Project>
