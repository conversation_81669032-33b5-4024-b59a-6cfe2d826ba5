﻿using System;
using System.Collections.Generic;
using System.Text;
using AppoMobi.Common.DTO.Shop;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class OrderPaymentFormDto : WithErrorDto
    {
        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("phone")]
        public long Phone { get; set; }

        [JsonProperty("product_count")]
        public long ProductCount { get; set; }

        [JsonProperty("cost")]
        public long Cost { get; set; }

        [JsonProperty("pay")]
        public long Pay { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("delivery")]
        public long Delivery { get; set; }

        [JsonProperty("pay_on_receipt")]
        public long PayOnReceipt { get; set; }

        [JsonProperty("payment_methods")]
        public Dictionary<string, FormPaymentMethod> PaymentMethods { get; set; }

    }
}