﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class OrderDeliveryMethodDto
    {
        [JsonProperty("key")]
        public string Key { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("price")]
        public decimal? Price { get; set; }

        [JsonProperty("prepayment")]
        public decimal? Prepayment { get; set; }

        [JsonProperty("min_order_summ")]
        public decimal? MinimumAmount { get; set; }
    }
}