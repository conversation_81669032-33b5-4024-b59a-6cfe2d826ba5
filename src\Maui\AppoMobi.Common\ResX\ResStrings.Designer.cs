﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AppoMobi.Common.ResX {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResStrings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResStrings() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("AppoMobi.Common.ResX.ResStrings", typeof(ResStrings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} characters left.
        /// </summary>
        public static string _0CharactersLeft {
            get {
                return ResourceManager.GetString("_0CharactersLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2 weeks.
        /// </summary>
        public static string _2Weeks {
            get {
                return ResourceManager.GetString("_2Weeks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  .
        /// </summary>
        public static string _Empty {
            get {
                return ResourceManager.GetString("_Empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About the Center.
        /// </summary>
        public static string AboutSalon {
            get {
                return ResourceManager.GetString("AboutSalon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About Us.
        /// </summary>
        public static string AboutTheCompany {
            get {
                return ResourceManager.GetString("AboutTheCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We have sent you an email to the following email address: {0}.
        ///Please follow the instructions in this email to complete the registration..
        /// </summary>
        public static string AccCeationConfirmEmail {
            get {
                return ResourceManager.GetString("AccCeationConfirmEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account creation.
        /// </summary>
        public static string AccCreationTitle {
            get {
                return ResourceManager.GetString("AccCreationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your account has not been yet assigned to an existing AppoMobi client. Please contact your tech support..
        /// </summary>
        public static string AccNotActiveForCLient {
            get {
                return ResourceManager.GetString("AccNotActiveForCLient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your reset link has expired..
        /// </summary>
        public static string AccountController_ResetPassword_InvalidToken {
            get {
                return ResourceManager.GetString("AccountController_ResetPassword_InvalidToken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action.
        /// </summary>
        public static string Action {
            get {
                return ResourceManager.GetString("Action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active.
        /// </summary>
        public static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active Orders.
        /// </summary>
        public static string ActiveOrders {
            get {
                return ResourceManager.GetString("ActiveOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Event.
        /// </summary>
        public static string AddEvent {
            get {
                return ResourceManager.GetString("AddEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add News.
        /// </summary>
        public static string AddNews {
            get {
                return ResourceManager.GetString("AddNews", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Region.
        /// </summary>
        public static string AddRegion {
            get {
                return ResourceManager.GetString("AddRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adress.
        /// </summary>
        public static string Adress {
            get {
                return ResourceManager.GetString("Adress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agree to &lt;strong&gt;the terms&lt;/strong&gt;.
        /// </summary>
        public static string AgreeToTerms {
            get {
                return ResourceManager.GetString("AgreeToTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        public static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All day.
        /// </summary>
        public static string AllDay {
            get {
                return ResourceManager.GetString("AllDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allowed.
        /// </summary>
        public static string Allowed {
            get {
                return ResourceManager.GetString("Allowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All products from category.
        /// </summary>
        public static string AllProductsHere {
            get {
                return ResourceManager.GetString("AllProductsHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Sorted By Distance.
        /// </summary>
        public static string AllSortedByDistance {
            get {
                return ResourceManager.GetString("AllSortedByDistance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Android.
        /// </summary>
        public static string Android {
            get {
                return ResourceManager.GetString("Android", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer.
        /// </summary>
        public static string Answer {
            get {
                return ResourceManager.GetString("Answer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answers.
        /// </summary>
        public static string Answers {
            get {
                return ResourceManager.GetString("Answers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appearence.
        /// </summary>
        public static string Appearence {
            get {
                return ResourceManager.GetString("Appearence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apple iOS.
        /// </summary>
        public static string AppleIOS {
            get {
                return ResourceManager.GetString("AppleIOS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color to display the calendar.
        /// </summary>
        public static string AppoColorDesc {
            get {
                return ResourceManager.GetString("AppoColorDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto-confirm bookings.
        /// </summary>
        public static string AppoConfirmAuto {
            get {
                return ResourceManager.GetString("AppoConfirmAuto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to auto/manual.
        /// </summary>
        public static string AppoConfirmAutoDesc {
            get {
                return ResourceManager.GetString("AppoConfirmAutoDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booking prohibited.
        /// </summary>
        public static string AppoController_Bookable_BlockDayForBooking {
            get {
                return ResourceManager.GetString("AppoController_Bookable_BlockDayForBooking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Explicit bookable time.
        /// </summary>
        public static string AppoExplicitBookable {
            get {
                return ResourceManager.GetString("AppoExplicitBookable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Whether bookable time should be indicated in bookable time for each object to be available.
        /// </summary>
        public static string AppoExplicitBookableDesc {
            get {
                return ResourceManager.GetString("AppoExplicitBookableDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Panel.
        /// </summary>
        public static string AppoMobiControlPanel {
            get {
                return ResourceManager.GetString("AppoMobiControlPanel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For the given conditions there is no available time. Try changing the conditions below:.
        /// </summary>
        public static string AppoNoTimeDesc {
            get {
                return ResourceManager.GetString("AppoNoTimeDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wildcard.
        /// </summary>
        public static string AppoObjectType_Any {
            get {
                return ResourceManager.GetString("AppoObjectType_Any", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Blocked.
        /// </summary>
        public static string AppoObjectType_Blocked {
            get {
                return ResourceManager.GetString("AppoObjectType_Blocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default.
        /// </summary>
        public static string AppoObjectType_Default {
            get {
                return ResourceManager.GetString("AppoObjectType_Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string AppoObjectType_System {
            get {
                return ResourceManager.GetString("AppoObjectType_System", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please await confirmation for {0}.
        /// </summary>
        public static string AppoTimeDescPending {
            get {
                return ResourceManager.GetString("AppoTimeDescPending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} will be waiting for You at {1}.
        /// </summary>
        public static string AppoTimeDescWho {
            get {
                return ResourceManager.GetString("AppoTimeDescWho", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strings.
        /// </summary>
        public static string AppStrings {
            get {
                return ResourceManager.GetString("AppStrings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sport Arena.
        /// </summary>
        public static string Arena {
            get {
                return ResourceManager.GetString("Arena", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arena Features.
        /// </summary>
        public static string ArenaFeatures {
            get {
                return ResourceManager.GetString("ArenaFeatures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Info.
        /// </summary>
        public static string ArenaFeaturesDesc {
            get {
                return ResourceManager.GetString("ArenaFeaturesDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sport Arenas.
        /// </summary>
        public static string Arenas {
            get {
                return ResourceManager.GetString("Arenas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arenas.
        /// </summary>
        public static string ArenasShort {
            get {
                return ResourceManager.GetString("ArenasShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are You sure?.
        /// </summary>
        public static string AreYouSure {
            get {
                return ResourceManager.GetString("AreYouSure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Really remove center from favorites?.
        /// </summary>
        public static string AreYouSureRemoveFromFavs {
            get {
                return ResourceManager.GetString("AreYouSureRemoveFromFavs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are You sure to delete.
        /// </summary>
        public static string AreYouSureToDelete {
            get {
                return ResourceManager.GetString("AreYouSureToDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure to delete this event ?.
        /// </summary>
        public static string AreYouSureToDeleteThisEvent {
            get {
                return ResourceManager.GetString("AreYouSureToDeleteThisEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Article.
        /// </summary>
        public static string Article {
            get {
                return ResourceManager.GetString("Article", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask for confirmation when removing items from lists.
        /// </summary>
        public static string AskForConfirmationWhenRemovingItemFromWishList {
            get {
                return ResourceManager.GetString("AskForConfirmationWhenRemovingItemFromWishList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do You want to hide this welcome message?.
        /// </summary>
        public static string AskHideWelcome {
            get {
                return ResourceManager.GetString("AskHideWelcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Associate your {0} account..
        /// </summary>
        public static string AssociateYourAccount {
            get {
                return ResourceManager.GetString("AssociateYourAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A user with this phone number was not found. Please register..
        /// </summary>
        public static string AUserWithThisPhoneNumberWasNotFoundPleaseRegister {
            get {
                return ResourceManager.GetString("AUserWithThisPhoneNumberWasNotFoundPleaseRegister", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Code.
        /// </summary>
        public static string Auth_CountryCode {
            get {
                return ResourceManager.GetString("Auth_CountryCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Didn&apos;t get the code?.
        /// </summary>
        public static string Auth_DidnTGetTheCode {
            get {
                return ResourceManager.GetString("Auth_DidnTGetTheCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We have sent an SMS code to your phone {0}.
        /// </summary>
        public static string Auth_EnterSmsCodeTitle {
            get {
                return ResourceManager.GetString("Auth_EnterSmsCodeTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect email or password.
        /// </summary>
        public static string Auth_IncorrectEmailOrPassword {
            get {
                return ResourceManager.GetString("Auth_IncorrectEmailOrPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logged off!.
        /// </summary>
        public static string Auth_LoggedOff {
            get {
                return ResourceManager.GetString("Auth_LoggedOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successfully logged in.
        /// </summary>
        public static string Auth_LoginSuccess {
            get {
                return ResourceManager.GetString("Auth_LoginSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign In With Your Phone Number.
        /// </summary>
        public static string Auth_LoginUsingPhone {
            get {
                return ResourceManager.GetString("Auth_LoginUsingPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Off.
        /// </summary>
        public static string Auth_LogOff {
            get {
                return ResourceManager.GetString("Auth_LogOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to log off? You will be able to sign in again later on any device using same account..
        /// </summary>
        public static string Auth_LogoffConfirmation {
            get {
                return ResourceManager.GetString("Auth_LogoffConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Profile.
        /// </summary>
        public static string Auth_MyProfile {
            get {
                return ResourceManager.GetString("Auth_MyProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal Data.
        /// </summary>
        public static string Auth_PersonalData {
            get {
                return ResourceManager.GetString("Auth_PersonalData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We have sent you another SMS, please check your messages..
        /// </summary>
        public static string Auth_WeHaveSentYouAnotherSMS {
            get {
                return ResourceManager.GetString("Auth_WeHaveSentYouAnotherSMS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authenticate.
        /// </summary>
        public static string Auth_XamarinAuthTitle {
            get {
                return ResourceManager.GetString("Auth_XamarinAuthTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authenticating...
        /// </summary>
        public static string Authenticating {
            get {
                return ResourceManager.GetString("Authenticating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Author.
        /// </summary>
        public static string Author {
            get {
                return ResourceManager.GetString("Author", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Seats.
        /// </summary>
        public static string AvailableSeats {
            get {
                return ResourceManager.GetString("AvailableSeats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go Back.
        /// </summary>
        public static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Catalog.
        /// </summary>
        public static string BackToCatalog {
            get {
                return ResourceManager.GetString("BackToCatalog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back to List.
        /// </summary>
        public static string BackToList {
            get {
                return ResourceManager.GetString("BackToList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go Back To Salon List.
        /// </summary>
        public static string BackToSalonList {
            get {
                return ResourceManager.GetString("BackToSalonList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The username or password you entered is incorrect..
        /// </summary>
        public static string BadUsernameOrPassword {
            get {
                return ResourceManager.GetString("BadUsernameOrPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export completed with success..
        /// </summary>
        public static string BaseControllerContent__IndexGet_ExportCompletedWithSuccess {
            get {
                return ResourceManager.GetString("BaseControllerContent__IndexGet_ExportCompletedWithSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rally.
        /// </summary>
        public static string BaseControllerContent_GenerateDropDowns_Rally {
            get {
                return ResourceManager.GetString("BaseControllerContent_GenerateDropDowns_Rally", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Base URL.
        /// </summary>
        public static string BaseURL {
            get {
                return ResourceManager.GetString("BaseURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of Birth.
        /// </summary>
        public static string BirthDate {
            get {
                return ResourceManager.GetString("BirthDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Blog.
        /// </summary>
        public static string Blog {
            get {
                return ResourceManager.GetString("Blog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Body.
        /// </summary>
        public static string Body {
            get {
                return ResourceManager.GetString("Body", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Present for booking.
        /// </summary>
        public static string Bookable {
            get {
                return ResourceManager.GetString("Bookable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will be visible to customers for booking in the app.
        /// </summary>
        public static string BookableHint {
            get {
                return ResourceManager.GetString("BookableHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} at {1}.
        /// </summary>
        public static string BookingDateTimeDesc {
            get {
                return ResourceManager.GetString("BookingDateTimeDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to we&apos;ll be waiting for You at {0}.
        /// </summary>
        public static string BookingDateTimeDescFormat {
            get {
                return ResourceManager.GetString("BookingDateTimeDescFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booking failed..
        /// </summary>
        public static string BookingFailed {
            get {
                return ResourceManager.GetString("BookingFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booking failed. Maybe someone has already taken that time, please retry..
        /// </summary>
        public static string BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry {
            get {
                return ResourceManager.GetString("BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Front Desk.
        /// </summary>
        public static string BookingFrontDesk {
            get {
                return ResourceManager.GetString("BookingFrontDesk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Canceled.
        /// </summary>
        public static string BookingFrontDeskStatusType_Canceled {
            get {
                return ResourceManager.GetString("BookingFrontDeskStatusType_Canceled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed.
        /// </summary>
        public static string BookingFrontDeskStatusType_Confirmed {
            get {
                return ResourceManager.GetString("BookingFrontDeskStatusType_Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending.
        /// </summary>
        public static string BookingFrontDeskStatusType_Pending {
            get {
                return ResourceManager.GetString("BookingFrontDeskStatusType_Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booking Objects.
        /// </summary>
        public static string BookingObjects {
            get {
                return ResourceManager.GetString("BookingObjects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Objects.
        /// </summary>
        public static string BookingObjectsShort {
            get {
                return ResourceManager.GetString("BookingObjectsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booking Request.
        /// </summary>
        public static string BookingRequest {
            get {
                return ResourceManager.GetString("BookingRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booking Requests.
        /// </summary>
        public static string BookingRequests {
            get {
                return ResourceManager.GetString("BookingRequests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working Schedule.
        /// </summary>
        public static string BookingSchedule {
            get {
                return ResourceManager.GetString("BookingSchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BookingStatus_Archived.
        /// </summary>
        public static string BookingStatus_Archived {
            get {
                return ResourceManager.GetString("BookingStatus_Archived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BookingStatus_Confirmed.
        /// </summary>
        public static string BookingStatus_Confirmed {
            get {
                return ResourceManager.GetString("BookingStatus_Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BookingStatus_Pending.
        /// </summary>
        public static string BookingStatus_Pending {
            get {
                return ResourceManager.GetString("BookingStatus_Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BookingStatus_Rejected.
        /// </summary>
        public static string BookingStatus_Rejected {
            get {
                return ResourceManager.GetString("BookingStatus_Rejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown.
        /// </summary>
        public static string BookingStatus_Unknown {
            get {
                return ResourceManager.GetString("BookingStatus_Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booking System.
        /// </summary>
        public static string BookingSystem {
            get {
                return ResourceManager.GetString("BookingSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At {0}.
        /// </summary>
        public static string BookingTimeDescAt {
            get {
                return ResourceManager.GetString("BookingTimeDescAt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Book Online.
        /// </summary>
        public static string BookOnline {
            get {
                return ResourceManager.GetString("BookOnline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brand.
        /// </summary>
        public static string Brand {
            get {
                return ResourceManager.GetString("Brand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brands.
        /// </summary>
        public static string Brands {
            get {
                return ResourceManager.GetString("Brands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Website.
        /// </summary>
        public static string BrowseSite {
            get {
                return ResourceManager.GetString("BrowseSite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System Settings.
        /// </summary>
        public static string BtnAppSettings {
            get {
                return ResourceManager.GetString("BtnAppSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Book.
        /// </summary>
        public static string btnBook {
            get {
                return ResourceManager.GetString("btnBook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Book Now!.
        /// </summary>
        public static string BtnBookNow {
            get {
                return ResourceManager.GetString("BtnBookNow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string BtnCancel {
            get {
                return ResourceManager.GetString("BtnCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel Order.
        /// </summary>
        public static string BtnCancelOrder {
            get {
                return ResourceManager.GetString("BtnCancelOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check Settings.
        /// </summary>
        public static string btnCheckSettings {
            get {
                return ResourceManager.GetString("btnCheckSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string BtnClose {
            get {
                return ResourceManager.GetString("BtnClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CONFIRM AND PAY.
        /// </summary>
        public static string BtnConfirmPay {
            get {
                return ResourceManager.GetString("BtnConfirmPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string BtnCreate {
            get {
                return ResourceManager.GetString("BtnCreate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string BtnDelete {
            get {
                return ResourceManager.GetString("BtnDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Selected.
        /// </summary>
        public static string BtnDeleteSelected {
            get {
                return ResourceManager.GetString("BtnDeleteSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details.
        /// </summary>
        public static string BtnDetails {
            get {
                return ResourceManager.GetString("BtnDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        public static string BtnEdit {
            get {
                return ResourceManager.GetString("BtnEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Feedback.
        /// </summary>
        public static string BtnEditFeedback {
            get {
                return ResourceManager.GetString("BtnEditFeedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go Back.
        /// </summary>
        public static string BtnGoBack {
            get {
                return ResourceManager.GetString("BtnGoBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Leave Feedback.
        /// </summary>
        public static string BtnLeaveFeedback {
            get {
                return ResourceManager.GetString("BtnLeaveFeedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log In.
        /// </summary>
        public static string BtnLogIn {
            get {
                return ResourceManager.GetString("BtnLogIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string BtnOk {
            get {
                return ResourceManager.GetString("BtnOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ORDER NOW.
        /// </summary>
        public static string BtnOrderNow {
            get {
                return ResourceManager.GetString("BtnOrderNow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay Now.
        /// </summary>
        public static string BtnPayNow {
            get {
                return ResourceManager.GetString("BtnPayNow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register.
        /// </summary>
        public static string BtnRegister {
            get {
                return ResourceManager.GetString("BtnRegister", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        public static string BtnReset {
            get {
                return ResourceManager.GetString("BtnReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        public static string BtnResetPassword {
            get {
                return ResourceManager.GetString("BtnResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string BtnSave {
            get {
                return ResourceManager.GetString("BtnSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Try Again.
        /// </summary>
        public static string btnTryAgain {
            get {
                return ResourceManager.GetString("btnTryAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add to favorites.
        /// </summary>
        public static string ButtonAddToFavs {
            get {
                return ResourceManager.GetString("ButtonAddToFavs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string ButtonCancel {
            get {
                return ResourceManager.GetString("ButtonCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reconnect.
        /// </summary>
        public static string ButtonConnect {
            get {
                return ResourceManager.GetString("ButtonConnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Find Your Salon.
        /// </summary>
        public static string ButtonFindYourSalon {
            get {
                return ResourceManager.GetString("ButtonFindYourSalon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string ButtonGotIt {
            get {
                return ResourceManager.GetString("ButtonGotIt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instructions.
        /// </summary>
        public static string ButtonHowToGetToUs {
            get {
                return ResourceManager.GetString("ButtonHowToGetToUs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Later.
        /// </summary>
        public static string ButtonLater {
            get {
                return ResourceManager.GetString("ButtonLater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Find Route.
        /// </summary>
        public static string ButtonNavigate {
            get {
                return ResourceManager.GetString("ButtonNavigate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string ButtonOk {
            get {
                return ResourceManager.GetString("ButtonOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partners Login Here.
        /// </summary>
        public static string ButtonProPartners {
            get {
                return ResourceManager.GetString("ButtonProPartners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info For Specialists.
        /// </summary>
        public static string ButtonProPpl {
            get {
                return ResourceManager.GetString("ButtonProPpl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info For Centers.
        /// </summary>
        public static string ButtonProSalons {
            get {
                return ResourceManager.GetString("ButtonProSalons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Region.
        /// </summary>
        public static string ButtonRegionChange {
            get {
                return ResourceManager.GetString("ButtonRegionChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to by.
        /// </summary>
        public static string By {
            get {
                return ResourceManager.GetString("By", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By days of week.
        /// </summary>
        public static string ByDaysOfWeek {
            get {
                return ResourceManager.GetString("ByDaysOfWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to See You Soon!.
        /// </summary>
        public static string Bye {
            get {
                return ResourceManager.GetString("Bye", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CachePaymentsProhibited.
        /// </summary>
        public static string CachePaymentsProhibited {
            get {
                return ResourceManager.GetString("CachePaymentsProhibited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CachPaymentsAutoAuthorized.
        /// </summary>
        public static string CachPaymentsAutoAuthorized {
            get {
                return ResourceManager.GetString("CachPaymentsAutoAuthorized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call.
        /// </summary>
        public static string Call {
            get {
                return ResourceManager.GetString("Call", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Us.
        /// </summary>
        public static string CallUs {
            get {
                return ResourceManager.GetString("CallUs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Canceled.
        /// </summary>
        public static string Canceled {
            get {
                return ResourceManager.GetString("Canceled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove item from wish list?.
        /// </summary>
        public static string CardProductFull_Fav_OnDown_ConfirmFavDelete {
            get {
                return ResourceManager.GetString("CardProductFull_Fav_OnDown_ConfirmFavDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ref..
        /// </summary>
        public static string CardProductFull_SetupCell_Ref {
            get {
                return ResourceManager.GetString("CardProductFull_SetupCell_Ref", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Categories.
        /// </summary>
        public static string Categories {
            get {
                return ResourceManager.GetString("Categories", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category.
        /// </summary>
        public static string Category {
            get {
                return ResourceManager.GetString("Category", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to News Slider.
        /// </summary>
        public static string CatNewsSlider {
            get {
                return ResourceManager.GetString("CatNewsSlider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Catalog.
        /// </summary>
        public static string CatRoot {
            get {
                return ResourceManager.GetString("CatRoot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Base Category 2.
        /// </summary>
        public static string CatRoot2 {
            get {
                return ResourceManager.GetString("CatRoot2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secondary Base Category.
        /// </summary>
        public static string CatSecRoot {
            get {
                return ResourceManager.GetString("CatSecRoot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Centers.
        /// </summary>
        public static string Centers {
            get {
                return ResourceManager.GetString("Centers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AppoMobi is a worldwide provider for business mobile applications solutions..
        /// </summary>
        public static string CEOSiteDesc {
            get {
                return ResourceManager.GetString("CEOSiteDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Championship.
        /// </summary>
        public static string Championship {
            get {
                return ResourceManager.GetString("Championship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change.
        /// </summary>
        public static string Change {
            get {
                return ResourceManager.GetString("Change", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changes saved.
        /// </summary>
        public static string ChangesSaved {
            get {
                return ResourceManager.GetString("ChangesSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change your password.
        /// </summary>
        public static string ChangeYourPassword {
            get {
                return ResourceManager.GetString("ChangeYourPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        public static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Class.
        /// </summary>
        public static string Class {
            get {
                return ResourceManager.GetString("Class", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear List.
        /// </summary>
        public static string ClearList {
            get {
                return ResourceManager.GetString("ClearList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click here to Log in.
        /// </summary>
        public static string ClickHereToLogIn {
            get {
                return ResourceManager.GetString("ClickHereToLogIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clicks Total.
        /// </summary>
        public static string Clicks {
            get {
                return ResourceManager.GetString("Clicks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click to upload or drop file here...
        /// </summary>
        public static string ClickToUploadOrDropFileHere {
            get {
                return ResourceManager.GetString("ClickToUploadOrDropFileHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client.
        /// </summary>
        public static string Client {
            get {
                return ResourceManager.GetString("Client", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Control Panel.
        /// </summary>
        public static string ClientControlPanel {
            get {
                return ResourceManager.GetString("ClientControlPanel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Global Settings.
        /// </summary>
        public static string ClientGlobalSettings {
            get {
                return ResourceManager.GetString("ClientGlobalSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Id.
        /// </summary>
        public static string ClientId {
            get {
                return ResourceManager.GetString("ClientId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closest Date.
        /// </summary>
        public static string ClosestDate {
            get {
                return ResourceManager.GetString("ClosestDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coaches.
        /// </summary>
        public static string Coaches {
            get {
                return ResourceManager.GetString("Coaches", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code.
        /// </summary>
        public static string Code {
            get {
                return ResourceManager.GetString("Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code From SMS.
        /// </summary>
        public static string CodeFromSMS {
            get {
                return ResourceManager.GetString("CodeFromSMS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color.
        /// </summary>
        public static string Color {
            get {
                return ResourceManager.GetString("Color", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Info.
        /// </summary>
        public static string CompanyInfo {
            get {
                return ResourceManager.GetString("CompanyInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Image.
        /// </summary>
        public static string CompanyLogo {
            get {
                return ResourceManager.GetString("CompanyLogo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conditions.
        /// </summary>
        public static string Conditions {
            get {
                return ResourceManager.GetString("Conditions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to confirmation pending.
        /// </summary>
        public static string ConfirmationPending {
            get {
                return ResourceManager.GetString("ConfirmationPending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation pending.
        /// </summary>
        public static string ConfirmationPendingTitle {
            get {
                return ResourceManager.GetString("ConfirmationPendingTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this?.
        /// </summary>
        public static string ConfirmDelete {
            get {
                return ResourceManager.GetString("ConfirmDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to export this?.
        /// </summary>
        public static string ConfirmExport {
            get {
                return ResourceManager.GetString("ConfirmExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Password.
        /// </summary>
        public static string ConfirmPassword {
            get {
                return ResourceManager.GetString("ConfirmPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to confirned.
        /// </summary>
        public static string Confirned {
            get {
                return ResourceManager.GetString("Confirned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection error.
        /// </summary>
        public static string ConnectionError {
            get {
                return ResourceManager.GetString("ConnectionError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect With.
        /// </summary>
        public static string ConnectWith {
            get {
                return ResourceManager.GetString("ConnectWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        public static string Contacts {
            get {
                return ResourceManager.GetString("Contacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Us.
        /// </summary>
        public static string ContactUs {
            get {
                return ResourceManager.GetString("ContactUs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Us.
        /// </summary>
        public static string ContactUs2 {
            get {
                return ResourceManager.GetString("ContactUs2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Us :.
        /// </summary>
        public static string ContactUsL {
            get {
                return ResourceManager.GetString("ContactUsL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contains.
        /// </summary>
        public static string Contains {
            get {
                return ResourceManager.GetString("Contains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Content Languages.
        /// </summary>
        public static string ContentLanguages {
            get {
                return ResourceManager.GetString("ContentLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Panel.
        /// </summary>
        public static string ControlPanel {
            get {
                return ResourceManager.GetString("ControlPanel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;strong&gt;AppoMobi&lt;/strong&gt; Control Panel.
        /// </summary>
        public static string ControlPanelHtml {
            get {
                return ResourceManager.GetString("ControlPanelHtml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conversion.
        /// </summary>
        public static string Conversion {
            get {
                return ResourceManager.GetString("Conversion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct.
        /// </summary>
        public static string Correct {
            get {
                return ResourceManager.GetString("Correct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct answers percent.
        /// </summary>
        public static string CorrectAnswersPercent {
            get {
                return ResourceManager.GetString("CorrectAnswersPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        public static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Andorra.
        /// </summary>
        public static string Country_AD {
            get {
                return ResourceManager.GetString("Country_AD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to United Arab Emirates.
        /// </summary>
        public static string Country_AE {
            get {
                return ResourceManager.GetString("Country_AE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Afghanistan.
        /// </summary>
        public static string Country_AF {
            get {
                return ResourceManager.GetString("Country_AF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Antigua and Barbuda.
        /// </summary>
        public static string Country_AG {
            get {
                return ResourceManager.GetString("Country_AG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anguilla.
        /// </summary>
        public static string Country_AI {
            get {
                return ResourceManager.GetString("Country_AI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Albania.
        /// </summary>
        public static string Country_AL {
            get {
                return ResourceManager.GetString("Country_AL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Armenia.
        /// </summary>
        public static string Country_AM {
            get {
                return ResourceManager.GetString("Country_AM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Netherlands Antilles.
        /// </summary>
        public static string Country_AN {
            get {
                return ResourceManager.GetString("Country_AN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Angola.
        /// </summary>
        public static string Country_AO {
            get {
                return ResourceManager.GetString("Country_AO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Antarctica.
        /// </summary>
        public static string Country_AQ {
            get {
                return ResourceManager.GetString("Country_AQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Argentina.
        /// </summary>
        public static string Country_AR {
            get {
                return ResourceManager.GetString("Country_AR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to American Samoa.
        /// </summary>
        public static string Country_AS {
            get {
                return ResourceManager.GetString("Country_AS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Austria.
        /// </summary>
        public static string Country_AT {
            get {
                return ResourceManager.GetString("Country_AT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Australia.
        /// </summary>
        public static string Country_AU {
            get {
                return ResourceManager.GetString("Country_AU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aruba.
        /// </summary>
        public static string Country_AW {
            get {
                return ResourceManager.GetString("Country_AW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azerbaijan.
        /// </summary>
        public static string Country_AZ {
            get {
                return ResourceManager.GetString("Country_AZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bosnia and Herzegovina.
        /// </summary>
        public static string Country_BA {
            get {
                return ResourceManager.GetString("Country_BA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barbados.
        /// </summary>
        public static string Country_BB {
            get {
                return ResourceManager.GetString("Country_BB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bangladesh.
        /// </summary>
        public static string Country_BD {
            get {
                return ResourceManager.GetString("Country_BD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Belgium.
        /// </summary>
        public static string Country_BE {
            get {
                return ResourceManager.GetString("Country_BE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Burkina Faso.
        /// </summary>
        public static string Country_BF {
            get {
                return ResourceManager.GetString("Country_BF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulgaria.
        /// </summary>
        public static string Country_BG {
            get {
                return ResourceManager.GetString("Country_BG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bahrain.
        /// </summary>
        public static string Country_BH {
            get {
                return ResourceManager.GetString("Country_BH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Burundi.
        /// </summary>
        public static string Country_BI {
            get {
                return ResourceManager.GetString("Country_BI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Benin.
        /// </summary>
        public static string Country_BJ {
            get {
                return ResourceManager.GetString("Country_BJ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bermuda.
        /// </summary>
        public static string Country_BM {
            get {
                return ResourceManager.GetString("Country_BM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brunei.
        /// </summary>
        public static string Country_BN {
            get {
                return ResourceManager.GetString("Country_BN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bolivia.
        /// </summary>
        public static string Country_BO {
            get {
                return ResourceManager.GetString("Country_BO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brazil.
        /// </summary>
        public static string Country_BR {
            get {
                return ResourceManager.GetString("Country_BR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Bahamas.
        /// </summary>
        public static string Country_BS {
            get {
                return ResourceManager.GetString("Country_BS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bhutan.
        /// </summary>
        public static string Country_BT {
            get {
                return ResourceManager.GetString("Country_BT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bouvet Island.
        /// </summary>
        public static string Country_BV {
            get {
                return ResourceManager.GetString("Country_BV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Botswana.
        /// </summary>
        public static string Country_BW {
            get {
                return ResourceManager.GetString("Country_BW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Belarus.
        /// </summary>
        public static string Country_BY {
            get {
                return ResourceManager.GetString("Country_BY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Belize.
        /// </summary>
        public static string Country_BZ {
            get {
                return ResourceManager.GetString("Country_BZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Canada.
        /// </summary>
        public static string Country_CA {
            get {
                return ResourceManager.GetString("Country_CA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cocos (Keeling) Islands.
        /// </summary>
        public static string Country_CC {
            get {
                return ResourceManager.GetString("Country_CC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Democratic Republic of the Congo.
        /// </summary>
        public static string Country_CD {
            get {
                return ResourceManager.GetString("Country_CD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Central African Republic.
        /// </summary>
        public static string Country_CF {
            get {
                return ResourceManager.GetString("Country_CF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Congo.
        /// </summary>
        public static string Country_CG {
            get {
                return ResourceManager.GetString("Country_CG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switzerland.
        /// </summary>
        public static string Country_CH {
            get {
                return ResourceManager.GetString("Country_CH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Côte d&apos;Ivoire.
        /// </summary>
        public static string Country_CI {
            get {
                return ResourceManager.GetString("Country_CI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cook Islands.
        /// </summary>
        public static string Country_CK {
            get {
                return ResourceManager.GetString("Country_CK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chile.
        /// </summary>
        public static string Country_CL {
            get {
                return ResourceManager.GetString("Country_CL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cameroon.
        /// </summary>
        public static string Country_CM {
            get {
                return ResourceManager.GetString("Country_CM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to China.
        /// </summary>
        public static string Country_CN {
            get {
                return ResourceManager.GetString("Country_CN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Colombia.
        /// </summary>
        public static string Country_CO {
            get {
                return ResourceManager.GetString("Country_CO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Costa Rica.
        /// </summary>
        public static string Country_CR {
            get {
                return ResourceManager.GetString("Country_CR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cuba.
        /// </summary>
        public static string Country_CU {
            get {
                return ResourceManager.GetString("Country_CU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cape Verde.
        /// </summary>
        public static string Country_CV {
            get {
                return ResourceManager.GetString("Country_CV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Christmas Island.
        /// </summary>
        public static string Country_CX {
            get {
                return ResourceManager.GetString("Country_CX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cyprus.
        /// </summary>
        public static string Country_CY {
            get {
                return ResourceManager.GetString("Country_CY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Czech Republic.
        /// </summary>
        public static string Country_CZ {
            get {
                return ResourceManager.GetString("Country_CZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Germany.
        /// </summary>
        public static string Country_DE {
            get {
                return ResourceManager.GetString("Country_DE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Djibouti.
        /// </summary>
        public static string Country_DJ {
            get {
                return ResourceManager.GetString("Country_DJ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Denmark.
        /// </summary>
        public static string Country_DK {
            get {
                return ResourceManager.GetString("Country_DK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dominica.
        /// </summary>
        public static string Country_DM {
            get {
                return ResourceManager.GetString("Country_DM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dominican Republic.
        /// </summary>
        public static string Country_DO {
            get {
                return ResourceManager.GetString("Country_DO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Algeria.
        /// </summary>
        public static string Country_DZ {
            get {
                return ResourceManager.GetString("Country_DZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ecuador.
        /// </summary>
        public static string Country_EC {
            get {
                return ResourceManager.GetString("Country_EC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estonia.
        /// </summary>
        public static string Country_EE {
            get {
                return ResourceManager.GetString("Country_EE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Egypt.
        /// </summary>
        public static string Country_EG {
            get {
                return ResourceManager.GetString("Country_EG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Western Sahara.
        /// </summary>
        public static string Country_EH {
            get {
                return ResourceManager.GetString("Country_EH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eritrea.
        /// </summary>
        public static string Country_ER {
            get {
                return ResourceManager.GetString("Country_ER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spain.
        /// </summary>
        public static string Country_ES {
            get {
                return ResourceManager.GetString("Country_ES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ethiopia.
        /// </summary>
        public static string Country_ET {
            get {
                return ResourceManager.GetString("Country_ET", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finland.
        /// </summary>
        public static string Country_FI {
            get {
                return ResourceManager.GetString("Country_FI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fiji.
        /// </summary>
        public static string Country_FJ {
            get {
                return ResourceManager.GetString("Country_FJ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Falkland Islands.
        /// </summary>
        public static string Country_FK {
            get {
                return ResourceManager.GetString("Country_FK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Micronesia.
        /// </summary>
        public static string Country_FM {
            get {
                return ResourceManager.GetString("Country_FM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Faeroe Islands.
        /// </summary>
        public static string Country_FO {
            get {
                return ResourceManager.GetString("Country_FO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to France.
        /// </summary>
        public static string Country_FR {
            get {
                return ResourceManager.GetString("Country_FR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gabon.
        /// </summary>
        public static string Country_GA {
            get {
                return ResourceManager.GetString("Country_GA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to United Kingdom.
        /// </summary>
        public static string Country_GB {
            get {
                return ResourceManager.GetString("Country_GB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grenada.
        /// </summary>
        public static string Country_GD {
            get {
                return ResourceManager.GetString("Country_GD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Georgia.
        /// </summary>
        public static string Country_GE {
            get {
                return ResourceManager.GetString("Country_GE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French Guiana.
        /// </summary>
        public static string Country_GF {
            get {
                return ResourceManager.GetString("Country_GF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghana.
        /// </summary>
        public static string Country_GH {
            get {
                return ResourceManager.GetString("Country_GH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gibraltar.
        /// </summary>
        public static string Country_GI {
            get {
                return ResourceManager.GetString("Country_GI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Greenland.
        /// </summary>
        public static string Country_GL {
            get {
                return ResourceManager.GetString("Country_GL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Gambia.
        /// </summary>
        public static string Country_GM {
            get {
                return ResourceManager.GetString("Country_GM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guinea.
        /// </summary>
        public static string Country_GN {
            get {
                return ResourceManager.GetString("Country_GN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guadeloupe.
        /// </summary>
        public static string Country_GP {
            get {
                return ResourceManager.GetString("Country_GP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Equatorial Guinea.
        /// </summary>
        public static string Country_GQ {
            get {
                return ResourceManager.GetString("Country_GQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Greece.
        /// </summary>
        public static string Country_GR {
            get {
                return ResourceManager.GetString("Country_GR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to South Georgia and the South Sandwich Islands.
        /// </summary>
        public static string Country_GS {
            get {
                return ResourceManager.GetString("Country_GS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guatemala.
        /// </summary>
        public static string Country_GT {
            get {
                return ResourceManager.GetString("Country_GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guam.
        /// </summary>
        public static string Country_GU {
            get {
                return ResourceManager.GetString("Country_GU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guinea-Bissau.
        /// </summary>
        public static string Country_GW {
            get {
                return ResourceManager.GetString("Country_GW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guyana.
        /// </summary>
        public static string Country_GY {
            get {
                return ResourceManager.GetString("Country_GY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hong Kong.
        /// </summary>
        public static string Country_HK {
            get {
                return ResourceManager.GetString("Country_HK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heard Island and McDonald Islands.
        /// </summary>
        public static string Country_HM {
            get {
                return ResourceManager.GetString("Country_HM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Honduras.
        /// </summary>
        public static string Country_HN {
            get {
                return ResourceManager.GetString("Country_HN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Croatia.
        /// </summary>
        public static string Country_HR {
            get {
                return ResourceManager.GetString("Country_HR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Haiti.
        /// </summary>
        public static string Country_HT {
            get {
                return ResourceManager.GetString("Country_HT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hungary.
        /// </summary>
        public static string Country_HU {
            get {
                return ResourceManager.GetString("Country_HU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indonesia.
        /// </summary>
        public static string Country_ID {
            get {
                return ResourceManager.GetString("Country_ID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ireland.
        /// </summary>
        public static string Country_IE {
            get {
                return ResourceManager.GetString("Country_IE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Israel.
        /// </summary>
        public static string Country_IL {
            get {
                return ResourceManager.GetString("Country_IL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to India.
        /// </summary>
        public static string Country_IN {
            get {
                return ResourceManager.GetString("Country_IN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to British Indian Ocean Territory.
        /// </summary>
        public static string Country_IO {
            get {
                return ResourceManager.GetString("Country_IO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Iraq.
        /// </summary>
        public static string Country_IQ {
            get {
                return ResourceManager.GetString("Country_IQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Iran.
        /// </summary>
        public static string Country_IR {
            get {
                return ResourceManager.GetString("Country_IR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Iceland.
        /// </summary>
        public static string Country_IS {
            get {
                return ResourceManager.GetString("Country_IS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Italy.
        /// </summary>
        public static string Country_IT {
            get {
                return ResourceManager.GetString("Country_IT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jamaica.
        /// </summary>
        public static string Country_JM {
            get {
                return ResourceManager.GetString("Country_JM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jordan.
        /// </summary>
        public static string Country_JO {
            get {
                return ResourceManager.GetString("Country_JO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Japan.
        /// </summary>
        public static string Country_JP {
            get {
                return ResourceManager.GetString("Country_JP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kenya.
        /// </summary>
        public static string Country_KE {
            get {
                return ResourceManager.GetString("Country_KE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kyrgyzstan.
        /// </summary>
        public static string Country_KG {
            get {
                return ResourceManager.GetString("Country_KG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cambodia.
        /// </summary>
        public static string Country_KH {
            get {
                return ResourceManager.GetString("Country_KH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kiribati.
        /// </summary>
        public static string Country_KI {
            get {
                return ResourceManager.GetString("Country_KI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comoros.
        /// </summary>
        public static string Country_KM {
            get {
                return ResourceManager.GetString("Country_KM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saint Kitts and Nevis.
        /// </summary>
        public static string Country_KN {
            get {
                return ResourceManager.GetString("Country_KN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to North Korea.
        /// </summary>
        public static string Country_KP {
            get {
                return ResourceManager.GetString("Country_KP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to South Korea.
        /// </summary>
        public static string Country_KR {
            get {
                return ResourceManager.GetString("Country_KR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kuwait.
        /// </summary>
        public static string Country_KW {
            get {
                return ResourceManager.GetString("Country_KW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cayman Islands.
        /// </summary>
        public static string Country_KY {
            get {
                return ResourceManager.GetString("Country_KY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kazakhstan.
        /// </summary>
        public static string Country_KZ {
            get {
                return ResourceManager.GetString("Country_KZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Laos.
        /// </summary>
        public static string Country_LA {
            get {
                return ResourceManager.GetString("Country_LA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lebanon.
        /// </summary>
        public static string Country_LB {
            get {
                return ResourceManager.GetString("Country_LB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saint Lucia.
        /// </summary>
        public static string Country_LC {
            get {
                return ResourceManager.GetString("Country_LC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Liechtenstein.
        /// </summary>
        public static string Country_LI {
            get {
                return ResourceManager.GetString("Country_LI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sri Lanka.
        /// </summary>
        public static string Country_LK {
            get {
                return ResourceManager.GetString("Country_LK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Liberia.
        /// </summary>
        public static string Country_LR {
            get {
                return ResourceManager.GetString("Country_LR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lesotho.
        /// </summary>
        public static string Country_LS {
            get {
                return ResourceManager.GetString("Country_LS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lithuania.
        /// </summary>
        public static string Country_LT {
            get {
                return ResourceManager.GetString("Country_LT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Luxembourg .
        /// </summary>
        public static string Country_LU {
            get {
                return ResourceManager.GetString("Country_LU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latvia.
        /// </summary>
        public static string Country_LV {
            get {
                return ResourceManager.GetString("Country_LV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Libya.
        /// </summary>
        public static string Country_LY {
            get {
                return ResourceManager.GetString("Country_LY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Morocco.
        /// </summary>
        public static string Country_MA {
            get {
                return ResourceManager.GetString("Country_MA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monaco.
        /// </summary>
        public static string Country_MC {
            get {
                return ResourceManager.GetString("Country_MC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moldova.
        /// </summary>
        public static string Country_MD {
            get {
                return ResourceManager.GetString("Country_MD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Madagascar.
        /// </summary>
        public static string Country_MG {
            get {
                return ResourceManager.GetString("Country_MG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marshall Islands.
        /// </summary>
        public static string Country_MH {
            get {
                return ResourceManager.GetString("Country_MH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Former Yugoslav Republic of Macedonia.
        /// </summary>
        public static string Country_MK {
            get {
                return ResourceManager.GetString("Country_MK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mali.
        /// </summary>
        public static string Country_ML {
            get {
                return ResourceManager.GetString("Country_ML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Myanmar.
        /// </summary>
        public static string Country_MM {
            get {
                return ResourceManager.GetString("Country_MM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mongolia.
        /// </summary>
        public static string Country_MN {
            get {
                return ResourceManager.GetString("Country_MN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Macau.
        /// </summary>
        public static string Country_MO {
            get {
                return ResourceManager.GetString("Country_MO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Northern Marianas.
        /// </summary>
        public static string Country_MP {
            get {
                return ResourceManager.GetString("Country_MP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Martinique.
        /// </summary>
        public static string Country_MQ {
            get {
                return ResourceManager.GetString("Country_MQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mauritania.
        /// </summary>
        public static string Country_MR {
            get {
                return ResourceManager.GetString("Country_MR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Montserrat.
        /// </summary>
        public static string Country_MS {
            get {
                return ResourceManager.GetString("Country_MS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malta.
        /// </summary>
        public static string Country_MT {
            get {
                return ResourceManager.GetString("Country_MT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mauritius.
        /// </summary>
        public static string Country_MU {
            get {
                return ResourceManager.GetString("Country_MU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maldives.
        /// </summary>
        public static string Country_MV {
            get {
                return ResourceManager.GetString("Country_MV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malawi.
        /// </summary>
        public static string Country_MW {
            get {
                return ResourceManager.GetString("Country_MW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mexico.
        /// </summary>
        public static string Country_MX {
            get {
                return ResourceManager.GetString("Country_MX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malaysia.
        /// </summary>
        public static string Country_MY {
            get {
                return ResourceManager.GetString("Country_MY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mozambique.
        /// </summary>
        public static string Country_MZ {
            get {
                return ResourceManager.GetString("Country_MZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Namibia.
        /// </summary>
        public static string Country_NA {
            get {
                return ResourceManager.GetString("Country_NA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Caledonia.
        /// </summary>
        public static string Country_NC {
            get {
                return ResourceManager.GetString("Country_NC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Niger.
        /// </summary>
        public static string Country_NE {
            get {
                return ResourceManager.GetString("Country_NE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Norfolk Island.
        /// </summary>
        public static string Country_NF {
            get {
                return ResourceManager.GetString("Country_NF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nigeria.
        /// </summary>
        public static string Country_NG {
            get {
                return ResourceManager.GetString("Country_NG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nicaragua.
        /// </summary>
        public static string Country_NI {
            get {
                return ResourceManager.GetString("Country_NI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Netherlands.
        /// </summary>
        public static string Country_NL {
            get {
                return ResourceManager.GetString("Country_NL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Norway.
        /// </summary>
        public static string Country_NO {
            get {
                return ResourceManager.GetString("Country_NO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nepal.
        /// </summary>
        public static string Country_NP {
            get {
                return ResourceManager.GetString("Country_NP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nauru.
        /// </summary>
        public static string Country_NR {
            get {
                return ResourceManager.GetString("Country_NR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Niue.
        /// </summary>
        public static string Country_NU {
            get {
                return ResourceManager.GetString("Country_NU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Zealand.
        /// </summary>
        public static string Country_NZ {
            get {
                return ResourceManager.GetString("Country_NZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oman.
        /// </summary>
        public static string Country_OM {
            get {
                return ResourceManager.GetString("Country_OM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Panama.
        /// </summary>
        public static string Country_PA {
            get {
                return ResourceManager.GetString("Country_PA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Peru.
        /// </summary>
        public static string Country_PE {
            get {
                return ResourceManager.GetString("Country_PE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French Polynesia.
        /// </summary>
        public static string Country_PF {
            get {
                return ResourceManager.GetString("Country_PF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Papua New Guinea.
        /// </summary>
        public static string Country_PG {
            get {
                return ResourceManager.GetString("Country_PG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Philippines.
        /// </summary>
        public static string Country_PH {
            get {
                return ResourceManager.GetString("Country_PH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pakistan.
        /// </summary>
        public static string Country_PK {
            get {
                return ResourceManager.GetString("Country_PK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Poland.
        /// </summary>
        public static string Country_PL {
            get {
                return ResourceManager.GetString("Country_PL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saint Pierre and Miquelon.
        /// </summary>
        public static string Country_PM {
            get {
                return ResourceManager.GetString("Country_PM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pitcairn Islands.
        /// </summary>
        public static string Country_PN {
            get {
                return ResourceManager.GetString("Country_PN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Puerto Rico.
        /// </summary>
        public static string Country_PR {
            get {
                return ResourceManager.GetString("Country_PR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Portugal.
        /// </summary>
        public static string Country_PT {
            get {
                return ResourceManager.GetString("Country_PT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Palau.
        /// </summary>
        public static string Country_PW {
            get {
                return ResourceManager.GetString("Country_PW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paraguay.
        /// </summary>
        public static string Country_PY {
            get {
                return ResourceManager.GetString("Country_PY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qatar.
        /// </summary>
        public static string Country_QA {
            get {
                return ResourceManager.GetString("Country_QA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Réunion.
        /// </summary>
        public static string Country_RE {
            get {
                return ResourceManager.GetString("Country_RE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Romania.
        /// </summary>
        public static string Country_RO {
            get {
                return ResourceManager.GetString("Country_RO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Russia.
        /// </summary>
        public static string Country_RU {
            get {
                return ResourceManager.GetString("Country_RU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rwanda.
        /// </summary>
        public static string Country_RW {
            get {
                return ResourceManager.GetString("Country_RW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saudi Arabia.
        /// </summary>
        public static string Country_SA {
            get {
                return ResourceManager.GetString("Country_SA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Solomon Islands.
        /// </summary>
        public static string Country_SB {
            get {
                return ResourceManager.GetString("Country_SB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seychelles.
        /// </summary>
        public static string Country_SC {
            get {
                return ResourceManager.GetString("Country_SC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sudan.
        /// </summary>
        public static string Country_SD {
            get {
                return ResourceManager.GetString("Country_SD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sweden.
        /// </summary>
        public static string Country_SE {
            get {
                return ResourceManager.GetString("Country_SE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Singapore.
        /// </summary>
        public static string Country_SG {
            get {
                return ResourceManager.GetString("Country_SG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saint Helena.
        /// </summary>
        public static string Country_SH {
            get {
                return ResourceManager.GetString("Country_SH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slovenia.
        /// </summary>
        public static string Country_SI {
            get {
                return ResourceManager.GetString("Country_SI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Svalbard and Jan Mayen.
        /// </summary>
        public static string Country_SJ {
            get {
                return ResourceManager.GetString("Country_SJ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slovakia.
        /// </summary>
        public static string Country_SK {
            get {
                return ResourceManager.GetString("Country_SK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sierra Leone.
        /// </summary>
        public static string Country_SL {
            get {
                return ResourceManager.GetString("Country_SL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to San Marino.
        /// </summary>
        public static string Country_SM {
            get {
                return ResourceManager.GetString("Country_SM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Senegal.
        /// </summary>
        public static string Country_SN {
            get {
                return ResourceManager.GetString("Country_SN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Somalia.
        /// </summary>
        public static string Country_SO {
            get {
                return ResourceManager.GetString("Country_SO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suriname.
        /// </summary>
        public static string Country_SR {
            get {
                return ResourceManager.GetString("Country_SR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to São Tomé and Príncipe.
        /// </summary>
        public static string Country_ST {
            get {
                return ResourceManager.GetString("Country_ST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to El Salvador.
        /// </summary>
        public static string Country_SV {
            get {
                return ResourceManager.GetString("Country_SV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Syria.
        /// </summary>
        public static string Country_SY {
            get {
                return ResourceManager.GetString("Country_SY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Swaziland.
        /// </summary>
        public static string Country_SZ {
            get {
                return ResourceManager.GetString("Country_SZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turks and Caicos Islands.
        /// </summary>
        public static string Country_TC {
            get {
                return ResourceManager.GetString("Country_TC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chad.
        /// </summary>
        public static string Country_TD {
            get {
                return ResourceManager.GetString("Country_TD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French Southern Territories.
        /// </summary>
        public static string Country_TF {
            get {
                return ResourceManager.GetString("Country_TF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Togo.
        /// </summary>
        public static string Country_TG {
            get {
                return ResourceManager.GetString("Country_TG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thailand.
        /// </summary>
        public static string Country_TH {
            get {
                return ResourceManager.GetString("Country_TH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tajikistan.
        /// </summary>
        public static string Country_TJ {
            get {
                return ResourceManager.GetString("Country_TJ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tokelau.
        /// </summary>
        public static string Country_TK {
            get {
                return ResourceManager.GetString("Country_TK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to East Timor.
        /// </summary>
        public static string Country_TL {
            get {
                return ResourceManager.GetString("Country_TL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turkmenistan.
        /// </summary>
        public static string Country_TM {
            get {
                return ResourceManager.GetString("Country_TM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tunisia.
        /// </summary>
        public static string Country_TN {
            get {
                return ResourceManager.GetString("Country_TN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tonga.
        /// </summary>
        public static string Country_TO {
            get {
                return ResourceManager.GetString("Country_TO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turkey.
        /// </summary>
        public static string Country_TR {
            get {
                return ResourceManager.GetString("Country_TR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trinidad and Tobago.
        /// </summary>
        public static string Country_TT {
            get {
                return ResourceManager.GetString("Country_TT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tuvalu.
        /// </summary>
        public static string Country_TV {
            get {
                return ResourceManager.GetString("Country_TV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taiwan.
        /// </summary>
        public static string Country_TW {
            get {
                return ResourceManager.GetString("Country_TW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tanzania.
        /// </summary>
        public static string Country_TZ {
            get {
                return ResourceManager.GetString("Country_TZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ukraine.
        /// </summary>
        public static string Country_UA {
            get {
                return ResourceManager.GetString("Country_UA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uganda.
        /// </summary>
        public static string Country_UG {
            get {
                return ResourceManager.GetString("Country_UG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to United States Minor Outlying Islands.
        /// </summary>
        public static string Country_UM {
            get {
                return ResourceManager.GetString("Country_UM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to United States.
        /// </summary>
        public static string Country_US {
            get {
                return ResourceManager.GetString("Country_US", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uruguay.
        /// </summary>
        public static string Country_UY {
            get {
                return ResourceManager.GetString("Country_UY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uzbekistan.
        /// </summary>
        public static string Country_UZ {
            get {
                return ResourceManager.GetString("Country_UZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vatican City.
        /// </summary>
        public static string Country_VA {
            get {
                return ResourceManager.GetString("Country_VA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saint Vincent and the Grenadines.
        /// </summary>
        public static string Country_VC {
            get {
                return ResourceManager.GetString("Country_VC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Venezuela.
        /// </summary>
        public static string Country_VE {
            get {
                return ResourceManager.GetString("Country_VE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to British Virgin Islands.
        /// </summary>
        public static string Country_VG {
            get {
                return ResourceManager.GetString("Country_VG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to US Virgin Islands.
        /// </summary>
        public static string Country_VI {
            get {
                return ResourceManager.GetString("Country_VI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vietnam.
        /// </summary>
        public static string Country_VN {
            get {
                return ResourceManager.GetString("Country_VN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vanuatu.
        /// </summary>
        public static string Country_VU {
            get {
                return ResourceManager.GetString("Country_VU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wallis and Futuna.
        /// </summary>
        public static string Country_WF {
            get {
                return ResourceManager.GetString("Country_WF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Samoa.
        /// </summary>
        public static string Country_WS {
            get {
                return ResourceManager.GetString("Country_WS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yemen.
        /// </summary>
        public static string Country_YE {
            get {
                return ResourceManager.GetString("Country_YE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mayotte.
        /// </summary>
        public static string Country_YT {
            get {
                return ResourceManager.GetString("Country_YT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yugoslavia.
        /// </summary>
        public static string Country_YU {
            get {
                return ResourceManager.GetString("Country_YU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to South Africa.
        /// </summary>
        public static string Country_ZA {
            get {
                return ResourceManager.GetString("Country_ZA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zambia.
        /// </summary>
        public static string Country_ZM {
            get {
                return ResourceManager.GetString("Country_ZM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zimbabwe.
        /// </summary>
        public static string Country_ZW {
            get {
                return ResourceManager.GetString("Country_ZW", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a new account.
        /// </summary>
        public static string CreateANewAccount {
            get {
                return ResourceManager.GetString("CreateANewAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a password.
        /// </summary>
        public static string CreateAPassword {
            get {
                return ResourceManager.GetString("CreateAPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Execute export for:.
        /// </summary>
        public static string CreateExportFor {
            get {
                return ResourceManager.GetString("CreateExportFor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create New.
        /// </summary>
        public static string CreateNew {
            get {
                return ResourceManager.GetString("CreateNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Creating.
        /// </summary>
        public static string CreateTitle {
            get {
                return ResourceManager.GetString("CreateTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currencies Conversions.
        /// </summary>
        public static string CurrenciesConversions {
            get {
                return ResourceManager.GetString("CurrenciesConversions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Info.
        /// </summary>
        public static string CustomerInfo {
            get {
                return ResourceManager.GetString("CustomerInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CustomerOrderNotes.
        /// </summary>
        public static string CustomerOrderNotes {
            get {
                return ResourceManager.GetString("CustomerOrderNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thoses who bought.
        /// </summary>
        public static string CustomersElement {
            get {
                return ResourceManager.GetString("CustomersElement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        public static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Friday.
        /// </summary>
        public static string DaysOfWeek_Friday {
            get {
                return ResourceManager.GetString("DaysOfWeek_Friday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monday.
        /// </summary>
        public static string DaysOfWeek_Monday {
            get {
                return ResourceManager.GetString("DaysOfWeek_Monday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturday.
        /// </summary>
        public static string DaysOfWeek_Saturday {
            get {
                return ResourceManager.GetString("DaysOfWeek_Saturday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sunday.
        /// </summary>
        public static string DaysOfWeek_Sunday {
            get {
                return ResourceManager.GetString("DaysOfWeek_Sunday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thursday.
        /// </summary>
        public static string DaysOfWeek_Thursday {
            get {
                return ResourceManager.GetString("DaysOfWeek_Thursday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tuesday.
        /// </summary>
        public static string DaysOfWeek_Tuesday {
            get {
                return ResourceManager.GetString("DaysOfWeek_Tuesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wednesday.
        /// </summary>
        public static string DaysOfWeek_Wednesday {
            get {
                return ResourceManager.GetString("DaysOfWeek_Wednesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image to be shown in the news..
        /// </summary>
        public static string DbNews_ImageToBeShownInTheNews {
            get {
                return ResourceManager.GetString("DbNews_ImageToBeShownInTheNews", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language area the news will be shown in..
        /// </summary>
        public static string DbNews_LanguageAreaTheNewsWillBeShownIn {
            get {
                return ResourceManager.GetString("DbNews_LanguageAreaTheNewsWillBeShownIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to News text.
        /// </summary>
        public static string DbNews_NewsText {
            get {
                return ResourceManager.GetString("DbNews_NewsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Url, product code (field &quot;Key&quot;) etc...
        /// </summary>
        public static string DbNews_UrlProductCodeEtc {
            get {
                return ResourceManager.GetString("DbNews_UrlProductCodeEtc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What to do when news frame is clicked in app.
        /// </summary>
        public static string DbNews_WhatToDoWhenNewsFrameIsClickedInApp {
            get {
                return ResourceManager.GetString("DbNews_WhatToDoWhenNewsFrameIsClickedInApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string DeleteTitle {
            get {
                return ResourceManager.GetString("DeleteTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeliveryStatus.
        /// </summary>
        public static string DeliveryStatus {
            get {
                return ResourceManager.GetString("DeliveryStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access Denied.
        /// </summary>
        public static string Denied {
            get {
                return ResourceManager.GetString("Denied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description EN.
        /// </summary>
        public static string DescriptionEN {
            get {
                return ResourceManager.GetString("DescriptionEN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description FR.
        /// </summary>
        public static string DescriptionFR {
            get {
                return ResourceManager.GetString("DescriptionFR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description RU.
        /// </summary>
        public static string DescriptionRU {
            get {
                return ResourceManager.GetString("DescriptionRU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Desktop.
        /// </summary>
        public static string Desktop {
            get {
                return ResourceManager.GetString("Desktop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details.
        /// </summary>
        public static string Details {
            get {
                return ResourceManager.GetString("Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dev.
        /// </summary>
        public static string Dev {
            get {
                return ResourceManager.GetString("Dev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to © 2018 AppoMobi and respective content owners.
        /// </summary>
        public static string DevCopyright {
            get {
                return ResourceManager.GetString("DevCopyright", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Devices total.
        /// </summary>
        public static string DevicesTotal {
            get {
                return ResourceManager.GetString("DevicesTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did you remember your old password?.
        /// </summary>
        public static string DidYouRememberYourOLDPassword {
            get {
                return ResourceManager.GetString("DidYouRememberYourOLDPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Did you remember your password?.
        /// </summary>
        public static string DidYouRememberYourPassword {
            get {
                return ResourceManager.GetString("DidYouRememberYourPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Difficulty Level.
        /// </summary>
        public static string DifficultyLevel {
            get {
                return ResourceManager.GetString("DifficultyLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable.
        /// </summary>
        public static string Disable {
            get {
                return ResourceManager.GetString("Disable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string Disabled {
            get {
                return ResourceManager.GetString("Disabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will not be exported outside control panel if turned On.
        /// </summary>
        public static string DisabledEntryDesc {
            get {
                return ResourceManager.GetString("DisabledEntryDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount.
        /// </summary>
        public static string Discount {
            get {
                return ResourceManager.GetString("Discount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displayed over Our Contacts.
        /// </summary>
        public static string DisplayedOverOurContacts {
            get {
                return ResourceManager.GetString("DisplayedOverOurContacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to or.
        /// </summary>
        public static string DividerOr {
            get {
                return ResourceManager.GetString("DividerOr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do Not Save.
        /// </summary>
        public static string DoNotSave {
            get {
                return ResourceManager.GetString("DoNotSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It appears You don&apos;t have the rights to acces this section. Please contact support if You think it might be a mistake.
        ///.
        /// </summary>
        public static string DonTHaveTheRights {
            get {
                return ResourceManager.GetString("DonTHaveTheRights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you have an account?.
        /// </summary>
        public static string DoYouHaveAnAccount {
            get {
                return ResourceManager.GetString("DoYouHaveAnAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If You would you like to find THALION centers closest to You please be positive at the next window..
        /// </summary>
        public static string DoYouWantUsToGPS {
            get {
                return ResourceManager.GetString("DoYouWantUsToGPS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drag and Drop Events on the Calendar.
        /// </summary>
        public static string DragAndDropEventsOnTheCalendar {
            get {
                return ResourceManager.GetString("DragAndDropEventsOnTheCalendar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duration.
        /// </summary>
        public static string Duration {
            get {
                return ResourceManager.GetString("Duration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        public static string EditDetails {
            get {
                return ResourceManager.GetString("EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edited By.
        /// </summary>
        public static string EditedBy {
            get {
                return ResourceManager.GetString("EditedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edited Time.
        /// </summary>
        public static string EditedTime {
            get {
                return ResourceManager.GetString("EditedTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Event.
        /// </summary>
        public static string EditEvent {
            get {
                return ResourceManager.GetString("EditEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Editor.
        /// </summary>
        public static string Editor {
            get {
                return ResourceManager.GetString("Editor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Editor&apos;s notes, internal use only.
        /// </summary>
        public static string EditorSNotesInternalUseOnly {
            get {
                return ResourceManager.GetString("EditorSNotesInternalUseOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Editing.
        /// </summary>
        public static string EditTitle {
            get {
                return ResourceManager.GetString("EditTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to E-mail.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email &apos;{0}&apos; is already taken. .
        /// </summary>
        public static string EmailAlreadyTaken {
            get {
                return ResourceManager.GetString("EmailAlreadyTaken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for confirming your email. You can now use your credentials to log in..
        /// </summary>
        public static string EmailConfirmed {
            get {
                return ResourceManager.GetString("EmailConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string EmailCreateAccBody {
            get {
                return ResourceManager.GetString("EmailCreateAccBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AppoMobi: confirm account creation.
        /// </summary>
        public static string EmailCreateAccSubject {
            get {
                return ResourceManager.GetString("EmailCreateAccSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AppoMobi Control Panel.
        /// </summary>
        public static string EmailFrom {
            get {
                return ResourceManager.GetString("EmailFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable.
        /// </summary>
        public static string Enable {
            get {
                return ResourceManager.GetString("Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled.
        /// </summary>
        public static string Enabled {
            get {
                return ResourceManager.GetString("Enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled Modules.
        /// </summary>
        public static string EnabledModules {
            get {
                return ResourceManager.GetString("EnabledModules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End.
        /// </summary>
        public static string EndEvent {
            get {
                return ResourceManager.GetString("EndEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter 2-letters language codes.
        /// </summary>
        public static string Enter2LettersLanguageCodes {
            get {
                return ResourceManager.GetString("Enter2LettersLanguageCodes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter reason.
        /// </summary>
        public static string EnterReason {
            get {
                return ResourceManager.GetString("EnterReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string EnterString {
            get {
                return ResourceManager.GetString("EnterString", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please ensure that you have a map application installed..
        /// </summary>
        public static string ErrorCannotNavigate {
            get {
                return ResourceManager.GetString("ErrorCannotNavigate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection error. Please check your Internet connection and try again..
        /// </summary>
        public static string ErrorConnection {
            get {
                return ResourceManager.GetString("ErrorConnection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection error. Please try again later..
        /// </summary>
        public static string ErrorConnNews {
            get {
                return ResourceManager.GetString("ErrorConnNews", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection error. Please try again later..
        /// </summary>
        public static string ErrorConnRegions {
            get {
                return ResourceManager.GetString("ErrorConnRegions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection error. Please try again later..
        /// </summary>
        public static string ErrorConnSalons {
            get {
                return ResourceManager.GetString("ErrorConnSalons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection error. Please try again later..
        /// </summary>
        public static string ErrorConSalon {
            get {
                return ResourceManager.GetString("ErrorConSalon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Found Error.
        /// </summary>
        public static string ErrorNotFound {
            get {
                return ResourceManager.GetString("ErrorNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error: Please check requirements for fields below..
        /// </summary>
        public static string ErrorPleaseCheckRequirementsForFieldsBelow {
            get {
                return ResourceManager.GetString("ErrorPleaseCheckRequirementsForFieldsBelow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Something went wrong.
        /// </summary>
        public static string ErrorSomething {
            get {
                return ResourceManager.GetString("ErrorSomething", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string ErrorTitle {
            get {
                return ResourceManager.GetString("ErrorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown Error.
        /// </summary>
        public static string ErrorUnknown {
            get {
                return ResourceManager.GetString("ErrorUnknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event Card.
        /// </summary>
        public static string EventCard {
            get {
                return ResourceManager.GetString("EventCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event details.
        /// </summary>
        public static string EventDetails {
            get {
                return ResourceManager.GetString("EventDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events.
        /// </summary>
        public static string Events {
            get {
                return ResourceManager.GetString("Events", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events elements.
        /// </summary>
        public static string EventsElements {
            get {
                return ResourceManager.GetString("EventsElements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event Type.
        /// </summary>
        public static string EventType {
            get {
                return ResourceManager.GetString("EventType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exclude with tags.
        /// </summary>
        public static string ExcludeQuestionsWithTags {
            get {
                return ResourceManager.GetString("ExcludeQuestionsWithTags", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exit.
        /// </summary>
        public static string Exit {
            get {
                return ResourceManager.GetString("Exit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From 1 To 3y.
        /// </summary>
        public static string ExperienceType_From1To3 {
            get {
                return ResourceManager.GetString("ExperienceType_From1To3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From 3 To 5y.
        /// </summary>
        public static string ExperienceType_From3To5 {
            get {
                return ResourceManager.GetString("ExperienceType_From3To5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From 5y.
        /// </summary>
        public static string ExperienceType_From5 {
            get {
                return ResourceManager.GetString("ExperienceType_From5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unneeded.
        /// </summary>
        public static string ExperienceType_Unneeded {
            get {
                return ResourceManager.GetString("ExperienceType_Unneeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up To 1y.
        /// </summary>
        public static string ExperienceType_UpTo1 {
            get {
                return ResourceManager.GetString("ExperienceType_UpTo1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today.
        /// </summary>
        public static string ExplainDate_Today {
            get {
                return ResourceManager.GetString("ExplainDate_Today", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tomorrow.
        /// </summary>
        public static string ExplainDate_Tomm {
            get {
                return ResourceManager.GetString("ExplainDate_Tomm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In {0} days.
        /// </summary>
        public static string ExplainDate_X {
            get {
                return ResourceManager.GetString("ExplainDate_X", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In {0} days.
        /// </summary>
        public static string ExplainDate_X1 {
            get {
                return ResourceManager.GetString("ExplainDate_X1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} days ago.
        /// </summary>
        public static string ExplainDate_X1past {
            get {
                return ResourceManager.GetString("ExplainDate_X1past", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In {0} days.
        /// </summary>
        public static string ExplainDate_X2 {
            get {
                return ResourceManager.GetString("ExplainDate_X2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} days ago.
        /// </summary>
        public static string ExplainDate_X2past {
            get {
                return ResourceManager.GetString("ExplainDate_X2past", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} days ago.
        /// </summary>
        public static string ExplainDate_Xpast {
            get {
                return ResourceManager.GetString("ExplainDate_Xpast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yesturday.
        /// </summary>
        public static string ExplainDate_Yest {
            get {
                return ResourceManager.GetString("ExplainDate_Yest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We will wait for you {0}.
        /// </summary>
        public static string ExplainDateWithInterval {
            get {
                return ResourceManager.GetString("ExplainDateWithInterval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export.
        /// </summary>
        public static string Export {
            get {
                return ResourceManager.GetString("Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ExportNeeded By.
        /// </summary>
        public static string ExportedBy {
            get {
                return ResourceManager.GetString("ExportedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ExportNeeded.
        /// </summary>
        public static string ExportedTime {
            get {
                return ResourceManager.GetString("ExportedTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exporting.
        /// </summary>
        public static string Exporting {
            get {
                return ResourceManager.GetString("Exporting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exports.
        /// </summary>
        public static string Exports {
            get {
                return ResourceManager.GetString("Exports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export complete successfully!.
        /// </summary>
        public static string ExportsController_Index_ExportComplete {
            get {
                return ResourceManager.GetString("ExportsController_Index_ExportComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fast Export.
        /// </summary>
        public static string ExportSection {
            get {
                return ResourceManager.GetString("ExportSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export Type.
        /// </summary>
        public static string ExportType {
            get {
                return ResourceManager.GetString("ExportType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to External Logins.
        /// </summary>
        public static string ExternalLogins {
            get {
                return ResourceManager.GetString("ExternalLogins", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Face.
        /// </summary>
        public static string Face {
            get {
                return ResourceManager.GetString("Face", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Facebook.
        /// </summary>
        public static string Facebook {
            get {
                return ResourceManager.GetString("Facebook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to verify code..
        /// </summary>
        public static string FailedToVerifyCode {
            get {
                return ResourceManager.GetString("FailedToVerifyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fast Login With.
        /// </summary>
        public static string FastLoginWith {
            get {
                return ResourceManager.GetString("FastLoginWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Now You can quickly access this center data from the Favorites section..
        /// </summary>
        public static string FavDescBlabla {
            get {
                return ResourceManager.GetString("FavDescBlabla", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Congratulations!.
        /// </summary>
        public static string FavGratz {
            get {
                return ResourceManager.GetString("FavGratz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Salon.
        /// </summary>
        public static string Favorite {
            get {
                return ResourceManager.GetString("Favorite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome!.
        /// </summary>
        public static string FavoriteEmpty1 {
            get {
                return ResourceManager.GetString("FavoriteEmpty1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace existing favorite with this one?.
        /// </summary>
        public static string FavReplaceConfirm {
            get {
                return ResourceManager.GetString("FavReplaceConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Featured Image.
        /// </summary>
        public static string FeaturedImage {
            get {
                return ResourceManager.GetString("FeaturedImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Feedback.
        /// </summary>
        public static string Feedback {
            get {
                return ResourceManager.GetString("Feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field &apos;{0}&apos; value must be unique.
        /// </summary>
        public static string FieldMustBeUnique {
            get {
                return ResourceManager.GetString("FieldMustBeUnique", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance In.
        /// </summary>
        public static string FinancialReasonType_Balance {
            get {
                return ResourceManager.GetString("FinancialReasonType_Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Charge.
        /// </summary>
        public static string FinancialReasonType_Charge {
            get {
                return ResourceManager.GetString("FinancialReasonType_Charge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deposit.
        /// </summary>
        public static string FinancialReasonType_Deposit {
            get {
                return ResourceManager.GetString("FinancialReasonType_Deposit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string FinancialReasonType_Other {
            get {
                return ResourceManager.GetString("FinancialReasonType_Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscription.
        /// </summary>
        public static string FinancialReasonType_Subscription {
            get {
                return ResourceManager.GetString("FinancialReasonType_Subscription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For Booking Only.
        /// </summary>
        public static string ForBookingOnly {
            get {
                return ResourceManager.GetString("ForBookingOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot password?.
        /// </summary>
        public static string ForgotYourPassword {
            get {
                return ResourceManager.GetString("ForgotYourPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Name.
        /// </summary>
        public static string FullName {
            get {
                return ResourceManager.GetString("FullName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Galleries.
        /// </summary>
        public static string Galleries {
            get {
                return ResourceManager.GetString("Galleries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gallery.
        /// </summary>
        public static string Gallery {
            get {
                return ResourceManager.GetString("Gallery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown.
        /// </summary>
        public static string GenerateDropDowns_Unknown {
            get {
                return ResourceManager.GetString("GenerateDropDowns_Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finding your location...
        /// </summary>
        public static string GettingGPSCoords {
            get {
                return ResourceManager.GetString("GettingGPSCoords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goalkeepers.
        /// </summary>
        public static string Goalkeepers {
            get {
                return ResourceManager.GetString("Goalkeepers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to    .
        /// </summary>
        public static string GoBack {
            get {
                return ResourceManager.GetString("GoBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods.
        /// </summary>
        public static string Goods {
            get {
                return ResourceManager.GetString("Goods", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go To Catalog.
        /// </summary>
        public static string GotoProducts {
            get {
                return ResourceManager.GetString("GotoProducts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turn It On.
        /// </summary>
        public static string GPSBtn_TurnGPSOn {
            get {
                return ResourceManager.GetString("GPSBtn_TurnGPSOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We failed to obtain your GPS position. Please turn on High accuracy (Use GPS, Wi-Fi, Bluetooth, or cellular networks to determine location) in your gps settings..
        /// </summary>
        public static string GPSNeedHighAccuracy {
            get {
                return ResourceManager.GetString("GPSNeedHighAccuracy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We would need access to your GPS position to be able to help find us. Enable access now?.
        /// </summary>
        public static string GPSPermissionsNeedOn {
            get {
                return ResourceManager.GetString("GPSPermissionsNeedOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your GPS is turned off. Please turn it on for us to be able to assist You..
        /// </summary>
        public static string GPSPleaseTurnOn {
            get {
                return ResourceManager.GetString("GPSPleaseTurnOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We would need your GPS coordinates to help You with geo-position..
        /// </summary>
        public static string GPSPleaseTurnOniOS {
            get {
                return ResourceManager.GetString("GPSPleaseTurnOniOS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello.
        /// </summary>
        public static string Hello {
            get {
                return ResourceManager.GetString("Hello", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello, {0}.
        /// </summary>
        public static string Hello0 {
            get {
                return ResourceManager.GetString("Hello0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow to be exported for mobile app or not..
        /// </summary>
        public static string HelpAllowToBeExportedForMobileAppOrNot {
            get {
                return ResourceManager.GetString("HelpAllowToBeExportedForMobileAppOrNot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculate your distance to centers and sort them using this button.
        /// </summary>
        public static string HintCentersListGpsBtn {
            get {
                return ResourceManager.GetString("HintCentersListGpsBtn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Find route.
        /// </summary>
        public static string HowToGet {
            get {
                return ResourceManager.GetString("HowToGet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Metro:.
        /// </summary>
        public static string HowToGetThereMetroTitle {
            get {
                return ResourceManager.GetString("HowToGetThereMetroTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How to use.
        /// </summary>
        public static string HowToUse {
            get {
                return ResourceManager.GetString("HowToUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About Us.
        /// </summary>
        public static string iAboutUs {
            get {
                return ResourceManager.GetString("iAboutUs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I Like.
        /// </summary>
        public static string ILike {
            get {
                return ResourceManager.GetString("ILike", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image.
        /// </summary>
        public static string Image {
            get {
                return ResourceManager.GetString("Image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image Height.
        /// </summary>
        public static string ImageHeight {
            get {
                return ResourceManager.GetString("ImageHeight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image (url).
        /// </summary>
        public static string ImageURL {
            get {
                return ResourceManager.GetString("ImageURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MINI-Image URL.
        /// </summary>
        public static string ImageURLForMini {
            get {
                return ResourceManager.GetString("ImageURLForMini", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image Width.
        /// </summary>
        public static string ImageWidth {
            get {
                return ResourceManager.GetString("ImageWidth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Important Notes.
        /// </summary>
        public static string ImportantNotes {
            get {
                return ResourceManager.GetString("ImportantNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive.
        /// </summary>
        public static string Inactive {
            get {
                return ResourceManager.GetString("Inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include with tags.
        /// </summary>
        public static string IncludeQuestionsWithTags {
            get {
                return ResourceManager.GetString("IncludeQuestionsWithTags", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect email address or phone number..
        /// </summary>
        public static string IncorrectEmailAddressOrPhoneNumber {
            get {
                return ResourceManager.GetString("IncorrectEmailAddressOrPhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information.
        /// </summary>
        public static string Information {
            get {
                return ResourceManager.GetString("Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to in the region of.
        /// </summary>
        public static string InRegion {
            get {
                return ResourceManager.GetString("InRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to inside.
        /// </summary>
        public static string InSection {
            get {
                return ResourceManager.GetString("InSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insert Event Name.
        /// </summary>
        public static string InsertEventName {
            get {
                return ResourceManager.GetString("InsertEventName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instagram.
        /// </summary>
        public static string Instagram {
            get {
                return ResourceManager.GetString("Instagram", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to International Titles Language.
        /// </summary>
        public static string InternationalTitlesLanguage {
            get {
                return ResourceManager.GetString("InternationalTitlesLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time:.
        /// </summary>
        public static string IntervalsTime {
            get {
                return ResourceManager.GetString("IntervalsTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GO TO CATEGORY.
        /// </summary>
        public static string INTHECATEGORY {
            get {
                return ResourceManager.GetString("INTHECATEGORY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Favorites.
        /// </summary>
        public static string iOSTabsStartup_Setup_Favorites {
            get {
                return ResourceManager.GetString("iOSTabsStartup_Setup_Favorites", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Favorites.
        /// </summary>
        public static string iOSTabsStartup_Setup_MyPreferences {
            get {
                return ResourceManager.GetString("iOSTabsStartup_Setup_MyPreferences", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Where To Find.
        /// </summary>
        public static string iOSTabsStartup_Setup_WhereToFind {
            get {
                return ResourceManager.GetString("iOSTabsStartup_Setup_WhereToFind", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Map.
        /// </summary>
        public static string iRegion {
            get {
                return ResourceManager.GetString("iRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Centers.
        /// </summary>
        public static string iSalonList {
            get {
                return ResourceManager.GetString("iSalonList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item added to Wish List.
        /// </summary>
        public static string ItemAddedToWishList {
            get {
                return ResourceManager.GetString("ItemAddedToWishList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items.
        /// </summary>
        public static string Items {
            get {
                return ResourceManager.GetString("Items", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key.
        /// </summary>
        public static string Key {
            get {
                return ResourceManager.GetString("Key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required unique key, used in mobile app..
        /// </summary>
        public static string KeyHint {
            get {
                return ResourceManager.GetString("KeyHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keywords.
        /// </summary>
        public static string Keywords {
            get {
                return ResourceManager.GetString("Keywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to km.
        /// </summary>
        public static string km {
            get {
                return ResourceManager.GetString("km", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to enter here.
        /// </summary>
        public static string LandingEnterHere {
            get {
                return ResourceManager.GetString("LandingEnterHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clients.
        /// </summary>
        public static string LandingForClients {
            get {
                return ResourceManager.GetString("LandingForClients", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to en.
        /// </summary>
        public static string Lang {
            get {
                return ResourceManager.GetString("Lang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to En.
        /// </summary>
        public static string LangCode {
            get {
                return ResourceManager.GetString("LangCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Armenian.
        /// </summary>
        public static string LangDesc {
            get {
                return ResourceManager.GetString("LangDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        public static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LastSeenTime.
        /// </summary>
        public static string LastSeenTime {
            get {
                return ResourceManager.GetString("LastSeenTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latest mobile app version.
        /// </summary>
        public static string LatestMobileAppVersion {
            get {
                return ResourceManager.GetString("LatestMobileAppVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn More.
        /// </summary>
        public static string LearnMore {
            get {
                return ResourceManager.GetString("LearnMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Leave us a message.
        /// </summary>
        public static string LeaveMessage {
            get {
                return ResourceManager.GetString("LeaveMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Legal Terms.
        /// </summary>
        public static string LegalTerms {
            get {
                return ResourceManager.GetString("LegalTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Level.
        /// </summary>
        public static string Level {
            get {
                return ResourceManager.GetString("Level", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Linked services that will be proposed for booking in app.
        /// </summary>
        public static string LinkedServicesHint {
            get {
                return ResourceManager.GetString("LinkedServicesHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info Link.
        /// </summary>
        public static string LinkMoreInfo {
            get {
                return ResourceManager.GetString("LinkMoreInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Links.
        /// </summary>
        public static string Links {
            get {
                return ResourceManager.GetString("Links", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List.
        /// </summary>
        public static string List {
            get {
                return ResourceManager.GetString("List", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading....
        /// </summary>
        public static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading original image...
        /// </summary>
        public static string LoadingOriginalImage {
            get {
                return ResourceManager.GetString("LoadingOriginalImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Log In.
        /// </summary>
        public static string LogIn {
            get {
                return ResourceManager.GetString("LogIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Failure.
        /// </summary>
        public static string LoginFailure {
            get {
                return ResourceManager.GetString("LoginFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Provider.
        /// </summary>
        public static string LoginProvider {
            get {
                return ResourceManager.GetString("LoginProvider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail.
        /// </summary>
        public static string Mail {
            get {
                return ResourceManager.GetString("Mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        public static string MailSubjectResetPassword {
            get {
                return ResourceManager.GetString("MailSubjectResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage.
        /// </summary>
        public static string Manage {
            get {
                return ResourceManager.GetString("Manage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Account.
        /// </summary>
        public static string ManageAccount {
            get {
                return ResourceManager.GetString("ManageAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Management.
        /// </summary>
        public static string Management {
            get {
                return ResourceManager.GetString("Management", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage your external logins.
        /// </summary>
        public static string ManageYourExternalLogins {
            get {
                return ResourceManager.GetString("ManageYourExternalLogins", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Many.
        /// </summary>
        public static string Many {
            get {
                return ResourceManager.GetString("Many", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Map Center X.
        /// </summary>
        public static string MapCenterX {
            get {
                return ResourceManager.GetString("MapCenterX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Map Center Y.
        /// </summary>
        public static string MapCenterY {
            get {
                return ResourceManager.GetString("MapCenterY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MapX.
        /// </summary>
        public static string MapX {
            get {
                return ResourceManager.GetString("MapX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Map Zoom.
        /// </summary>
        public static string MapZoom {
            get {
                return ResourceManager.GetString("MapZoom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sent to {0} devices.
        /// </summary>
        public static string maskSentToDevices {
            get {
                return ResourceManager.GetString("maskSentToDevices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seats.
        /// </summary>
        public static string MaxSeats {
            get {
                return ResourceManager.GetString("MaxSeats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum space for reservation in the round. When 0, it is not limited..
        /// </summary>
        public static string MaxSeatsDesc {
            get {
                return ResourceManager.GetString("MaxSeatsDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Men Only.
        /// </summary>
        public static string MenOnly {
            get {
                return ResourceManager.GetString("MenOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        public static string MenuPageAbout {
            get {
                return ResourceManager.GetString("MenuPageAbout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        public static string MenuPageContacts {
            get {
                return ResourceManager.GetString("MenuPageContacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Homepage.
        /// </summary>
        public static string MenuPageHome {
            get {
                return ResourceManager.GetString("MenuPageHome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to News.
        /// </summary>
        public static string MenuPageNews {
            get {
                return ResourceManager.GetString("MenuPageNews", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Find A Center.
        /// </summary>
        public static string MenuPageSalons {
            get {
                return ResourceManager.GetString("MenuPageSalons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Catalog.
        /// </summary>
        public static string MenuProducts {
            get {
                return ResourceManager.GetString("MenuProducts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products.
        /// </summary>
        public static string MenuProductsShort {
            get {
                return ResourceManager.GetString("MenuProductsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Even More...
        /// </summary>
        public static string MenuSomeMore {
            get {
                return ResourceManager.GetString("MenuSomeMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Messagable devices total.
        /// </summary>
        public static string MessagableDevicesTotal {
            get {
                return ResourceManager.GetString("MessagableDevicesTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Message body.
        /// </summary>
        public static string MessageTextDesc {
            get {
                return ResourceManager.GetString("MessageTextDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Metro.
        /// </summary>
        public static string Metro {
            get {
                return ResourceManager.GetString("Metro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min Time To Book.
        /// </summary>
        public static string MinTimeToBook {
            get {
                return ResourceManager.GetString("MinTimeToBook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minimum time for booking events.
        /// </summary>
        public static string MinTimeToBookDesc {
            get {
                return ResourceManager.GetString("MinTimeToBookDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connecting, please wait...
        /// </summary>
        public static string ModelAuth_ConnectingWithProviderPleaseWait {
            get {
                return ResourceManager.GetString("ModelAuth_ConnectingWithProviderPleaseWait", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To take full advantage of all the features of this app please register, it&apos;s free!.
        /// </summary>
        public static string ModelAuth_DoRegistrationCheck {
            get {
                return ResourceManager.GetString("ModelAuth_DoRegistrationCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid email address provided.
        /// </summary>
        public static string ModelAuth_ServerErrorMessageDesc_InvalidEmailAdressProvided {
            get {
                return ResourceManager.GetString("ModelAuth_ServerErrorMessageDesc_InvalidEmailAdressProvided", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registration failed, using another email address might help.
        /// </summary>
        public static string ModelAuth_ServerErrorMessageDesc_RegistrationFailedUsingAnotherEmailAdressMightHelp {
            get {
                return ResourceManager.GetString("ModelAuth_ServerErrorMessageDesc_RegistrationFailedUsingAnotherEmailAdressMightHe" +
                        "lp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The password must be at least 6 characters long.
        /// </summary>
        public static string ModelAuth_ServerErrorMessageDesc_ThePasswordMustBeAtLeast6CharactersLong {
            get {
                return ResourceManager.GetString("ModelAuth_ServerErrorMessageDesc_ThePasswordMustBeAtLeast6CharactersLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Info.
        /// </summary>
        public static string MoreInfo {
            get {
                return ResourceManager.GetString("MoreInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More info at this link.
        /// </summary>
        public static string MoreInfoLink {
            get {
                return ResourceManager.GetString("MoreInfoLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Salon.
        /// </summary>
        public static string MyFav_iOS {
            get {
                return ResourceManager.GetString("MyFav_iOS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Salon.
        /// </summary>
        public static string MySalonTab {
            get {
                return ResourceManager.GetString("MySalonTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Wish List.
        /// </summary>
        public static string MyWishList {
            get {
                return ResourceManager.GetString("MyWishList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        public static string NameTitle {
            get {
                return ResourceManager.GetString("NameTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Find Route.
        /// </summary>
        public static string NavigateTo {
            get {
                return ResourceManager.GetString("NavigateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open center inside app.
        /// </summary>
        public static string NavigateToCenter {
            get {
                return ResourceManager.GetString("NavigateToCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Navigate to url.
        /// </summary>
        public static string NavigateToUrl {
            get {
                return ResourceManager.GetString("NavigateToUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Navigate to url inside app.
        /// </summary>
        public static string NavigateToWww {
            get {
                return ResourceManager.GetString("NavigateToWww", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log off users.
        /// </summary>
        public static string NeedAllUsersRelog {
            get {
                return ResourceManager.GetString("NeedAllUsersRelog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make all users of the control panel related to this client relog so that the visual changes take effect..
        /// </summary>
        public static string NeedAllUsersRelogDesc {
            get {
                return ResourceManager.GetString("NeedAllUsersRelogDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to , needed!.
        /// </summary>
        public static string Needed {
            get {
                return ResourceManager.GetString("Needed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data loading failed.. 
        ///Please check your internet connection..
        /// </summary>
        public static string NeedInternet {
            get {
                return ResourceManager.GetString("NeedInternet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log off user.
        /// </summary>
        public static string NeedRelogUser {
            get {
                return ResourceManager.GetString("NeedRelogUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Need Seats.
        /// </summary>
        public static string NeedSeats {
            get {
                return ResourceManager.GetString("NeedSeats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        public static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Password.
        /// </summary>
        public static string NewPassword {
            get {
                return ResourceManager.GetString("NewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Record.
        /// </summary>
        public static string NewRecord {
            get {
                return ResourceManager.GetString("NewRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to News.
        /// </summary>
        public static string News {
            get {
                return ResourceManager.GetString("News", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ERROR: Image URL not valid!.
        /// </summary>
        public static string NewsController_Create_ERRORUImageURLNotValid {
            get {
                return ResourceManager.GetString("NewsController_Create_ERRORUImageURLNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By display region.
        /// </summary>
        public static string NewsController_NewsController_ByRegion {
            get {
                return ResourceManager.GetString("NewsController_NewsController_ByRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your News.
        /// </summary>
        public static string NewsMenu {
            get {
                return ResourceManager.GetString("NewsMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to News.
        /// </summary>
        public static string NewsTitleDesc {
            get {
                return ResourceManager.GetString("NewsTitleDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Me More...
        /// </summary>
        public static string NextCategory {
            get {
                return ResourceManager.GetString("NextCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Action.
        /// </summary>
        public static string NoAction {
            get {
                return ResourceManager.GetString("NoAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        public static string None {
            get {
                return ResourceManager.GetString("None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No RU translation.
        /// </summary>
        public static string NoRUTranslation {
            get {
                return ResourceManager.GetString("NoRUTranslation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not allowed.
        /// </summary>
        public static string NotAllowed {
            get {
                return ResourceManager.GetString("NotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nothing was found!.
        /// </summary>
        public static string NothingWasFound {
            get {
                return ResourceManager.GetString("NothingWasFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No time available.
        /// </summary>
        public static string NoTimeAvailable {
            get {
                return ResourceManager.GetString("NoTimeAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number.
        /// </summary>
        public static string Number {
            get {
                return ResourceManager.GetString("Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of seats:  {0}.
        /// </summary>
        public static string NumberOfSeats0 {
            get {
                return ResourceManager.GetString("NumberOfSeats0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to items.
        /// </summary>
        public static string NumDesc_Items_0 {
            get {
                return ResourceManager.GetString("NumDesc_Items_0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to item.
        /// </summary>
        public static string NumDesc_Items_1 {
            get {
                return ResourceManager.GetString("NumDesc_Items_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have {0} {1} in your list..
        /// </summary>
        public static string NumDesc_Items_Format {
            get {
                return ResourceManager.GetString("NumDesc_Items_Format", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to items.
        /// </summary>
        public static string NumDesc_Items_with0 {
            get {
                return ResourceManager.GetString("NumDesc_Items_with0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to items.
        /// </summary>
        public static string NumDesc_Items_with1 {
            get {
                return ResourceManager.GetString("NumDesc_Items_with1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to items.
        /// </summary>
        public static string NumDesc_Items_with2 {
            get {
                return ResourceManager.GetString("NumDesc_Items_with2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Object.
        /// </summary>
        public static string Object {
            get {
                return ResourceManager.GetString("Object", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fulltime.
        /// </summary>
        public static string OccupancyType_Fulltime {
            get {
                return ResourceManager.GetString("OccupancyType_Fulltime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partial.
        /// </summary>
        public static string OccupancyType_Partial {
            get {
                return ResourceManager.GetString("OccupancyType_Partial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text cannot be empty for the English language..
        /// </summary>
        public static string OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage {
            get {
                return ResourceManager.GetString("OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One More Step.
        /// </summary>
        public static string OneMoreStep {
            get {
                return ResourceManager.GetString("OneMoreStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Map.
        /// </summary>
        public static string OnMap {
            get {
                return ResourceManager.GetString("OnMap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Map.
        /// </summary>
        public static string OnMapSalon {
            get {
                return ResourceManager.GetString("OnMapSalon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oops!.
        /// </summary>
        public static string Oops {
            get {
                return ResourceManager.GetString("Oops", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open blog article.
        /// </summary>
        public static string OpenBlogArticle {
            get {
                return ResourceManager.GetString("OpenBlogArticle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data not found in your region..
        /// </summary>
        public static string OpenItemNotFound {
            get {
                return ResourceManager.GetString("OpenItemNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open product category.
        /// </summary>
        public static string OpenProdCatInApp {
            get {
                return ResourceManager.GetString("OpenProdCatInApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open product in app.
        /// </summary>
        public static string OpenProductInApp {
            get {
                return ResourceManager.GetString("OpenProductInApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order.
        /// </summary>
        public static string Order {
            get {
                return ResourceManager.GetString("Order", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make sure that the data provided is correct. Important: check that you have entered a valid phone number. You can also leave us message below..
        /// </summary>
        public static string OrderCaution {
            get {
                return ResourceManager.GetString("OrderCaution", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Canceled.
        /// </summary>
        public static string OrderDelveryStatus_Canceled {
            get {
                return ResourceManager.GetString("OrderDelveryStatus_Canceled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivered.
        /// </summary>
        public static string OrderDelveryStatus_Delivered {
            get {
                return ResourceManager.GetString("OrderDelveryStatus_Delivered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivering.
        /// </summary>
        public static string OrderDelveryStatus_Delivering {
            get {
                return ResourceManager.GetString("OrderDelveryStatus_Delivering", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expired.
        /// </summary>
        public static string OrderDelveryStatus_Expired {
            get {
                return ResourceManager.GetString("OrderDelveryStatus_Expired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created.
        /// </summary>
        public static string OrderDelveryStatus_None {
            get {
                return ResourceManager.GetString("OrderDelveryStatus_None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing.
        /// </summary>
        public static string OrderDelveryStatus_Processing {
            get {
                return ResourceManager.GetString("OrderDelveryStatus_Processing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApplePay.
        /// </summary>
        public static string OrderPaymentMethod_ApplePay {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_ApplePay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance.
        /// </summary>
        public static string OrderPaymentMethod_Balance {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Card.
        /// </summary>
        public static string OrderPaymentMethod_Card {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_Card", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash.
        /// </summary>
        public static string OrderPaymentMethod_Cash {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_Cash", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coupon Code.
        /// </summary>
        public static string OrderPaymentMethod_CouponCode {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_CouponCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GooglePay.
        /// </summary>
        public static string OrderPaymentMethod_GooglePay {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_GooglePay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -.
        /// </summary>
        public static string OrderPaymentMethod_None {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string OrderPaymentMethod_Other {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SamsungPay.
        /// </summary>
        public static string OrderPaymentMethod_SamsungPay {
            get {
                return ResourceManager.GetString("OrderPaymentMethod_SamsungPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Canceled.
        /// </summary>
        public static string OrderPaymentStatus_Canceled {
            get {
                return ResourceManager.GetString("OrderPaymentStatus_Canceled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed.
        /// </summary>
        public static string OrderPaymentStatus_Failed {
            get {
                return ResourceManager.GetString("OrderPaymentStatus_Failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpaid.
        /// </summary>
        public static string OrderPaymentStatus_None {
            get {
                return ResourceManager.GetString("OrderPaymentStatus_None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid.
        /// </summary>
        public static string OrderPaymentStatus_Payed {
            get {
                return ResourceManager.GetString("OrderPaymentStatus_Payed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expecting.
        /// </summary>
        public static string OrderPaymentStatus_Pending {
            get {
                return ResourceManager.GetString("OrderPaymentStatus_Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing.
        /// </summary>
        public static string OrderPaymentStatus_Processing {
            get {
                return ResourceManager.GetString("OrderPaymentStatus_Processing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refunded.
        /// </summary>
        public static string OrderPaymentStatus_Refunded {
            get {
                return ResourceManager.GetString("OrderPaymentStatus_Refunded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order Registered.
        /// </summary>
        public static string OrderProcessed {
            get {
                return ResourceManager.GetString("OrderProcessed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders.
        /// </summary>
        public static string Orders {
            get {
                return ResourceManager.GetString("Orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders Payed.
        /// </summary>
        public static string OrdersPayed {
            get {
                return ResourceManager.GetString("OrdersPayed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders Total.
        /// </summary>
        public static string OrdersTotal {
            get {
                return ResourceManager.GetString("OrdersTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to or enter your credentials.
        /// </summary>
        public static string OrEnterYourCredentials {
            get {
                return ResourceManager.GetString("OrEnterYourCredentials", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organization.
        /// </summary>
        public static string Organization {
            get {
                return ResourceManager.GetString("Organization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organizations.
        /// </summary>
        public static string Organizations {
            get {
                return ResourceManager.GetString("Organizations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modify...
        /// </summary>
        public static string OrUploadFromDisk {
            get {
                return ResourceManager.GetString("OrUploadFromDisk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Or use another service to log in.
        /// </summary>
        public static string OrUseAnotherServiceToLogIn {
            get {
                return ResourceManager.GetString("OrUseAnotherServiceToLogIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string Other {
            get {
                return ResourceManager.GetString("Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OTHER CATEGORIES.
        /// </summary>
        public static string OtherCategories {
            get {
                return ResourceManager.GetString("OtherCategories", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Our Choice.
        /// </summary>
        public static string OurChoice {
            get {
                return ResourceManager.GetString("OurChoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Us.
        /// </summary>
        public static string OurContacts {
            get {
                return ResourceManager.GetString("OurContacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outdated mobile app version.
        /// </summary>
        public static string OutdatedMobileAppVersion {
            get {
                return ResourceManager.GetString("OutdatedMobileAppVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AroMarket.
        /// </summary>
        public static string OwnerTitle {
            get {
                return ResourceManager.GetString("OwnerTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AroMarket.
        /// </summary>
        public static string OwnerTitleShort {
            get {
                return ResourceManager.GetString("OwnerTitleShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information.
        /// </summary>
        public static string PageContactsInfo {
            get {
                return ResourceManager.GetString("PageContactsInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How To Find Us.
        /// </summary>
        public static string PageHowToGetThereInstructions {
            get {
                return ResourceManager.GetString("PageHowToGetThereInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection failed. Please try again..
        /// </summary>
        public static string PageLogin_LoginAuth_ConnectionFailedPleaseTryAgain {
            get {
                return ResourceManager.GetString("PageLogin_LoginAuth_ConnectionFailedPleaseTryAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot password?.
        /// </summary>
        public static string PageLogin_LoginAuth_ForgotPassword {
            get {
                return ResourceManager.GetString("PageLogin_LoginAuth_ForgotPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        public static string PageLogin_LoginAuth_ResetPassword {
            get {
                return ResourceManager.GetString("PageLogin_LoginAuth_ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latest News.
        /// </summary>
        public static string PageNewsTitle {
            get {
                return ResourceManager.GetString("PageNewsTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to version.
        /// </summary>
        public static string PageSettings_PageSettings_Version {
            get {
                return ResourceManager.GetString("PageSettings_PageSettings_Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        public static string PageTitleSettings {
            get {
                return ResourceManager.GetString("PageTitleSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are You sure to clear your Wish List?.
        /// </summary>
        public static string PageWishList_OnBtnClearList_ConfirmClearList {
            get {
                return ResourceManager.GetString("PageWishList_OnBtnClearList_ConfirmClearList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Wish List.
        /// </summary>
        public static string PageWishList_OnBtnShare_МойСписокЖеланийTHALION {
            get {
                return ResourceManager.GetString("PageWishList_OnBtnShare_МойСписокЖеланийTHALION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Catalogue.
        /// </summary>
        public static string PageWishList_UpdateFavs_ToCatalogue {
            get {
                return ResourceManager.GetString("PageWishList_UpdateFavs_ToCatalogue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameters.
        /// </summary>
        public static string Parameters {
            get {
                return ResourceManager.GetString("Parameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent.
        /// </summary>
        public static string Parent {
            get {
                return ResourceManager.GetString("Parent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent category to insert this element into.
        /// </summary>
        public static string ParentElementToInsertCategoryInto {
            get {
                return ResourceManager.GetString("ParentElementToInsertCategoryInto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The password and confirmation password do not match..
        /// </summary>
        public static string PasswordAndConfirmationPasswordDoNotMatch {
            get {
                return ResourceManager.GetString("PasswordAndConfirmationPasswordDoNotMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Past Orders.
        /// </summary>
        public static string PastOrders {
            get {
                return ResourceManager.GetString("PastOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PatternUrl.
        /// </summary>
        public static string PatternUrl {
            get {
                return ResourceManager.GetString("PatternUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment.
        /// </summary>
        public static string Payment {
            get {
                return ResourceManager.GetString("Payment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We failed to process your payment, please try again with different details..
        /// </summary>
        public static string PaymentMessageFail {
            get {
                return ResourceManager.GetString("PaymentMessageFail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you, your order was successfully paid..
        /// </summary>
        public static string PaymentMessageSuccess {
            get {
                return ResourceManager.GetString("PaymentMessageSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment Method.
        /// </summary>
        public static string PaymentMethod {
            get {
                return ResourceManager.GetString("PaymentMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment Methods.
        /// </summary>
        public static string PaymentMethods {
            get {
                return ResourceManager.GetString("PaymentMethods", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payments.
        /// </summary>
        public static string Payments {
            get {
                return ResourceManager.GetString("Payments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PaymentStatus.
        /// </summary>
        public static string PaymentStatus {
            get {
                return ResourceManager.GetString("PaymentStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your phone number.
        /// </summary>
        public static string PhoneNumber {
            get {
                return ResourceManager.GetString("PhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Players Level.
        /// </summary>
        public static string PlayersLvl {
            get {
                return ResourceManager.GetString("PlayersLvl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please check you device.
        /// </summary>
        public static string PleaseCheckYouDevice {
            get {
                return ResourceManager.GetString("PleaseCheckYouDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please check your email to reset your password..
        /// </summary>
        public static string PleaseCheckYourEmailToResetYourPassword {
            get {
                return ResourceManager.GetString("PleaseCheckYourEmailToResetYourPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a user name for this site below and click the Register button to finish logging in..
        /// </summary>
        public static string PleaseEnterAUserNameForThisSiteBelow {
            get {
                return ResourceManager.GetString("PleaseEnterAUserNameForThisSiteBelow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter more characters!.
        /// </summary>
        public static string PleaseEnterMoreCharacters {
            get {
                return ResourceManager.GetString("PleaseEnterMoreCharacters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter your new password below..
        /// </summary>
        public static string PleaseEnterYourNewPasswordBelow {
            get {
                return ResourceManager.GetString("PleaseEnterYourNewPasswordBelow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please fill empty fields.
        /// </summary>
        public static string PleaseFillEmptyFields {
            get {
                return ResourceManager.GetString("PleaseFillEmptyFields", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please fill required fields.
        /// </summary>
        public static string PleaseFillRequiredFields {
            get {
                return ResourceManager.GetString("PleaseFillRequiredFields", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save this record to be able to add sub-records..
        /// </summary>
        public static string PleaseSaveThisRecordToBeAbleToAddSubRecords {
            get {
                return ResourceManager.GetString("PleaseSaveThisRecordToBeAbleToAddSubRecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Just a quick second...
        /// </summary>
        public static string PleaseWait {
            get {
                return ResourceManager.GetString("PleaseWait", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are You sure to export this section now?.
        /// </summary>
        public static string PlsConfirmExport {
            get {
                return ResourceManager.GetString("PlsConfirmExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HOT.
        /// </summary>
        public static string Popular {
            get {
                return ResourceManager.GetString("Popular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Popularity.
        /// </summary>
        public static string Popularity {
            get {
                return ResourceManager.GetString("Popularity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Book a place for the whole time.
        /// </summary>
        public static string PopularityDesc {
            get {
                return ResourceManager.GetString("PopularityDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position.
        /// </summary>
        public static string Position {
            get {
                return ResourceManager.GetString("Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Practice.
        /// </summary>
        public static string Practice {
            get {
                return ResourceManager.GetString("Practice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Practice Elements.
        /// </summary>
        public static string PracticeElements {
            get {
                return ResourceManager.GetString("PracticeElements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Practices.
        /// </summary>
        public static string Practices {
            get {
                return ResourceManager.GetString("Practices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press BACK once again to quit the application.
        /// </summary>
        public static string PressBACKOnceAgain {
            get {
                return ResourceManager.GetString("PressBACKOnceAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prev Category.
        /// </summary>
        public static string PrevCategory {
            get {
                return ResourceManager.GetString("PrevCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price.
        /// </summary>
        public static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Details.
        /// </summary>
        public static string PriceDetails {
            get {
                return ResourceManager.GetString("PriceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;per hour&quot; etc...
        /// </summary>
        public static string PriceDetailsDesc {
            get {
                return ResourceManager.GetString("PriceDetailsDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Mask.
        /// </summary>
        public static string PriceMask {
            get {
                return ResourceManager.GetString("PriceMask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Priority.
        /// </summary>
        public static string Priority {
            get {
                return ResourceManager.GetString("Priority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The higher the priority the higher item will be shown in list.
        /// </summary>
        public static string PriorityDesc {
            get {
                return ResourceManager.GetString("PriorityDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Privacy.
        /// </summary>
        public static string PrivacyPolicy {
            get {
                return ResourceManager.GetString("PrivacyPolicy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order Confirmation.
        /// </summary>
        public static string ProcessingOrder {
            get {
                return ResourceManager.GetString("ProcessingOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing your booking...
        /// </summary>
        public static string ProcessingYourBooking {
            get {
                return ResourceManager.GetString("ProcessingYourBooking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Elements.
        /// </summary>
        public static string ProductElements {
            get {
                return ResourceManager.GetString("ProductElements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products.
        /// </summary>
        public static string Products {
            get {
                return ResourceManager.GetString("Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Any Category.
        /// </summary>
        public static string ProductsController_CreateDropdownList_ANYCAT {
            get {
                return ResourceManager.GetString("ProductsController_CreateDropdownList_ANYCAT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Promo Action.
        /// </summary>
        public static string PromoAction {
            get {
                return ResourceManager.GetString("PromoAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Promo Actons.
        /// </summary>
        public static string PromoActons {
            get {
                return ResourceManager.GetString("PromoActons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed.
        /// </summary>
        public static string PromoStatus_Closed {
            get {
                return ResourceManager.GetString("PromoStatus_Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incoming.
        /// </summary>
        public static string PromoStatus_Incoming {
            get {
                return ResourceManager.GetString("PromoStatus_Incoming", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        public static string PromoStatus_Open {
            get {
                return ResourceManager.GetString("PromoStatus_Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string PromoStatus_Other {
            get {
                return ResourceManager.GetString("PromoStatus_Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public Notes.
        /// </summary>
        public static string PublicNotes {
            get {
                return ResourceManager.GetString("PublicNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visible to customers on the mobile app.
        /// </summary>
        public static string PublicNotesDesc {
            get {
                return ResourceManager.GetString("PublicNotesDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Published.
        /// </summary>
        public static string Published {
            get {
                return ResourceManager.GetString("Published", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active users.
        /// </summary>
        public static string PushActiveUsers {
            get {
                return ResourceManager.GetString("PushActiveUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash payment was authorized for order {0}, amount due is {1}..
        /// </summary>
        public static string PushCashPaymentAllowed {
            get {
                return ResourceManager.GetString("PushCashPaymentAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash payment was declined for order {0}..
        /// </summary>
        public static string PushCashPaymentDeclined {
            get {
                return ResourceManager.GetString("PushCashPaymentDeclined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Engaged users.
        /// </summary>
        public static string PushEngagedUsers {
            get {
                return ResourceManager.GetString("PushEngagedUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive users.
        /// </summary>
        public static string PushInactiveUsers {
            get {
                return ResourceManager.GetString("PushInactiveUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push Messages.
        /// </summary>
        public static string PushMessages {
            get {
                return ResourceManager.GetString("PushMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push messages were not configured for You yet..
        /// </summary>
        public static string PushMessagesWereNotConfigured {
            get {
                return ResourceManager.GetString("PushMessagesWereNotConfigured", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order was canceled: {0}..
        /// </summary>
        public static string PushOrderCanceled {
            get {
                return ResourceManager.GetString("PushOrderCanceled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order created {0}..
        /// </summary>
        public static string PushOrderCreated {
            get {
                return ResourceManager.GetString("PushOrderCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank You, your order {0} was successfully payed!.
        /// </summary>
        public static string PushOrderPayed {
            get {
                return ResourceManager.GetString("PushOrderPayed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QR Code Image Url.
        /// </summary>
        public static string QRCodeImageUrl {
            get {
                return ResourceManager.GetString("QRCodeImageUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        public static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question.
        /// </summary>
        public static string Question {
            get {
                return ResourceManager.GetString("Question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rating.
        /// </summary>
        public static string Rating {
            get {
                return ResourceManager.GetString("Rating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Try to connect.
        /// </summary>
        public static string Reconnect {
            get {
                return ResourceManager.GetString("Reconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link.
        /// </summary>
        public static string Redirect {
            get {
                return ResourceManager.GetString("Redirect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference code.
        /// </summary>
        public static string RefCodeDesc {
            get {
                return ResourceManager.GetString("RefCodeDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refunds.
        /// </summary>
        public static string Refunds {
            get {
                return ResourceManager.GetString("Refunds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        public static string Region {
            get {
                return ResourceManager.GetString("Region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regions.
        /// </summary>
        public static string Regions {
            get {
                return ResourceManager.GetString("Regions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region Inside Mobile App.
        /// </summary>
        public static string RegionsTitleDesc {
            get {
                return ResourceManager.GetString("RegionsTitleDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register.
        /// </summary>
        public static string Register {
            get {
                return ResourceManager.GetString("Register", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register Account.
        /// </summary>
        public static string RegisterAccount {
            get {
                return ResourceManager.GetString("RegisterAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a new account.
        /// </summary>
        public static string RegisterAsANewUser {
            get {
                return ResourceManager.GetString("RegisterAsANewUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register.
        /// </summary>
        public static string RegisterTitle {
            get {
                return ResourceManager.GetString("RegisterTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registration failed. Please check you have provided a valid phone number or try again later..
        /// </summary>
        public static string RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater {
            get {
                return ResourceManager.GetString("RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release Date.
        /// </summary>
        public static string ReleaseDate {
            get {
                return ResourceManager.GetString("ReleaseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reloading booking data...
        /// </summary>
        public static string ReloadingBookingData {
            get {
                return ResourceManager.GetString("ReloadingBookingData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remember Me.
        /// </summary>
        public static string RememberMe {
            get {
                return ResourceManager.GetString("RememberMe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remember Me?.
        /// </summary>
        public static string RememberMe2 {
            get {
                return ResourceManager.GetString("RememberMe2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string Remove {
            get {
                return ResourceManager.GetString("Remove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove From Favorites.
        /// </summary>
        public static string RemoveFromFavs {
            get {
                return ResourceManager.GetString("RemoveFromFavs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required.
        /// </summary>
        public static string Required {
            get {
                return ResourceManager.GetString("Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not used..
        /// </summary>
        public static string ReservedField {
            get {
                return ResourceManager.GetString("ReservedField", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        public static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel Changes.
        /// </summary>
        public static string ResetChanges {
            get {
                return ResourceManager.GetString("ResetChanges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        public static string ResetPassword {
            get {
                return ResourceManager.GetString("ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please reset your password by clicking &lt;a href=&quot;{0}&quot;&gt;here&lt;/a&gt;..
        /// </summary>
        public static string ResetYourPasswordMailBody {
            get {
                return ResourceManager.GetString("ResetYourPasswordMailBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Admin.
        /// </summary>
        public static string RoleAdmin {
            get {
                return ResourceManager.GetString("RoleAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Editor.
        /// </summary>
        public static string RoleEditor {
            get {
                return ResourceManager.GetString("RoleEditor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Merchandiser.
        /// </summary>
        public static string RoleMerchandiser {
            get {
                return ResourceManager.GetString("RoleMerchandiser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guest.
        /// </summary>
        public static string RoleNoRole {
            get {
                return ResourceManager.GetString("RoleNoRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Superuser.
        /// </summary>
        public static string RoleSuperuser {
            get {
                return ResourceManager.GetString("RoleSuperuser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Category.
        /// </summary>
        public static string RootCategory {
            get {
                return ResourceManager.GetString("RootCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By clicking on any button on this page, you consent to the processing of personal data and agree to our privacy policy..
        /// </summary>
        public static string S_PrivacyMessage {
            get {
                return ResourceManager.GetString("S_PrivacyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hourly.
        /// </summary>
        public static string SalaryType_Hourly {
            get {
                return ResourceManager.GetString("SalaryType_Hourly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monthly.
        /// </summary>
        public static string SalaryType_Monthly {
            get {
                return ResourceManager.GetString("SalaryType_Monthly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salon List.
        /// </summary>
        public static string SalonList {
            get {
                return ResourceManager.GetString("SalonList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved for later.
        /// </summary>
        public static string SavedForLater {
            get {
                return ResourceManager.GetString("SavedForLater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save for later.
        /// </summary>
        public static string SaveForLater {
            get {
                return ResourceManager.GetString("SaveForLater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save for quick access!.
        /// </summary>
        public static string SaveItToFavorites {
            get {
                return ResourceManager.GetString("SaveItToFavorites", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedules.
        /// </summary>
        public static string Schedules {
            get {
                return ResourceManager.GetString("Schedules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule Type.
        /// </summary>
        public static string ScheduleType {
            get {
                return ResourceManager.GetString("ScheduleType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If with fixed dates, days of the week that are not used, and vice versa..
        /// </summary>
        public static string ScheduleTypeDesc {
            get {
                return ResourceManager.GetString("ScheduleTypeDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SEE ALSO.
        /// </summary>
        public static string SeaAlso {
            get {
                return ResourceManager.GetString("SeaAlso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search...
        /// </summary>
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search by name .
        /// </summary>
        public static string SearchByName {
            get {
                return ResourceManager.GetString("SearchByName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search keywords.
        /// </summary>
        public static string SearchKeywords {
            get {
                return ResourceManager.GetString("SearchKeywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Products.
        /// </summary>
        public static string SearchProd {
            get {
                return ResourceManager.GetString("SearchProd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Results.
        /// </summary>
        public static string SearchResults {
            get {
                return ResourceManager.GetString("SearchResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Centers.
        /// </summary>
        public static string SearchSalonLabel {
            get {
                return ResourceManager.GetString("SearchSalonLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seats Taken.
        /// </summary>
        public static string SeatsTaken {
            get {
                return ResourceManager.GetString("SeatsTaken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sections.
        /// </summary>
        public static string Sections {
            get {
                return ResourceManager.GetString("Sections", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secure Payment.
        /// </summary>
        public static string SecurePayment {
            get {
                return ResourceManager.GetString("SecurePayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to See also: .
        /// </summary>
        public static string SeeAlso {
            get {
                return ResourceManager.GetString("SeeAlso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language selection.
        /// </summary>
        public static string SelectLanguage {
            get {
                return ResourceManager.GetString("SelectLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send now.
        /// </summary>
        public static string SendNow {
            get {
                return ResourceManager.GetString("SendNow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        public static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services.
        /// </summary>
        public static string Services {
            get {
                return ResourceManager.GetString("Services", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services Categories.
        /// </summary>
        public static string ServicesCategories {
            get {
                return ResourceManager.GetString("ServicesCategories", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        public static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose Your Tabs  ({0}/{1} min {2}).
        /// </summary>
        public static string Settings_ChooseYourTabsMinMax {
            get {
                return ResourceManager.GetString("Settings_ChooseYourTabsMinMax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to © 2019 AppoMobi and respective content owners.
        /// </summary>
        public static string Settings_Copyright {
            get {
                return ResourceManager.GetString("Settings_Copyright", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select tabs to be shown.
        /// </summary>
        public static string Settings_FavsTabs {
            get {
                return ResourceManager.GetString("Settings_FavsTabs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No text bottom menu.
        /// </summary>
        public static string Settings_NoTitlesInTabs {
            get {
                return ResourceManager.GetString("Settings_NoTitlesInTabs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language selection.
        /// </summary>
        public static string Settings_SelectLanguage {
            get {
                return ResourceManager.GetString("Settings_SelectLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Theme.
        /// </summary>
        public static string Settings_SelectTheme {
            get {
                return ResourceManager.GetString("Settings_SelectTheme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push messages remain silent.
        /// </summary>
        public static string Settings_SilentPush {
            get {
                return ResourceManager.GetString("Settings_SilentPush", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable background  animations for battery saving.
        /// </summary>
        public static string SettingsAnimation {
            get {
                return ResourceManager.GetString("SettingsAnimation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interface.
        /// </summary>
        public static string SettingsInterface {
            get {
                return ResourceManager.GetString("SettingsInterface", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No text bottom menu.
        /// </summary>
        public static string SettingsShowTabbedTitles {
            get {
                return ResourceManager.GetString("SettingsShowTabbedTitles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show page My Salon (Favorite) on program start.
        /// </summary>
        public static string SettingsStartFav {
            get {
                return ResourceManager.GetString("SettingsStartFav", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Always show welcome tutorial at program start.
        /// </summary>
        public static string SettingsTutorial {
            get {
                return ResourceManager.GetString("SettingsTutorial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sex Restriction.
        /// </summary>
        public static string SexRestriction {
            get {
                return ResourceManager.GetString("SexRestriction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Should be exported.
        /// </summary>
        public static string ShouldBeExported {
            get {
                return ResourceManager.GetString("ShouldBeExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Should not be exported.
        /// </summary>
        public static string ShouldNotBeExported {
            get {
                return ResourceManager.GetString("ShouldNotBeExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show.
        /// </summary>
        public static string ShowList {
            get {
                return ResourceManager.GetString("ShowList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discover On Map.
        /// </summary>
        public static string ShowOnMap {
            get {
                return ResourceManager.GetString("ShowOnMap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show on page.
        /// </summary>
        public static string ShowOnPage {
            get {
                return ResourceManager.GetString("ShowOnPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Welcome Slides.
        /// </summary>
        public static string ShowWelcomeSlides {
            get {
                return ResourceManager.GetString("ShowWelcomeSlides", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push messages remain silent.
        /// </summary>
        public static string SilentPush {
            get {
                return ResourceManager.GetString("SilentPush", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Simple message.
        /// </summary>
        public static string SimpleMessage {
            get {
                return ResourceManager.GetString("SimpleMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Since.
        /// </summary>
        public static string Since {
            get {
                return ResourceManager.GetString("Since", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Site.
        /// </summary>
        public static string Site {
            get {
                return ResourceManager.GetString("Site", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading.
        /// </summary>
        public static string SiteLoading {
            get {
                return ResourceManager.GetString("SiteLoading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HOT PRODUCTS.
        /// </summary>
        public static string SliderAnnounce {
            get {
                return ResourceManager.GetString("SliderAnnounce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, we&apos;re doing some work on the site.
        /// </summary>
        public static string SorryWeReDoingSomeWorkOnTheSite {
            get {
                return ResourceManager.GetString("SorryWeReDoingSomeWorkOnTheSite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort by Abc.
        /// </summary>
        public static string SortAbc {
            get {
                return ResourceManager.GetString("SortAbc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Level.
        /// </summary>
        public static string SortByLevel {
            get {
                return ResourceManager.GetString("SortByLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string SortByStatus {
            get {
                return ResourceManager.GetString("SortByStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Category.
        /// </summary>
        public static string SortCat {
            get {
                return ResourceManager.GetString("SortCat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Code.
        /// </summary>
        public static string SortCode {
            get {
                return ResourceManager.GetString("SortCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Edition Date.
        /// </summary>
        public static string SortDate {
            get {
                return ResourceManager.GetString("SortDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Default.
        /// </summary>
        public static string SortDefault {
            get {
                return ResourceManager.GetString("SortDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort by Km.
        /// </summary>
        public static string SortKm {
            get {
                return ResourceManager.GetString("SortKm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort List.
        /// </summary>
        public static string SortList {
            get {
                return ResourceManager.GetString("SortList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Notes.
        /// </summary>
        public static string SortNotes {
            get {
                return ResourceManager.GetString("SortNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Release Date.
        /// </summary>
        public static string SortOutDate {
            get {
                return ResourceManager.GetString("SortOutDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Price.
        /// </summary>
        public static string SortPrice {
            get {
                return ResourceManager.GetString("SortPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort Priority.
        /// </summary>
        public static string SortPriority {
            get {
                return ResourceManager.GetString("SortPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Rating.
        /// </summary>
        public static string SortRating {
            get {
                return ResourceManager.GetString("SortRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Name.
        /// </summary>
        public static string SortTitle {
            get {
                return ResourceManager.GetString("SortTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Components.
        /// </summary>
        public static string Specifications {
            get {
                return ResourceManager.GetString("Specifications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Splash Logo.
        /// </summary>
        public static string SplashLogo {
            get {
                return ResourceManager.GetString("SplashLogo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sport.
        /// </summary>
        public static string Sport {
            get {
                return ResourceManager.GetString("Sport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string StartEvent {
            get {
                return ResourceManager.GetString("StartEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to START NOW.
        /// </summary>
        public static string StartUp {
            get {
                return ResourceManager.GetString("StartUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        public static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed.
        /// </summary>
        public static string StatusConfirmed {
            get {
                return ResourceManager.GetString("StatusConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disapproved.
        /// </summary>
        public static string StatusDisapproved {
            get {
                return ResourceManager.GetString("StatusDisapproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending confirmation.
        /// </summary>
        public static string StatusPendingConfirmation {
            get {
                return ResourceManager.GetString("StatusPendingConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to String.
        /// </summary>
        public static string String {
            get {
                return ResourceManager.GetString("String", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The {0} must be at least {2} characters long..
        /// </summary>
        public static string StringLengthError {
            get {
                return ResourceManager.GetString("StringLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subcategories.
        /// </summary>
        public static string Subcategories {
            get {
                return ResourceManager.GetString("Subcategories", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sub-categories:.
        /// </summary>
        public static string SubCatsHere {
            get {
                return ResourceManager.GetString("SubCatsHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subtitle.
        /// </summary>
        public static string Subtitle {
            get {
                return ResourceManager.GetString("Subtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System.
        /// </summary>
        public static string System {
            get {
                return ResourceManager.GetString("System", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not visible in mobile app, system name used for selecting this item in lists etc.
        /// </summary>
        public static string SystemNameHint {
            get {
                return ResourceManager.GetString("SystemNameHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to read more.
        /// </summary>
        public static string TapToRead {
            get {
                return ResourceManager.GetString("TapToRead", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Platform.
        /// </summary>
        public static string TargetPlatfrom {
            get {
                return ResourceManager.GetString("TargetPlatfrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recipients.
        /// </summary>
        public static string TargetSegment {
            get {
                return ResourceManager.GetString("TargetSegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Parameters.
        /// </summary>
        public static string TargetSegmentParameters {
            get {
                return ResourceManager.GetString("TargetSegmentParameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team.
        /// </summary>
        public static string Team {
            get {
                return ResourceManager.GetString("Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teams.
        /// </summary>
        public static string Teams {
            get {
                return ResourceManager.GetString("Teams", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tel.
        /// </summary>
        public static string Tel {
            get {
                return ResourceManager.GetString("Tel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client name in the control panel.
        /// </summary>
        public static string TenantNameDesc {
            get {
                return ResourceManager.GetString("TenantNameDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tenants.
        /// </summary>
        public static string Tenants {
            get {
                return ResourceManager.GetString("Tenants", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test.
        /// </summary>
        public static string Test {
            get {
                return ResourceManager.GetString("Test", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        public static string Text {
            get {
                return ResourceManager.GetString("Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank You!.
        /// </summary>
        public static string ThankYou {
            get {
                return ResourceManager.GetString("ThankYou", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for being patient. We are doing some work on the site that will be back soon..
        /// </summary>
        public static string ThankYouForBeingPatient {
            get {
                return ResourceManager.GetString("ThankYouForBeingPatient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for confirming your email. Please.
        /// </summary>
        public static string ThankYouForConfirmingYourEmailPlease {
            get {
                return ResourceManager.GetString("ThankYouForConfirmingYourEmailPlease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Theme.
        /// </summary>
        public static string Theme {
            get {
                return ResourceManager.GetString("Theme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time.
        /// </summary>
        public static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Time.
        /// </summary>
        public static string TimeEnd {
            get {
                return ResourceManager.GetString("TimeEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Time.
        /// </summary>
        public static string TimeStart {
            get {
                return ResourceManager.GetString("TimeStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displayed title .
        /// </summary>
        public static string TitleDesc {
            get {
                return ResourceManager.GetString("TitleDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details.
        /// </summary>
        public static string TitleDetails {
            get {
                return ResourceManager.GetString("TitleDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To be exported.
        /// </summary>
        public static string ToBeExported {
            get {
                return ResourceManager.GetString("ToBeExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Summary.
        /// </summary>
        public static string ToCatRoot {
            get {
                return ResourceManager.GetString("ToCatRoot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello, .
        /// </summary>
        public static string ToolbarHello {
            get {
                return ResourceManager.GetString("ToolbarHello", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log In.
        /// </summary>
        public static string ToolbarLogin {
            get {
                return ResourceManager.GetString("ToolbarLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Do not forget to) Log Off.
        /// </summary>
        public static string ToolbarLogoff {
            get {
                return ResourceManager.GetString("ToolbarLogoff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register.
        /// </summary>
        public static string ToolbarRegister {
            get {
                return ResourceManager.GetString("ToolbarRegister", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Full Salon List.
        /// </summary>
        public static string ToSalonList {
            get {
                return ResourceManager.GetString("ToSalonList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total.
        /// </summary>
        public static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total installations.
        /// </summary>
        public static string TotalInstallations {
            get {
                return ResourceManager.GetString("TotalInstallations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ToursCategories {
            get {
                return ResourceManager.GetString("ToursCategories", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Treatment.
        /// </summary>
        public static string Treatment {
            get {
                return ResourceManager.GetString("Treatment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Treatments.
        /// </summary>
        public static string Treatments {
            get {
                return ResourceManager.GetString("Treatments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tweak Mobile.
        /// </summary>
        public static string TweakApp {
            get {
                return ResourceManager.GetString("TweakApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Two-Factor Authentication.
        /// </summary>
        public static string TwoFactorAuthentication {
            get {
                return ResourceManager.GetString("TwoFactorAuthentication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unavalable.
        /// </summary>
        public static string Unavalable {
            get {
                return ResourceManager.GetString("Unavalable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Under construction.
        /// </summary>
        public static string UnderConstruction {
            get {
                return ResourceManager.GetString("UnderConstruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Units.
        /// </summary>
        public static string Units {
            get {
                return ResourceManager.GetString("Units", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown.
        /// </summary>
        public static string Unknown {
            get {
                return ResourceManager.GetString("Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unsuccessful login with service..
        /// </summary>
        public static string UnsuccessfulLoginWithService {
            get {
                return ResourceManager.GetString("UnsuccessfulLoginWithService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We have released an update, please update the app!.
        /// </summary>
        public static string UpdateNeded {
            get {
                return ResourceManager.GetString("UpdateNeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Updating data...
        /// </summary>
        public static string UpdatingData {
            get {
                return ResourceManager.GetString("UpdatingData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can upload a file or enter an existing URL in the next field.
        /// </summary>
        public static string UploadFileFieldDesc {
            get {
                return ResourceManager.GetString("UploadFileFieldDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload image.
        /// </summary>
        public static string UploadImage {
            get {
                return ResourceManager.GetString("UploadImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload MINI-image.
        /// </summary>
        public static string UploadMiniImage {
            get {
                return ResourceManager.GetString("UploadMiniImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Files.
        /// </summary>
        public static string Uploads {
            get {
                return ResourceManager.GetString("Uploads", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use a local account.
        /// </summary>
        public static string UseALocalAccountToLogIn {
            get {
                return ResourceManager.GetString("UseALocalAccountToLogIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        public static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User with this phone number already registered..
        /// </summary>
        public static string UserWithThisPhoneNumberAlreadyRegistered {
            get {
                return ResourceManager.GetString("UserWithThisPhoneNumberAlreadyRegistered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active.
        /// </summary>
        public static string VacancyState_Active {
            get {
                return ResourceManager.GetString("VacancyState_Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed.
        /// </summary>
        public static string VacancyState_Closed {
            get {
                return ResourceManager.GetString("VacancyState_Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Draft.
        /// </summary>
        public static string VacancyState_Draft {
            get {
                return ResourceManager.GetString("VacancyState_Draft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Valid username required.
        /// </summary>
        public static string ValidUsernameRequired {
            get {
                return ResourceManager.GetString("ValidUsernameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verifying code...
        /// </summary>
        public static string VerifyingCode {
            get {
                return ResourceManager.GetString("VerifyingCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View.
        /// </summary>
        public static string View {
            get {
                return ResourceManager.GetString("View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VimeoId.
        /// </summary>
        public static string VimeoId {
            get {
                return ResourceManager.GetString("VimeoId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VKontakte.
        /// </summary>
        public static string VK {
            get {
                return ResourceManager.GetString("VK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume.
        /// </summary>
        public static string Volume {
            get {
                return ResourceManager.GetString("Volume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to WallpaperUrl.
        /// </summary>
        public static string WallpaperUrl {
            get {
                return ResourceManager.GetString("WallpaperUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Was well exported.
        /// </summary>
        public static string WasWellExported {
            get {
                return ResourceManager.GetString("WasWellExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back.
        /// </summary>
        public static string WebBack {
            get {
                return ResourceManager.GetString("WebBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days Of Week.
        /// </summary>
        public static string WeekDays {
            get {
                return ResourceManager.GetString("WeekDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We have sent a verification code to your number.
        /// </summary>
        public static string WeHaveSentAVerificationCodeToYourNumber {
            get {
                return ResourceManager.GetString("WeHaveSentAVerificationCodeToYourNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We have sent you a confirmation code by SMS. Please enter it below to process your booking:.
        /// </summary>
        public static string WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking {
            get {
                return ResourceManager.GetString("WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome, Guest!.
        /// </summary>
        public static string WelcomeGuest {
            get {
                return ResourceManager.GetString("WelcomeGuest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show On Map.
        /// </summary>
        public static string WeOnMap {
            get {
                return ResourceManager.GetString("WeOnMap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Where To Buy.
        /// </summary>
        public static string WhereToBuy {
            get {
                return ResourceManager.GetString("WhereToBuy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Width.
        /// </summary>
        public static string Width {
            get {
                return ResourceManager.GetString("Width", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to With fixed dates.
        /// </summary>
        public static string WithFixedDate {
            get {
                return ResourceManager.GetString("WithFixedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Without description..
        /// </summary>
        public static string WithoutDescription {
            get {
                return ResourceManager.GetString("WithoutDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Women Only.
        /// </summary>
        public static string WomenOnly {
            get {
                return ResourceManager.GetString("WomenOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working Days.
        /// </summary>
        public static string WorkingDays {
            get {
                return ResourceManager.GetString("WorkingDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working Hours.
        /// </summary>
        public static string WorkingTime {
            get {
                return ResourceManager.GetString("WorkingTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working Hours Detaled.
        /// </summary>
        public static string WorkingTimeDetailed {
            get {
                return ResourceManager.GetString("WorkingTimeDetailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working Hours To.
        /// </summary>
        public static string WorkingTimeEnd {
            get {
                return ResourceManager.GetString("WorkingTimeEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Break end.
        /// </summary>
        public static string WorkingTimePauseEnd {
            get {
                return ResourceManager.GetString("WorkingTimePauseEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Break start.
        /// </summary>
        public static string WorkingTimePauseStart {
            get {
                return ResourceManager.GetString("WorkingTimePauseStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working Hours From.
        /// </summary>
        public static string WorkingTimeStart {
            get {
                return ResourceManager.GetString("WorkingTimeStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wrong code entered..
        /// </summary>
        public static string WrongCodeEntered {
            get {
                return ResourceManager.GetString("WrongCodeEntered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Notes.
        /// </summary>
        public static string X_NotesFromCustomer {
            get {
                return ResourceManager.GetString("X_NotesFromCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have an active order!.
        /// </summary>
        public static string YouHaveActiveOrder {
            get {
                return ResourceManager.GetString("YouHaveActiveOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have active orders!.
        /// </summary>
        public static string YouHaveActiveOrders {
            get {
                return ResourceManager.GetString("YouHaveActiveOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have searched for.
        /// </summary>
        public static string YouHaveSearched {
            get {
                return ResourceManager.GetString("YouHaveSearched", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have tried too many times, please try again in {0} mins..
        /// </summary>
        public static string YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins {
            get {
                return ResourceManager.GetString("YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must accept terms and conditions..
        /// </summary>
        public static string YouMustAcceptTermsAndConditions {
            get {
                return ResourceManager.GetString("YouMustAcceptTermsAndConditions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your name.
        /// </summary>
        public static string YourFName {
            get {
                return ResourceManager.GetString("YourFName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Family name.
        /// </summary>
        public static string YourLName {
            get {
                return ResourceManager.GetString("YourLName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your name.
        /// </summary>
        public static string YourName {
            get {
                return ResourceManager.GetString("YourName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your new password has been set..
        /// </summary>
        public static string YourNewPasswordHasBeenSet {
            get {
                return ResourceManager.GetString("YourNewPasswordHasBeenSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Order.
        /// </summary>
        public static string YourOrder {
            get {
                return ResourceManager.GetString("YourOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your security code is: .
        /// </summary>
        public static string YourSecCode {
            get {
                return ResourceManager.GetString("YourSecCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your security code is {0}. Use it to login to AppoMobi Control Panel..
        /// </summary>
        public static string YourSecCodeLoginMask {
            get {
                return ResourceManager.GetString("YourSecCodeLoginMask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You&apos;ve successfully authenticated with.
        /// </summary>
        public static string YouVeSuccessfullyAuthenticatedWith {
            get {
                return ResourceManager.GetString("YouVeSuccessfullyAuthenticatedWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share.
        /// </summary>
        public static string Поделиться {
            get {
                return ResourceManager.GetString("Поделиться", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please check the data You entered is valid..
        /// </summary>
        public static string ПроверьтеКорректностьВведенныхДанных {
            get {
                return ResourceManager.GetString("ПроверьтеКорректностьВведенныхДанных", resourceCulture);
            }
        }
    }
}
