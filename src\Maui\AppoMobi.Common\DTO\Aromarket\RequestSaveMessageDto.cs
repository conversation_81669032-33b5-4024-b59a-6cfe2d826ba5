﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public class RequestSaveMessageDto : WithTokenDto
    {
 

        [JsonProperty("city_id")]
        public string City { get; set; }

        [JsonProperty("fio")]
        public string Fullname { get; set; }

        [JsonProperty("phone")]
        public string Phone { get; set; }

        [JsonProperty("text")]
        public string Text { get; set; }


        [JsonProperty("line_id")]
        public string Line { get; set; }
        /// <summary>
        /// message - сообщение из обратной связи, complain - жалоба
        /// </summary>
        [JsonProperty("type")]
        public string MessageType { get; set; }

        //[JsonProperty("email")]
        //public string Email { get; set; }

    }
}