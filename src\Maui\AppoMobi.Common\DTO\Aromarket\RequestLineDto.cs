﻿using System.Text;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    #region get_line    

    public class RequestLineDto : WithTokenDto
    {


        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("user_uid")]
        public string UserId { get; set; }

    }

    /*
    Cannot deserialize the current JSON object (e.g. {"name":"value"}) into type 'System.Collections.Generic.List`1[AppoMobi.Common.DTO.Aromarket.RemoteImage]' because the type requires a JSON array(e.g. [1, 2, 3]) to deserialize correctly.
    To fix this error either change the JSON to a JSON array (e.g. [1, 2, 3]) or change the deserialized type so that it is a normal .NET type (e.g.not a primitive type like integer, not a collection type like an array or List<T>) that can be deserialized from a JSON object. 
    JsonObjectAttribute can also be added to the type to force it to deserialize from a JSON object.
    Path 'products[2].images', line 121, position 7.
    Newtonsoft.Json.JsonSerializationException: Cannot deserialize the current JSON object (e.g. { "name":"value"}) into type 'System.Collections.Generic.List`1[AppoMobi.Common.DTO.Aromarket.RemoteImage]' because the type requires a JSON array (e.g. [1,2,3]) to deserialize correctly.
    To fix this error either change the JSON to a JSON array (e.g. [1,2,3]) or change the deserialized type so that it is a normal .NET type (e.g. not a primitive type like integer, not a collection type like an array or List<T>) that can be deserialized from a JSON object. JsonObjectAttribute can also be added to the type to force it to deserialize from a JSON object.
    */





    #endregion
}
