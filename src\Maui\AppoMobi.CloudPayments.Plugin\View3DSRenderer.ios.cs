using System;
using System.Drawing;
using System.Reflection;
using System.Text;
using AppoMobi.CloudPayments;
using AppoMobi.CloudPayments.Models;
using AppoMobi.CloudPayments.Platform;
using AppoMobi.CloudPayments.Plugin;
using Foundation;
using Newtonsoft.Json.Linq;
using WebKit;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;


namespace AppoMobi.CloudPayments.Platform
{
    public class View3DSHandler : WebViewHandler, IView3DS
    {

        /// <summary>
        /// Register the handler
        /// </summary>
        public static void Register()
        {
            Microsoft.Maui.Handlers.WebViewHandler.Mapper.AppendToMapping(nameof(View3DS), (handler, view) =>
            {
                if (view is View3DS view3ds)
                {
                    view3ds.Renderer = (IView3DS)handler;
                }
            });
        }

        protected override void ConnectHandler(WebKit.WKWebView platformView)
        {
            base.ConnectHandler(platformView);

            if (VirtualView is View3DS formsControl)
            {
                FormsControl = formsControl;
                FormsControl.Renderer = this;

                platformView.Opaque = false;

                if (FormsControl.BackgroundColor != Colors.Transparent)
                {
                    platformView.BackgroundColor = FormsControl.BackgroundColor.ToPlatform();
                }

                platformView.NavigationDelegate = new CustomDelegate(this);
            }
        }

        public View3DS FormsControl { get; set; }

        public void Post(string url, string body)
        {
            var request = new NSMutableUrlRequest(new NSUrl(url));
            request.HttpMethod = "POST";
            request["Content-Type"] = "application/x-www-form-urlencoded";
            request.Body = NSData.FromString(body);
            request["Content-Length"] = request.Body.Length.ToString();
            request.CachePolicy = NSUrlRequestCachePolicy.ReloadIgnoringCacheData;
            NSUrlCache.SharedCache.RemoveCachedResponse(request);
            
            Console.WriteLine($"[CloudPayments] 3DS\nurl: {url}\nbody: {body}");
            PlatformView.LoadRequest(request);
        }


        protected override void DisconnectHandler(WebKit.WKWebView platformView)
        {
            if (platformView != null)
            {
                platformView.NavigationDelegate?.Dispose();
                platformView.NavigationDelegate = null;
            }

            FormsControl?.Dispose();
            base.DisconnectHandler(platformView);
        }

    }

    public class CustomDelegate : WKNavigationDelegate
    {
        private readonly View3DSHandler _renderer;

        public CustomDelegate(View3DSHandler renderer)
        {
            _weakRenderer = new WeakReference<View3DSHandler>(renderer);
        }

        public View3DSHandler Renderer
        {
            get
            {
                if (!_weakRenderer.TryGetTarget(out View3DSHandler value))
                {
                    return default;
                }

                return value;
            }
        }

        private WeakReference<View3DSHandler> _weakRenderer;

        public string GetStrBetweenTags(string value,
            string startTag,
            string endTag)
        {
            if (value.Contains(startTag) && value.Contains(endTag))
            {
                int index = value.IndexOf(startTag) + startTag.Length;
                return value.Substring(index, value.IndexOf(endTag) - index);
            }
            else
                return null;
        }

        public override void DidFinishNavigation(WKWebView webView, WKNavigation navigation)
        {
            System.Diagnostics.Debug.WriteLine("[CloudPayments] onPageFinished: " + webView.Url.AbsoluteString);

            if (webView.Url.AbsoluteString.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                //3DS complete
                var js =
                    "(function() { return (document.getElementsByTagName('html')[0].innerHTML); })();";

                

                webView.EvaluateJavaScript(new NSString(js), (value, error) =>
                {

                    var html = System.Text.RegularExpressions.Regex.Unescape(value.Description);
                    string json = GetStrBetweenTags(html, "<body>", "</body>");

                    var jsonObj = JObject.Parse(json);
                    System.Diagnostics.Debug.WriteLine($"[CloudPayments] {json}");

                    //set default
                    var result = new PayApiResponse<Post3dsRequestArgs>
                    {
                        Message = "3DS failed"
                    };

                    try
                    {
                        var model = new Post3dsRequestArgs
                        {
                            TransactionId = (string)jsonObj["MD"],
                            PaymentResponse = (string)jsonObj["PaRes"]
                        };

                        if (!string.IsNullOrEmpty(model.PaymentResponse))
                        {
                            result = new PayApiResponse<Post3dsRequestArgs>
                            {
                                Success = true,
                                Model = model
                            };
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                    Microsoft.Maui.Controls.Application.Current?.Dispatcher.Dispatch(() =>
                    {
                        Renderer.FormsControl.Request3DSCallback?.Invoke(result);
                    });

                });
            }
        }

        public override void DidStartProvisionalNavigation(WKWebView webView, WKNavigation navigation)
        {
            System.Diagnostics.Debug.WriteLine("[CloudPayments] OnPageStarted: " + webView.Url.AbsoluteString);

            bool invisibleSet = false;
            if (webView.Url.AbsoluteString.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                //result page, we can hide it..
                if (Renderer.FormsControl.HideResult)
                {
                    ((View3DS)Renderer.VirtualView).IsVisible = false;
                    invisibleSet = true;
                }
            }

            if (!invisibleSet)
                ((View3DS)Renderer.VirtualView).IsVisible = true;
        }


        public override void DidFailNavigation(WKWebView webView, WKNavigation navigation, NSError error)
        {
            //todo

        }
    }

    /*
    public class CustomWebVew : FormsWebViewClient
    {
        private WeakReference<View3DSHandler> _weakRenderer;

        public View3DSHandler Renderer
        {
            get
            {
                if (!_weakRenderer.TryGetTarget(out View3DSHandler value))
                {
                    return default;
                }

                return value;
            }
        }



        public CustomWebVew(View3DSHandler renderer) : base(renderer)
        {
            _weakRenderer = new WeakReference<View3DSHandler>(renderer);
        }

        public override void OnPageStarted(global::Android.Webkit.WebView view, string url, Bitmap favicon)
        {
            System.Diagnostics.Debug.WriteLine("[CloudPayments] OnPageStarted: " + url);

            bool invisibleSet=false;
            if (url.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                //result page, we can hide it..
                if (Renderer.FormsControl.HideResult)
                {
                    view.Visibility = ViewStates.Invisible;
                    invisibleSet = true;
                }
            }

            if (!invisibleSet)
                view.Visibility = ViewStates.Visible;

            base.OnPageStarted(view, url, favicon);
        }

        public override void OnPageFinished(global::Android.Webkit.WebView view, string url)
        {
            base.OnPageFinished(view, url);

            System.Diagnostics.Debug.WriteLine("[CloudPayments] onPageFinished: " + url);

            if (url.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                //3DS complete
                var js =
                    "(function() { return (document.getElementsByTagName('html')[0].innerHTML); })();";
                var html = new Callback3DSResultReceived(Renderer.FormsControl);
                view.EvaluateJavascript(js, html);
            }
        }


    }

    class Callback3DSResultReceived : Java.Lang.Object, IValueCallback
    {
        private WeakReference<View3DS> _weakParent;

        public View3DS Parent
        {
            get
            {
                if (!_weakParent.TryGetTarget(out View3DS value))
                {
                    return default;
                }
                return value;
            }
        }


        public Callback3DSResultReceived(View3DS rendererFormsControl)
        {
            _weakParent = new WeakReference<View3DS>(rendererFormsControl);
        }

        public void OnReceiveValue(Object value)
        {
            var html = System.Text.RegularExpressions.Regex.Unescape((string)value);
            string json = GetStrBetweenTags(html, "<body>", "</body>");

            var jsonObj = JObject.Parse(json);
            System.Diagnostics.Debug.WriteLine($"[CloudPayments] {json}");

            //set default
            var result = new PayApiResponse<Post3dsRequestArgs>
            {
                Message = "3DS failed"
            };

            try
            {
                var model = new Post3dsRequestArgs
                {
                    TransactionId = (string)jsonObj["MD"],
                    PaymentResponse = (string)jsonObj["PaRes"]
                };

                if (!string.IsNullOrEmpty(model.PaymentResponse))
                {
                    result = new PayApiResponse<Post3dsRequestArgs>
                    {
                        Success = true,
                        Model = model
                    };
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            Device.BeginInvokeOnMainThread(() =>
            {
                Parent.Request3DSCallback?.Invoke(result);
            });
        }

        public string GetStrBetweenTags(string value,
            string startTag,
            string endTag)
        {
            if (value.Contains(startTag) && value.Contains(endTag))
            {
                int index = value.IndexOf(startTag) + startTag.Length;
                return value.Substring(index, value.IndexOf(endTag) - index);
            }
            else
                return null;
        }
    }
        */

}