﻿<?xml version="1.0" encoding="UTF-8"?>
<Grid xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:converters="clr-namespace:AppoMobi.Xam.Converters;assembly=AppoMobi.Forms"
             xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg;assembly=AppoMobi.Forms"
             mc:Ignorable="d"

            x:Name="This"

             VerticalOptions="Start"
             HorizontalOptions="Start"
             HeightRequest="16"
             WidthRequest="16"

             x:Class="AppoMobi.Forms.Controls.SvgCheck">


    <svg:SvgIcon
        TintColor="{Binding Source={x:Reference This}, Path=SvgTintColor}"
        IsVisible="{Binding Source={x:Reference This}, 
        Path=Checked, Converter={x:StaticResource NotConverter}}"
        HeightRequest="{Binding Source={x:Reference This}, Path=Height}"
        WidthRequest="{Binding Source={x:Reference This}, Path=Width}"
        IconFilePath="{Binding Source={x:Reference This}, Path=SvgUnselected}"
        HorizontalOptions="Start"
        VerticalOptions="Start"/>

    <svg:SvgIcon
        TintColor="{Binding Source={x:Reference This}, Path=SvgTintColor}"
        IsVisible="{Binding Source={x:Reference This}, 
        Path=Checked}"
        HeightRequest="{Binding Source={x:Reference This}, Path=Height}"
        WidthRequest="{Binding Source={x:Reference This}, Path=Width}"
        IconFilePath="{Binding Source={x:Reference This}, Path=SvgSelected}"
        HorizontalOptions="Start"
        VerticalOptions="Start"/>


</Grid>