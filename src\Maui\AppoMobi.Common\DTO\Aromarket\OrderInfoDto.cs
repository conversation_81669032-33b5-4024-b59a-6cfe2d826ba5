﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class OrderInfoDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        
        [JsonProperty("currency")]
        public string CurrencyShort { get; set; }

        [JsonProperty("currency_short")]
        public string CurrencyShortest { get; set; }


        [JsonProperty("time")]
        [JsonConverter(typeof(SecondsEpochConverter))]
        public DateTime Time { get; set; }
        
        [JsonProperty("payment_method")]
        public string PaymentMethod { get; set; }


        [JsonProperty("payment_method_key")]
        public string PaymentMethodKey { get; set; }


        [JsonProperty("payment_methods")]
        public Dictionary<string, FormPaymentMethod> PaymentMethods { get; set; }


        [JsonProperty("pay_url")]
        public string PayUrl { get; set; }

        [JsonProperty("prepayment_amount")]
        public decimal PrepaymentAmount { get; set; }

        [JsonProperty("delivery")]
        public string Delivery { get; set; }

        [JsonProperty("delivery_amount")]
        public decimal DeliveryAmount { get; set; }

        [JsonProperty("delivery_title")]
        public string DeliveryTitle { get; set; }

        [JsonProperty("delivery_info")]
        public string DeliveryInfo { get; set; }

        [JsonProperty("fio")]
        public string Fio { get; set; }

        [JsonProperty("phone")]
        public string Phone { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("comment")]
        public string Comment { get; set; }

        [JsonProperty("promocode_id")]
        public string PromocodeId { get; set; }

        [JsonProperty("promocode_discount")]
        public double? PromocodeDiscount { get; set; }

        [JsonProperty("discount")]
        public double? Discount { get; set; }

        [JsonProperty("product_cost")]
        public decimal ProductCost { get; set; }

        [JsonProperty("total_cost")]
        public decimal TotalCost { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("time_pay")]
        [JsonConverter(typeof(SecondsNullableEpochConverter))]
        public DateTime? TimePayed { get; set; }

        //[JsonProperty("payment_data")]
        //public OrderPaymentData PaymentData { get; set; }


        
    }
}