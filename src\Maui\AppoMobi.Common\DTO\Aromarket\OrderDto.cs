﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class OrderDto
    {
        [JsonProperty("idstore")]
        public string StoreId { get; set; }

        [JsonProperty("idcity")]
        public string CityId { get; set; }

        [JsonProperty("sum")]
        public decimal? Sum { get; set; }

        [JsonProperty("present_sum")]
        public decimal? PresentSum { get; set; }

        [JsonProperty("discount_sum")]
        public decimal? DiscountSum { get; set; }

        [JsonProperty("spec_sum")]
        public decimal? SpecSum { get; set; }

        [JsonProperty("num")]
        public int Num { get; set; }

        [JsonProperty("promocode_discount")]
        public decimal? PromocodeDiscount { get; set; }

        [JsonProperty("promocode_val_sum")]
        public decimal? PromocodeValSum { get; set; }

        [JsonProperty("promocode_val_percent")]
        public double? PromocodeValPercent { get; set; }

        [JsonProperty("discount")]
        public double? Discount { get; set; }

        [JsonProperty("discount_type")]
        public string DiscountType { get; set; }

        [JsonProperty("delivery")]
        public string Delivery { get; set; }

        [JsonProperty("delivery_price")]
        public decimal? DeliveryPrice { get; set; }

        [JsonProperty("has_virtual")]
        public bool HasVirtual { get; set; }

        [JsonProperty("has_nal")]
        public bool HasNal { get; set; }

        [JsonProperty("sale_sum")]
        public decimal SaleSum { get; set; }

        [JsonProperty("form_pay")]
        public string FormPay { get; set; }
    }
}