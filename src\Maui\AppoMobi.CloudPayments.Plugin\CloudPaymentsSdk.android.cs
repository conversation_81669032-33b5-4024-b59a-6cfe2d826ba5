using System;
using System.Net.Http;
using System.Threading;
using AppoMobi.CloudPayments.Models;

namespace AppoMobi.CloudPayments.Plugin
{
    public partial class CloudPaymentsSdk
    {
        /// <summary>
        /// Initialize Android-specific components
        /// </summary>
        public static void InitAndroid()
        {
            // Android-specific initialization if needed
            // Handler registration is now done through MauiAppBuilder.ConfigureMauiHandlers
        }
    }


}
