﻿using System;
using System.Net.Http;
using System.Threading;
using AppoMobi.CloudPayments.Models;

namespace AppoMobi.CloudPayments.Plugin
{
    public partial class CloudPaymentsSdk
    {
        /// <summary>
        /// Must be called in platform project to register MAUI handlers
        /// </summary>
        public static void InitAndroid()
        {
            AppoMobi.CloudPayments.Platform.View3DSHandler.Register();
        }
    }


}
