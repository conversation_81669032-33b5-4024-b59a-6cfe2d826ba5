﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class LineItemDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("idbrand")]
        public string BrandId { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("name_desc")]
        public string NameDesc { get; set; }


        [JsonProperty("img")]
        public string Image { get; set; }

        [JsonProperty("sex")]
        public string Sex { get; set; }



        [JsonProperty("root_catalog_id")]
        public string RootCatalogId { get; set; }

        [JsonProperty("available")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Available { get; set; }

        [JsonProperty("min_price")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long MinPrice { get; set; }

        [JsonProperty("max_price")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long MaxPrice { get; set; }

        [JsonProperty("min_volume")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long MinVolume { get; set; }

        [JsonProperty("max_volume")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long MaxVolume { get; set; }

        [JsonProperty("vintage_mark")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long VintageMark { get; set; }

        [JsonProperty("brand_name")]
        public string BrandName { get; set; }
    }
}