﻿using System;
using System.Net.Http;
using System.Threading;
using AppoMobi.CloudPayments.Models;

namespace AppoMobi.CloudPayments.Plugin
{
    public partial class CloudPaymentsSdk
    {
        public static Func<HttpClient> CreateHttpClient = new Func<HttpClient>(() => new HttpClient());

        /// <summary>
        /// Awaitable API error handler
        /// </summary>
        public static Action<Exception> OnApiException;

        static CloudPaymentsSdk()
        {

        }

        /*
        private static CloudPaymentsSdk instance;
        public static CloudPaymentsSdk Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CloudPaymentsSdk();
                }

                if (instance.CreateHttpClient==null)
                {
                    throw new InvalidOperationException("You must set CreateHttpClient before using Client.");
                }

                return instance;
            }
        }
        */

        private static ApiHelper _api;
        public static ApiHelper Api
        {
            get
            {
                if (_api == null)
                {
                    _api = new ApiHelper(CreateHttpClient);
                }
                return _api;
            }
        }

        #region Features

        /// <summary>
        /// Gets responce after 3DS
        /// When authentication is done, payer will be returned to TermUrl with the MD and PaRes
        /// parameters passed in the POST method.
        /// </summary>
        public static string RedirectUrl { get; set; } = "https://demo.cloudpayments.ru/WebFormPost/GetWebViewData";


        /// <summary>
        /// Server public key in PEM format (important)
        /// </summary>
        public static string PublicKeyPEM { get; set; } = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArBZ1NNjvszen6BNWsgyD\nUJvDUZDtvR4jKNQtEwW1iW7hqJr0TdD8hgTxw3DfH+Hi/7ZjSNdH5EfChvgVW9wt\nTxrvUXCOyJndReq7qNMo94lHpoSIVW82dp4rcDB4kU+q+ekh5rj9Oj6EReCTuXr3\nfoLLBVpH0/z1vtgcCfQzsLlGkSTwgLqASTUsuzfI8viVUbxE1a+600hN0uBh/CYK\noMnCp/EhxV8g7eUmNsWjZyiUrV8AA/5DgZUCB+jqGQT/Dhc8e21tAkQ3qan/jQ5i\n/QYocA/4jW3WQAldMLj0PA36kINEbuDKq8qRh25v+k4qyjb7Xp4W2DywmNtG3Q20\nMQIDAQAB\n-----END PUBLIC KEY-----";

        public static string PublicKeyVersion = "04";

        /// <summary>
        /// Public ID
        /// </summary>
        public static string ClientId { get; set; }

        /// <summary>
        /// Secret
        /// </summary>
        public static string ApiSecret { get; set; }

        
        public static Transaction PendingTransaction { get; set; }

        #endregion


    }


}
