﻿using System;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class UserOrderDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("country_id")]
        public string CountryId { get; set; }

        [JsonProperty("currency")]
        public string CurrencyShort { get; set; }

        [JsonProperty("currency_short")]
        public string CurrencyShortest { get; set; }

        [JsonProperty("time")]
        [JsonConverter(typeof(SecondsEpochConverter))]
        public DateTime? Time { get; set; }

        [JsonProperty("cost")]
        [JsonConverter(typeof(ParseDecimalStringConverter))]
        public decimal Cost { get; set; }

        [JsonProperty("num")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Num { get; set; }

        [JsonProperty("status")]
        public ApiOrderStatus Status { get; set; }

        [JsonProperty("time_pay")]
        [JsonConverter(typeof(SecondsNullableEpochConverter))]
        public DateTime? TimePayed { get; set; }

    }
}