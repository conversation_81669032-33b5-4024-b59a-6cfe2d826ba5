﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class LineProductDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("weight")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Weight { get; set; }

        [JsonProperty("weight_time")]
        [JsonConverter(typeof(SecondsEpochConverter))]
        public DateTime WeightTime { get; set; }

        [JsonProperty("color1")]
        public string Color1 { get; set; }

        [JsonProperty("color2")]
        public string Color2 { get; set; }

        [JsonProperty("color_textures")]
        public string ColorTextures { get; set; }

        //[JsonProperty("color_name")]
        //public string ColorName { get; set; }

        [JsonConverter(typeof(BoolConverter))]
        [JsonProperty("vintage")]
        public bool Vintage { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("desc")]
        public string Desc { get; set; }

        [JsonProperty("volume")]
        public string Volume { get; set; }

        [JsonConverter(typeof(BoolConverter))]
        [JsonProperty("available")]
        public bool Available { get; set; }

        [JsonProperty("old_price")]
        public long OldPrice { get; set; }

        [JsonProperty("price")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Price { get; set; }

        [JsonProperty("price2")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Price2 { get; set; }

        [JsonProperty("price3")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Price3 { get; set; }


        [JsonConverter(typeof(RemoteImagesConverter))] //fix api exporting wrong json
        [JsonProperty("images")]
        public List<RemoteImage> Images { get; set; }

        [JsonProperty("in_favorite")]
        public int InFavorite { get; set; }

        /// <summary>
        /// Признак того что товар в заявках у указанного пользователя
        /// </summary>
        [JsonProperty("in_requests")]
        public int InRequests { get; set; }

        [JsonConverter(typeof(BoolConverter))]
        [JsonProperty("sale")]
        public bool IsSale { get; set; }

        //[JsonProperty("prices")]
        //public List<SpecialPrice> Prices { get; set; }

    }
}