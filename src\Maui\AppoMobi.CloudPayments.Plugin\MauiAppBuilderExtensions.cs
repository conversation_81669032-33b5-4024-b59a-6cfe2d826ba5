using Microsoft.Extensions.Logging;
using AppoMobi.CloudPayments.Plugin;
using AppoMobi.CloudPayments;

#if ANDROID
using AppoMobi.CloudPayments.Platform;
#elif IOS
using AppoMobi.CloudPayments.Platform;
#elif WINDOWS
using AppoMobi.CloudPayments.Platform;
#endif

namespace Microsoft.Extensions.DependencyInjection;

public static class MauiAppBuilderExtensions
{
    /// <summary>
    /// Configures CloudPayments SDK for MAUI application with platform-specific handlers
    /// </summary>
    /// <param name="builder">The MauiAppBuilder instance</param>
    /// <returns>The configured MauiAppBuilder</returns>
    public static MauiAppBuilder UseCloudPayments(this MauiAppBuilder builder)
    {
        // Register platform-specific handlers using proper MAUI approach
        builder.ConfigureMauiHandlers(handlers =>
        {
#if ANDROID
            handlers.AddHandler<View3DS, View3DSHandler>();
#elif IOS
            handlers.AddHandler<View3DS, View3DSHandler>();
#elif WINDOWS
            handlers.AddHandler<View3DS, View3DSHandler>();
#endif
        });

        // Initialize platform-specific SDK components
#if ANDROID
        CloudPaymentsSdk.InitAndroid();
#elif IOS
        CloudPaymentsSdk.InitiOS();
#elif WINDOWS
        CloudPaymentsSdk.InitWindows();
#endif

        builder.Services.AddSingleton<CloudPaymentsSdk>();

        return builder;
    }

    /// <summary>
    /// Configures CloudPayments SDK for MAUI application with configuration options
    /// </summary>
    /// <param name="builder">The MauiAppBuilder instance</param>
    /// <param name="configure">Configuration action for CloudPayments settings</param>
    /// <returns>The configured MauiAppBuilder</returns>
    public static MauiAppBuilder UseCloudPayments(this MauiAppBuilder builder, Action<CloudPaymentsOptions> configure)
    {
        var options = new CloudPaymentsOptions();
        configure(options);

        // Apply configuration
        if (!string.IsNullOrEmpty(options.ClientId))
            CloudPaymentsSdk.ClientId = options.ClientId;
            
        if (!string.IsNullOrEmpty(options.ApiSecret))
            CloudPaymentsSdk.ApiSecret = options.ApiSecret;
            
        if (!string.IsNullOrEmpty(options.PublicKeyPEM))
            CloudPaymentsSdk.PublicKeyPEM = options.PublicKeyPEM;
            
        if (!string.IsNullOrEmpty(options.PublicKeyVersion))
            CloudPaymentsSdk.PublicKeyVersion = options.PublicKeyVersion;
            
        if (!string.IsNullOrEmpty(options.RedirectUrl))
            CloudPaymentsSdk.RedirectUrl = options.RedirectUrl;

        if (options.OnApiException != null)
            CloudPaymentsSdk.OnApiException = options.OnApiException;

        if (options.CreateHttpClient != null)
            CloudPaymentsSdk.CreateHttpClient = options.CreateHttpClient;

        // Register platform-specific handlers using proper MAUI approach
        builder.ConfigureMauiHandlers(handlers =>
        {
#if ANDROID
            handlers.AddHandler<View3DS, View3DSHandler>();
#elif IOS
            handlers.AddHandler<View3DS, View3DSHandler>();
#elif WINDOWS
            handlers.AddHandler<View3DS, View3DSHandler>();
#endif
        });

        // Initialize platform-specific SDK components
#if ANDROID
        CloudPaymentsSdk.InitAndroid();
#elif IOS
        CloudPaymentsSdk.InitiOS();
#elif WINDOWS
        CloudPaymentsSdk.InitWindows();
#endif

        builder.Services.AddSingleton<CloudPaymentsSdk>();
        builder.Services.AddSingleton(options);
        
        return builder;
    }
}

/// <summary>
/// Configuration options for CloudPayments SDK
/// </summary>
public class CloudPaymentsOptions
{
    /// <summary>
    /// Public ID for CloudPayments
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// API Secret for CloudPayments
    /// </summary>
    public string? ApiSecret { get; set; }

    /// <summary>
    /// Server public key in PEM format
    /// </summary>
    public string? PublicKeyPEM { get; set; }

    /// <summary>
    /// Public key version
    /// </summary>
    public string? PublicKeyVersion { get; set; }

    /// <summary>
    /// Redirect URL for 3DS authentication
    /// </summary>
    public string? RedirectUrl { get; set; }

    /// <summary>
    /// API exception handler
    /// </summary>
    public Action<Exception>? OnApiException { get; set; }

    /// <summary>
    /// HttpClient factory function
    /// </summary>
    public Func<System.Net.Http.HttpClient>? CreateHttpClient { get; set; }
}