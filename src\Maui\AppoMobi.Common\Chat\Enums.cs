﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using AppoMobi.Common.Helpers;

namespace AppoMobi.Common.Chat
{
    //todo refactor code to use this

    [TypeConverter(typeof(LocalizedEnumConverter))]
    public enum MessageStatus
    {
            Unsent,
                Sent,
            Viewed
    }


    [TypeConverter(typeof(LocalizedEnumConverter))]
    public enum NotificationStatus
    {
        New,
        Viewed,
        Any
    }

    [TypeConverter(typeof(LocalizedEnumConverter))]
    public enum FriendshipStatus
    {
        None,
        Sent,
        Accepted,
        Rejected,
    }

    [TypeConverter(typeof(LocalizedEnumConverter))]
    public enum NotificationType
    {
        Default,

        //friends
        FriendRequest,
        FriendRequestAccepted,
        FriendRequestRejected,

        //chat
        NewMessage,

        //content
        NewContent,

        //HoReCa
        VacancyResponce,

        //payments
        PaymentOk,
        PaymentFail,

    }

    public enum VacancyResponceResultType
    {
        Unset,

        /// <summary>
        /// Вас выбрали
        /// </summary>
        VacancyApproved,

        /// <summary>
        /// Вас отклонили
        /// </summary>
        VacancyRejected

    }

}
