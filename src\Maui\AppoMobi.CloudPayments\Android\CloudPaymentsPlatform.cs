﻿using System;
using Android.App;
using Android.Content;
using Android.Runtime;
using AndroidX.AppCompat.App;
using AppoMobi.CloudPayments.Models;
using RU.Cloudpayments.Sdk.Cp_card;
using RU.Cloudpayments.Sdk.Three_ds;

namespace AppoMobi.CloudPayments.Android
{
    [Preserve(AllMembers = true)]
    public sealed class CloudPaymentsPlatform : ICloudPaymentsClient
    {
        private static WeakReference<Activity> _activity;

        public static void Init(Activity activity)
        {
            _activity =  new WeakReference<Activity>(activity); ;

            //OpenTokPublisherViewRenderer.Preserve();
            //OpenTokSubscriberViewRenderer.Preserve();
            CloudPaymentsSdk.Init(() => new CloudPaymentsPlatform());
        }

        Activity MainActivity
        {
            get
            {
                if (_activity.TryGetTarget(out var activity))
                    return activity;
                return null;
            }
        }

        public string CreateCryptogram(CardInfo cardInfo)
        {
            var platformCard = new CPCard(cardInfo.Number, cardInfo.ExpirationDate, cardInfo.CVV);

            //var ok = card.IsValidNumber;

            //ok = card.IsValidExpDate;

            ////Определение типа платежной системы
            //var type = card.Type;

            ////Получаем криптограмму
           var cryptogram = platformCard.CardCryptogram(CloudPaymentsSdk.ClientId);

           //card.Cryptogram = cryptogram;

            return cryptogram;
        }

        /// <summary>
        /// For internal use
        /// </summary>
        public static Action<PayApiResponse<Post3dsRequestArgs>> Request3DSCallback { get; protected set; }

        public void Request3DS(Transaction transaction, Action<PayApiResponse<Post3dsRequestArgs>> callback)
        {

            var fragment = ThreeDsDialogFragment.NewInstance(transaction.AcsUrl, transaction.TransactionId, 
                transaction.PayerAuthenticationRequest);

            Request3DSCallback = callback;

            var activity = (AppCompatActivity)MainActivity;

            //fragment.Show(activity.SupportFragmentManager, "3DS");


            CloudPaymentsSdk.PendingTransaction = transaction;

            var intent = new Intent(MainActivity, typeof(Activity3DS));

            MainActivity.StartActivityForResult(intent, 812);
        }


    }
}