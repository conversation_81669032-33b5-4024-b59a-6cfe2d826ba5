﻿using System.Text;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{

    public class RequestSaveUserDto : UserInfoExternalDto
    {
        [JsonProperty("token")]
        public string Token { get; set; }
        
        [JsonProperty("city_id")]
        public string CityAgain { get; set; }
    }


    /*
 city_id 	число 	Да 	ID города
user_uid 	строка 	Да 	Авторизационный токен пользователя
fio 	строка 	Да 	ФИО
phone 	строка 	Да 	Телефон
email 	строка 	Нет 	Email
text 	строка 	Да 	Текст сообщения
type 	строка 	Да 	Тип сообщения: message - сообщение из обратной связи, complain - жалоба
order_id 	число 	Нет 	Номер заказа
 */
}
