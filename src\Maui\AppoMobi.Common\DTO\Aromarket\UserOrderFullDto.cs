﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class UserOrderFullDto
    {
        [JsonProperty("order")]
        public OrderInfoDto Order { get; set; }

        [JsonProperty("items")]
        public List<OrderItemDto> Items { get; set; }

 

        //[JsonProperty("payment_data")]
        //public OrderPaymentData PaymentData { get; set; }
    }
}