using System;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections.Specialized;
using AppoMobi.CloudPayments;
using AppoMobi.CloudPayments.Models;
using AppoMobi.CloudPayments.Platform;
using AppoMobi.CloudPayments.Plugin;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Handlers;
using Microsoft.Web.WebView2.Core;
using Newtonsoft.Json.Linq;

namespace AppoMobi.CloudPayments.Platform
{
    public class View3DSHandler : WebViewHandler, IView3DS
    {
        /// <summary>
        /// Register the handler
        /// </summary>
        public static void Register()
        {
            Microsoft.Maui.Handlers.WebViewHandler.Mapper.AppendToMapping(nameof(View3DS), (handler, view) =>
            {
                if (view is View3DS view3ds)
                {
                    view3ds.Renderer = (IView3DS)handler;
                }
            });
        }

        protected override void ConnectHandler(Microsoft.UI.Xaml.Controls.WebView2 platformView)
        {
            base.ConnectHandler(platformView);

            if (VirtualView is View3DS formsControl)
            {
                FormsControl = formsControl;
                FormsControl.Renderer = this;

                if (FormsControl.BackgroundColor != Colors.Transparent)
                {
                    // Note: WebView2 background color is handled differently
                    // This would require CSS injection or DefaultBackgroundColor property
                }

                platformView.NavigationCompleted += OnNavigationCompleted;
                platformView.NavigationStarting += OnNavigationStarting;
            }
        }

        protected override void DisconnectHandler(Microsoft.UI.Xaml.Controls.WebView2 platformView)
        {
            if (platformView != null)
            {
                platformView.NavigationCompleted -= OnNavigationCompleted;
                platformView.NavigationStarting -= OnNavigationStarting;
            }

            FormsControl?.Dispose();
            base.DisconnectHandler(platformView);
        }

        public View3DS? FormsControl { get; set; }

        public void Post(string url, string body)
        {
            Console.WriteLine($"[CloudPayments] 3DS\nurl: {url}\nbody: {body}");

            // For Windows, we'll inject JavaScript to create a form and submit it
            // This is a workaround since WebView2 POST API is complex in MAUI
            // Parse the body to extract form data manually
            var paReq = "";
            var md = "";
            var termUrl = CloudPaymentsSdk.RedirectUrl;

            // Simple parsing of form data
            var pairs = body.Split('&');
            foreach (var pair in pairs)
            {
                var keyValue = pair.Split('=');
                if (keyValue.Length == 2)
                {
                    var key = Uri.UnescapeDataString(keyValue[0]);
                    var value = Uri.UnescapeDataString(keyValue[1]);

                    switch (key)
                    {
                        case "PaReq":
                            paReq = value;
                            break;
                        case "MD":
                            md = value;
                            break;
                        case "TermUrl":
                            termUrl = value;
                            break;
                    }
                }
            }

            var formHtml = $@"
                <html>
                <head><title>3DS Redirect</title></head>
                <body>
                    <form id='postForm' method='POST' action='{url}'>
                        <input type='hidden' name='PaReq' value='{paReq.Replace("'", "&#39;")}' />
                        <input type='hidden' name='TermUrl' value='{termUrl.Replace("'", "&#39;")}' />
                        <input type='hidden' name='MD' value='{md.Replace("'", "&#39;")}' />
                    </form>
                    <script>
                        document.getElementById('postForm').submit();
                    </script>
                </body>
                </html>";

            PlatformView.NavigateToString(formHtml);
        }

        private void OnNavigationStarting(object? sender, CoreWebView2NavigationStartingEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"[CloudPayments] NavigationStarting: {e.Uri}");

            if (e.Uri.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                // Result page, we can hide it if needed
                if (FormsControl?.HideResult == true)
                {
                    PlatformView.Visibility = Microsoft.UI.Xaml.Visibility.Collapsed;
                }
            }
            else
            {
                PlatformView.Visibility = Microsoft.UI.Xaml.Visibility.Visible;
            }
        }

        private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            var webView = sender as Microsoft.UI.Xaml.Controls.WebView2;
            if (webView == null) return;

            var currentUri = webView.Source?.ToString() ?? "";
            System.Diagnostics.Debug.WriteLine($"[CloudPayments] NavigationCompleted: {currentUri}");

            if (currentUri.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                // 3DS complete - extract result from page
                try
                {
                    var js = "(function() { return document.getElementsByTagName('html')[0].innerHTML; })();";
                    var result = await webView.CoreWebView2.ExecuteScriptAsync(js);
                    
                    // Remove quotes from JSON string result
                    var html = result.Trim('"').Replace("\\\"", "\"").Replace("\\n", "\n").Replace("\\r", "\r");
                    html = Regex.Unescape(html);
                    
                    string json = GetStrBetweenTags(html, "<body>", "</body>");

                    var jsonObj = JObject.Parse(json);
                    System.Diagnostics.Debug.WriteLine($"[CloudPayments] {json}");

                    // Set default response
                    var response = new PayApiResponse<Post3dsRequestArgs>
                    {
                        Message = "3DS failed"
                    };

                    try
                    {
                        var model = new Post3dsRequestArgs
                        {
                            TransactionId = (string?)jsonObj["MD"] ?? "",
                            PaymentResponse = (string?)jsonObj["PaRes"] ?? ""
                        };

                        if (!string.IsNullOrEmpty(model.PaymentResponse))
                        {
                            response = new PayApiResponse<Post3dsRequestArgs>
                            {
                                Success = true,
                                Model = model
                            };
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[CloudPayments] Parse error: {ex}");
                    }

                    Microsoft.Maui.Controls.Application.Current?.Dispatcher.Dispatch(() =>
                    {
                        FormsControl?.Request3DSCallback?.Invoke(response);
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[CloudPayments] Script execution error: {ex.Message}");
                }
            }
        }

        public string GetStrBetweenTags(string value, string startTag, string endTag)
        {
            if (value.Contains(startTag) && value.Contains(endTag))
            {
                int index = value.IndexOf(startTag) + startTag.Length;
                return value.Substring(index, value.IndexOf(endTag) - index);
            }
            return "";
        }
    }
}