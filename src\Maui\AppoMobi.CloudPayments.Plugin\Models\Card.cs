﻿using System;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using AppoMobi.CloudPayments.Plugin;
using Newtonsoft.Json;

namespace AppoMobi.CloudPayments.Models
{
    public class CardInfo 
    {
        public CardInfo()
        {

        }

        public CardInfo(string number, string expirationDate, string cvv)
        {
            Number = number;
            ExpirationDate = expirationDate;
            CVV = cvv;
        }

        public string CVV { get; set; }

        public string Number { get; set; }

        public string ExpirationDate { get; set; }


        public string CreateCryptogram()
        {
            return CreateCryptogram(this.Number, this.ExpirationDate, this.CVV, CloudPaymentsSdk.ClientId);
        }

        public string CreateCryptogram(string clientId)
        {
            return CreateCryptogram(this.Number, this.ExpirationDate, this.CVV, clientId);
        }

        public string CreateCryptogram(string cardNumberString, string expDateString, string CVVString, string merchantPublicIDString)
        {

            // ExpDate must be in YYMM format
            var cleanCard = RemoveNonDigits(cardNumberString);
            String shortNumber = cleanCard.Substring(0, 6) + cleanCard.Substring(cleanCard.Length - 4, 4);


            //todo clean it
            var cleanExp = RemoveNonDigits(expDateString);
            var cardExpirationDateString = cleanExp.Substring(2, 2) + cleanExp.Substring(0, 2);

            // create cryptogram
            var cleanCardNumber = RemoveNonDigits(cardNumberString);
            var decryptedCryptogram = $"{cleanCardNumber}@{cardExpirationDateString}@{CVVString}@{merchantPublicIDString}";

            Debug.WriteLine($"\n{CloudPaymentsSdk.PublicKeyPEM}\n");

            var engine = Encryption.ImportPublicKey(CloudPaymentsSdk.PublicKeyPEM);

#if DEBUG
            var pem = Encryption.ExportPublicKey(engine);
            Debug.WriteLine($"\n{pem}\n");
#endif

            var encyption = new Encryption();
            var cryptogramString= encyption.RsaEncryptCloudPaymentsWithPemPublicKey(decryptedCryptogram, CloudPaymentsSdk.PublicKeyPEM);

            if (!string.IsNullOrEmpty(cryptogramString))
            {
                cryptogramString = cryptogramString.Replace("\n", "");
                cryptogramString = cryptogramString.Replace("\r", "");
            }

            var packetString = "02";
            packetString += shortNumber;
            packetString += cardExpirationDateString;
            packetString += CloudPaymentsSdk.PublicKeyVersion;
            packetString += cryptogramString;

            return packetString;
        }

        int GetLegth(int startIndex, int endIndex)
        {
            return endIndex - startIndex;
        }

        public bool IsExpirationValid()
        {
            var expDate = ExpirationDate;

            if (expDate.Length != 4)
            {
                return false;
            }

            try
            {
                var date =
                    DateTime.ParseExact(expDate, "MMyy", CultureInfo.InvariantCulture);

                if (DateTime.UtcNow < date)
                {
                    return true;
                }

                return false;
            }
            catch (Exception e)
            {
                Console.WriteLine($"[CloudPayments] {e}");
                return false;
            }
        }

        public bool IsCardNumberValid()
        {
            var cleanCardNumber = RemoveNonDigits(Number);

            if (string.IsNullOrEmpty(cleanCardNumber))
                return false;

            bool res = false;
            int sum = 0;
            int i;

            if (cleanCardNumber.Length % 2 == 0)
            {
                for (i = 0; i < cleanCardNumber.Length; i += 2)
                {
                    int c = Int32.Parse(cleanCardNumber.Substring(i, GetLegth(i, i + 1)));
                    c *= 2;
                    if (c > 9)
                    {
                        c -= 9;
                    }
                    sum += c;
                    sum += Int32.Parse(cleanCardNumber.Substring(i + 1, GetLegth(i + 1, i + 2)));
                }

            }
            else
            {
                for (i = 1; i < cleanCardNumber.Length; i += 2)
                {
                    int c = Int32.Parse(cleanCardNumber.Substring(i, GetLegth(i, i + 1)));
                    c *= 2;
                    if (c > 9)
                    {
                        c -= 9;
                    }

                    sum += c;
                    sum += Int32.Parse(cleanCardNumber.Substring(i - 1, GetLegth(i - 1,i)));
                }

                //			adding last character
                sum += Int32.Parse(cleanCardNumber.Substring(i - 1, GetLegth(i - 1,i)));
            }
            //final check
            if (sum % 10 == 0)
            {
                res = true;
            }

            return res;
        }



        public string RemoveNonDigits(string aCreditCardNo)
        {

            var clean =   Regex.Replace(aCreditCardNo, "[^0-9]", "");

            return clean;
        }



 
    }
}
