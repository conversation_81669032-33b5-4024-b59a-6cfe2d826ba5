﻿using System;
using System.Text;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{

 

    public class RequestAdvancedSearchDto : WithTokenDto
    {


        /// <summary>
        /// ID раздела каталога
        /// </summary>
        [JsonProperty("catalog_id")]
        public string CatalogId { get; set; }

        /// <summary>
        /// ID бренда
        /// </summary>
        [JsonProperty("brand_id")]
        public string BrandId { get; set; }


        /// <summary>
        /// Минимальная цена
        /// </summary>
        [JsonProperty("price_min")]
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// Максимальная цена
        /// </summary>
        [JsonProperty("price_max")]
        public decimal? MaxPrice { get; set; }

        /// <summary>
        /// Минимальный объем, мл
        /// </summary>
        [JsonProperty("volume_min")]
        public double? MinVolume { get; set; }

        /// <summary>
        /// Максимальный объем, мл
        /// </summary>
        [JsonProperty("volume_max")]
        public double? MaxVolume { get; set; }


        /// <summary>
        /// Лидерство продаж мин, %
        /// </summary>
        [JsonProperty("leader_min")]
        public double? MinPopularity { get; set; }

        /// <summary>
        /// Лидерство продаж макс, %
        /// </summary>
        [JsonProperty("leader_max")]
        public double? MaxPopularity { get; set; }


        /// <summary>
        /// Признаки товаров: 1 - Тестеры, 2 - Наборы, 3 - Миники до 2 мл, 4 - Миники до 10 мл, 5 - Наличие
        /// </summary>
        [JsonProperty("show")]
        public int[] Options { get; set; }

        /// <summary>
        /// Тип вывода: product - товары, line - линии
        /// </summary>
        [JsonProperty("view")]
        public string Output { get; set; }  

        /// <summary>
        /// Поисковая строка (как в методе "get_search_lines")
        /// </summary>
        [JsonProperty("search")]
        public string SearchTerm { get; set; }

        /// <summary>
        /// Поисковая строка только для бренда
        /// </summary>
        [JsonProperty("search_brand")]
        public string SearchTermBrand { get; set; }

        [JsonProperty("page")]
        public int? PageFrom1 { get; set; }

        [JsonProperty("pagelimit")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Метод сортировки: (price[поумолчанию], name, rating)
        /// </summary>
        [JsonProperty("order")]
        public string Order { get; set; }

        /// <summary>
        /// Направление сортировки: (asc[поумолчанию], desc)
        /// </summary>
        [JsonProperty("order_direction")]
        public string OrderDirection { get; set; }

        /// <summary>
        /// Tags
        /// </summary>
        [JsonProperty("product_ids")]
        public string ProductsIds { get; set; }

        /// <summary>
        /// Tags
        /// </summary>
        [JsonProperty("line_ids")]
        public string LinesIds { get; set; }


    }
}
