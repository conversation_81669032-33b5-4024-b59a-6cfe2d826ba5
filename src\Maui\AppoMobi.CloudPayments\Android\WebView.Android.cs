﻿using System;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Views;
using Android.Webkit;
using AppoMobi.CloudPayments;
using AppoMobi.CloudPayments.Android;
using AppoMobi.CloudPayments.Models;
using Java.Interop;
using Newtonsoft.Json.Linq;
using Xamarin.Forms;
using Xamarin.Forms.Platform.Android;
using Object = Java.Lang.Object;
using WebView = Xamarin.Forms.WebView;


[assembly: ExportRenderer(typeof(View3DS), typeof(View3DSRenderer))]
namespace AppoMobi.CloudPayments.Android
{
    public class View3DSRenderer : WebViewRenderer, IView3DS
    {

        protected override void OnElementChanged(ElementChangedEventArgs<WebView> e)
        {

            base.OnElementChanged(e);

            if (e.OldElement != null)
            {
                //var OldControl = (View3DS)e.OldElement;
                //OldControl.RendererCommand -= OnRendererCommand;

                //Control.RemoveJavascriptInterface("jsBridge");
                //((WebView)Element).Dispose();

                return;
            }

            if (Element == null) 
                return;

            FormsControl = (View3DS)Element;

            if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.Lollipop)
            {
                CookieManager.Instance.SetAcceptCookie(true);
                CookieManager.Instance.SetAcceptThirdPartyCookies(Control, true);
            }

            Control.SetWebViewClient(new CustomWebVew(this));

            if (FormsControl.BackgroundColor.A>0)
                Control.SetBackgroundColor(FormsControl.BackgroundColor.ToAndroid());

            FormsControl.Renderer = this;

            Control.Settings.SetSupportZoom(true);
        }

        public View3DS FormsControl { get; set; }

        public void Post(string url)
        {
            var parts = url.Split('?');

            Control.PostUrl(parts[0], Encoding.ASCII.GetBytes(parts[1]));
        }


        protected override void Dispose(bool disposing)
        {
            FormsControl?.Dispose();

            base.Dispose(disposing);
        }

        
        public View3DSRenderer(Context context) : base(context)
        {
        }
    }



    public class CustomWebVew : FormsWebViewClient
    {
        private WeakReference<View3DSRenderer> _weakRenderer;

        public View3DSRenderer Renderer
        {
            get
            {
                if (!_weakRenderer.TryGetTarget(out View3DSRenderer value))
                {
                    return default;
                }

                return value;
            }
        }



        public CustomWebVew(View3DSRenderer renderer) : base(renderer)
        {
            _weakRenderer = new WeakReference<View3DSRenderer>(renderer);
        }

        public override void OnPageStarted(global::Android.Webkit.WebView view, string url, Bitmap favicon)
        {
            System.Diagnostics.Debug.WriteLine("[CloudPayments] OnPageStarted: " + url);

            bool invisibleSet=false;
            if (url.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                //result page, we can hide it..
                if (Renderer.FormsControl.HideResult)
                {
                    view.Visibility = ViewStates.Invisible;
                    invisibleSet = true;
                }
            }

            if (!invisibleSet)
                view.Visibility = ViewStates.Visible;

            base.OnPageStarted(view, url, favicon);
        }

        public override void OnPageFinished(global::Android.Webkit.WebView view, string url)
        {
            base.OnPageFinished(view, url);

            System.Diagnostics.Debug.WriteLine("[CloudPayments] onPageFinished: " + url);

            if (url.ToLower() == CloudPaymentsSdk.RedirectUrl.ToLower())
            {
                //3DS complete
                var js =
                    "(function() { return (document.getElementsByTagName('html')[0].innerHTML); })();";
                var html = new Callback3DSResultReceived(Renderer.FormsControl);
                view.EvaluateJavascript(js, html);
            }
        }


    }

    class Callback3DSResultReceived : Java.Lang.Object, IValueCallback
    {
        private WeakReference<View3DS> _weakParent;

        public View3DS Parent
        {
            get
            {
                if (!_weakParent.TryGetTarget(out View3DS value))
                {
                    return default;
                }
                return value;
            }
        }


        public Callback3DSResultReceived(View3DS rendererFormsControl)
        {
            _weakParent = new WeakReference<View3DS>(rendererFormsControl);
        }

        public void OnReceiveValue(Object value)
        {
            var html = System.Text.RegularExpressions.Regex.Unescape((string)value);
            string json = GetStrBetweenTags(html, "<body>", "</body>");

            var jsonObj = JObject.Parse(json);
            System.Diagnostics.Debug.WriteLine($"[CloudPayments] {json}");

            //set default
            var result = new PayApiResponse<Post3dsRequestArgs>
            {
                Message = "3DS failed"
            };

            try
            {
                var model = new Post3dsRequestArgs
                {
                    TransactionId = (string)jsonObj["MD"],
                    PaymentResponse = (string)jsonObj["PaRes"]
                };

                if (!string.IsNullOrEmpty(model.PaymentResponse))
                {
                    result = new PayApiResponse<Post3dsRequestArgs>
                    {
                        Success = true,
                        Model = model
                    };
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            Device.BeginInvokeOnMainThread(() =>
            {
                Parent.Request3DSCallback?.Invoke(result);
            });
        }

        public string GetStrBetweenTags(string value,
            string startTag,
            string endTag)
        {
            if (value.Contains(startTag) && value.Contains(endTag))
            {
                int index = value.IndexOf(startTag) + startTag.Length;
                return value.Substring(index, value.IndexOf(endTag) - index);
            }
            else
                return null;
        }
    }

}