﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class UserProductDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        
        [JsonProperty("idline")]
        public string Line { get; set; }

        [JsonProperty("idbrand")]
        public string Brand { get; set; }

        [JsonProperty("brand_name")]
        public string BrandName { get; set; }

        [JsonProperty("line_name")]
        public string LineName { get; set; }



        [JsonProperty("sex")]
        public string Sex { get; set; }


        [JsonProperty("volume")]
        //[JsonConverter(typeof(ParseDoubleStringConverter))]
        public double Volume { get; set; }

        [JsonProperty("img")]
        public string Image { get; set; }

        [JsonProperty("gram")]
        //[JsonConverter(typeof(ParseDoubleStringConverter))]
        public double Grams { get; set; }

        [JsonProperty("price")]
        //[JsonConverter(typeof(ParseDecimalStringConverter))]
        public decimal Price { get; set; }

        [JsonProperty("price2")]
        //[JsonConverter(typeof(ParseDecimalStringConverter))]
        public decimal Price2 { get; set; }

        [JsonProperty("price3")]
        //[JsonConverter(typeof(ParseDecimalStringConverter))]
        public decimal Price3 { get; set; }

        [JsonProperty("sprice")]
        //[JsonConverter(typeof(ParseDecimalStringConverter))]
        public decimal Sprice { get; set; }

        [JsonProperty("available")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long Available { get; set; }

        [JsonProperty("root_catalog_id")]
        public string CatalogId { get; set; }


    }
}