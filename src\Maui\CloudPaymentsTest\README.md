# CloudPayments MAUI Test Application

This is a test application for the CloudPayments .NET MAUI plugin, demonstrating the successful port from Xamarin.Forms to .NET MAUI.

## Features Tested

### ✅ **Core SDK Integration**
- CloudPayments SDK initialization via MAUI extension methods
- Configuration through dependency injection
- API client functionality

### ✅ **Platform Support**
- **Android** - Full support with WebView handlers
- **iOS** - Full support with WKWebView handlers
- **macOS Catalyst** - Full support
- **Windows** - ✅ **Full support** with WebView2 handlers

### ✅ **3DS Authentication**
- View3DS control for 3D Secure authentication
- Platform-specific WebView implementations
- Mock transaction testing

## Project Structure

```
CloudPaymentsTest/
├── MainPage.xaml          # UI with test buttons and 3DS view
├── MainPage.xaml.cs       # Test logic and event handlers
├── MauiProgram.cs         # MAUI app configuration with CloudPayments
└── CloudPaymentsTest.csproj # Project file with platform targets
```

## Usage

### 1. **API Testing**
Click "Test CloudPayments API" to verify the API client connectivity.

### 2. **3DS View Testing**
Click "Show 3DS View" to display the 3DS authentication control with a mock transaction.

## Configuration

The app is configured in `MauiProgram.cs`:

```csharp
builder.UseCloudPayments(options =>
{
    options.ClientId = "test_client_id";
    options.ApiSecret = "test_api_secret";
    options.OnApiException = ex => Debug.WriteLine($"CloudPayments Error: {ex.Message}");
});
```

## Platform Notes

### Android
- Uses WebView with custom handlers
- Supports 3DS authentication flow
- Cookie management enabled

### iOS/macOS
- Uses WKWebView with navigation delegates
- Full 3DS support
- Proper memory management

### Windows
- WebView2 implementation with form submission workaround
- Full 3DS authentication support
- Resource conflicts resolved with custom build configuration

## Build Status

- ✅ **Android**: Builds and runs successfully
- ✅ **iOS**: Builds and runs successfully
- ✅ **macOS**: Builds and runs successfully
- ✅ **Windows**: Builds and runs successfully

## Dependencies

- Microsoft.Maui.Controls 9.0.0
- AppoMobi.CloudPayments.Maui (local project reference)
- AppoMobi.CloudPayments 1.0.0
- Newtonsoft.Json 13.0.3
- Portable.BouncyCastle 1.9.0

## Testing

To test the application:

1. Build and run on your target platform
2. Use the "Test CloudPayments API" button to verify connectivity
3. Use the "Show 3DS View" button to test the WebView integration
4. Monitor the debug output for CloudPayments logs

The test demonstrates that the Xamarin.Forms to .NET MAUI port is successful and maintains full functionality across all supported platforms.
