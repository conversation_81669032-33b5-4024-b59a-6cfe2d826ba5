﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class OrderLineDto
    {
        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("price")]
        public decimal Price { get; set; }

        [JsonProperty("price_src")]
        public decimal PriceSrc { get; set; }

        [JsonProperty("price_ue")]
        public decimal PriceUe { get; set; }

        [JsonProperty("spec")]
        public long Spec { get; set; }

        [JsonProperty("idsupplier")]
        [J<PERSON><PERSON>onverter(typeof(ParseLongStringConverter))]
        public long Idsupplier { get; set; }

        [JsonProperty("nal")]
        public int Nal { get; set; }

        [JsonProperty("no_discount_virtual")]
        public decimal NoDiscountVirtual { get; set; }

        [JsonProperty("num")]
        public string Num { get; set; }

        [JsonProperty("price_spec")]
        public decimal PriceSpec { get; set; }

        [JsonProperty("price_discount")]
        public decimal PriceDiscount { get; set; }
    }
}