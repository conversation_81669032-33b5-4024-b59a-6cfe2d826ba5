﻿using System;
using Newtonsoft.Json;

namespace AppoMobi.CloudPayments.Models
{
    public class Transaction
    {

        public string TransactionId { get; set; }

        public decimal? Amount { get; set; }

        public string Currency { get; set; }

        public int? CurrencyCode { get; set; }

        public string InvoiceId { get; set; }

        public string AccountId { get; set; }

        public string Email { get; set; }

        public string Description { get; set; }

        public string JsonData { get; set; }

        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// все даты в указанной временной зоне
        /// </summary>
        public DateTime? CreatedDateIso { get; set; }

        public DateTime? AuthDate { get; set; }

        public DateTime? AuthDateIso { get; set; }

        public DateTime? ConfirmDate { get; set; }

        public DateTime? ConfirmDateIso { get; set; }

        /// <summary>
        /// дата возмещения
        /// </summary>
        public string PayoutDate { get; set; }

        public DateTime? PayoutDateIso { get; set; }

        /// <summary>
        /// сумма возмещения
        /// </summary>
        public decimal? PayoutAmount { get; set; }

        public bool TestMode { get; set; }

        public string IpAddress { get; set; }

        public string IpCountry { get; set; }

        public string IpCity { get; set; }

        public string IpRegion { get; set; }
        public string IpDistrict { get; set; }
        public double? IpLatitude { get; set; }

        public double? IpLongitude { get; set; }

        public string CardFirstSix { get; set; }

        public string CardLastFour { get; set; }

        public string CardExpDate { get; set; }

        public string CardType { get; set; }

        public int? CardTypeCode { get; set; }

        public string Issuer { get; set; }

        public string IssuerBankCountry { get; set; }

        public TransactionStatus Status { get; set; }

        public int StatusCode { get; set; }

        public string Reason { get; set; }

        public int? ReasonCode { get; set; }

        /// <summary>
        /// сообщение для покупателя
        /// </summary>
        public string CardHolderMessage { get; set; }

        public string Token { get; set; }

        public string Name { get; set; }

        // 3DS Begin

        /// <summary>
        /// PaReq
        /// </summary>
        [JsonProperty("PaReq")]
        public string PayerAuthenticationRequest { get; set; }


        /// <summary>
        /// Access Control Server URL
        /// </summary>
        [JsonProperty("AcsUrl")]
        public string AcsUrl { get; set; }

        // 3DS End

       
    }

    public class SavedCard
    {

        public DateTime? CreatedDate { get; set; }

        public bool TestMode { get; set; }

        public string CardFirstSix { get; set; }

        public string CardLastFour { get; set; }

        public string CardExpDate { get; set; }

        public string CardType { get; set; }

        public int? CardTypeCode { get; set; }

        public string Issuer { get; set; }

        public string IssuerBankCountry { get; set; }

        public string Token { get; set; }

        public string Name { get; set; }


    }
}