﻿using Xamarin.Forms;

namespace AppoMobi.Forms.Controls.Navigation
{
    public class BaseNavBar : Grid
    {

 

        //-------------------------------------------------------------
        // ShowLogo
        //-------------------------------------------------------------
        private const string nameShowLogo = "ShowLogo";
        public static readonly BindableProperty ShowLogoProperty = BindableProperty.Create(nameShowLogo, typeof(bool), typeof(BaseNavBar), false); //, BindingMode.TwoWay
        public bool ShowLogo
        {
            get { return (bool)GetValue(ShowLogoProperty); }
            set { SetValue(ShowLogoProperty, value); }
        }

        //-------------------------------------------------------------
        // PageTitle
        //-------------------------------------------------------------
        private const string namePageTitle = "PageTitle";
        public static readonly BindableProperty PageTitleProperty = BindableProperty.Create(namePageTitle, typeof(string), typeof(BaseNavBar), ""); //, BindingMode.TwoWay
        public string PageTitle
        {
            get { return (string)GetValue(PageTitleProperty); }
            set
            {
                SetValue(PageTitleProperty, value);
            }
        }



    }
}