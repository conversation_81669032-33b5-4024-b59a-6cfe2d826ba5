﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{BABC5CE5-6317-4C5B-90E2-491965167D7D}</ProjectGuid>
    <ProjectTypeGuids>{10368E6C-D01B-4462-8E8B-01FC667A7035};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{77efb91c-a7e9-4b0e-a7c5-31eeec3c6d46}</TemplateGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AppoMobi.CloudPayments.Android</RootNamespace>
    <AssemblyName>AppoMobi.CloudPayments.Android</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <Deterministic>True</Deterministic>
    <AndroidUseLatestPlatformSdk>false</AndroidUseLatestPlatformSdk>
    <TargetFrameworkVersion>v10.0</TargetFrameworkVersion>
    <AndroidClassParser>class-parse</AndroidClassParser>
    <AndroidCodegenTarget>XAJavaInterop1</AndroidCodegenTarget>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>portable</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Mono.Android" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Activity3DS.cs" />
    <Compile Include="CloudPaymentsPlatform.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="WebView.Android.cs" />
  </ItemGroup>
  <ItemGroup>
    <LibraryProjectZip Include="..\..\SDK-Android-master\sdk\build\outputs\aar\sdk-release.aar">
      <Link>Jars\sdk-release.aar</Link>
    </LibraryProjectZip>
    <None Include="Jars\AboutJars.txt" />
    <None Include="Additions\AboutAdditions.txt" />
  </ItemGroup>
  <ItemGroup>
    <TransformFile Include="Transforms\Metadata.xml" />
    <TransformFile Include="Transforms\EnumFields.xml" />
    <TransformFile Include="Transforms\EnumMethods.xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Portable.BouncyCastle">
      <Version>*******</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.AndroidX.Browser">
      <Version>*******</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.AndroidX.Fragment">
      <Version>*******</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.AndroidX.Legacy.Support.V4">
      <Version>*******</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.AndroidX.Lifecycle.LiveData">
      <Version>*******</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Google.Android.Material">
      <Version>1.0.0.1</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedReferenceJar Include="Jars\jsoup-1.11.3.jar" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedReferenceJar Include="Jars\gson-2.8.5.jar" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedReferenceJar Include="Jars\gson-2.8.5-javadoc.jar" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Shared\AppoMobi.CloudPayments.csproj">
      <Project>{26ca1115-71bb-43d3-821e-65ab4b86f9f8}</Project>
      <Name>AppoMobi.CloudPayments</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.Bindings.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>