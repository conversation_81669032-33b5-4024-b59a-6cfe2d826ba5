﻿<?xml version="1.0" encoding="UTF-8"?>
<Grid     
            xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:touch="clr-namespace:AppoMobi.Forms.Xam.Touch;assembly=AppoMobi.Forms"
            xmlns:converters="clr-namespace:AppoMobi.Xam.Converters;assembly=AppoMobi.Forms"
            xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg;assembly=AppoMobi.Forms"
            mc:Ignorable="d"

            IsVisible="{Binding HideNavigation, Converter={x:StaticResource NotConverter}}"

            VerticalOptions="Start"
            HorizontalOptions="FillAndExpand"

BackgroundColor="{StaticResource ColorPrimary}"

            x:Class="AppoMobi.Forms.Controls.Navigation.LoaderNavBar">

<Grid.RowDefinitions>
    <RowDefinition Height="Auto"/>
</Grid.RowDefinitions>


    <StackLayout         
                         Spacing="0"
                          HorizontalOptions="FillAndExpand" 
                          VerticalOptions="Start">

    <!--STATUS BAR-->
    <ContentView
        VerticalOptions="Start"
                    HeightRequest="{Binding StatusBarHeightRequest}"
                    StyleId="StatusBar"/>

    <!--NAVIGATION BAR-->
        <Grid   StyleId="NavBar" 
                    x:Name="cNavBar"
                    d:MinimumHeightRequest="49"
                    HeightRequest="{Binding NavBarHeightRequest}">

            <!--MENU-->
            <!--<svg:SvgIcon
                IsVisible="{Binding NoMenu, Converter={x:StaticResource NotConverter}}"
                Margin="13,0,0,0"
                VerticalOptions="Center"
                HeightRequest="20"
                WidthRequest="20"
                TintColor="{StaticResource ColorAccentDark}"
                IconFilePath="aro.hamburger.svg"/>-->

        <!--GO BACK-->
            <svg:SvgIcon
                IsVisible="{Binding NoMenu, Converter={x:StaticResource NotConverter}}"
                Margin="13,0,0,0"
                VerticalOptions="Center"
                HeightRequest="20"
                WidthRequest="20"
                TintColor="{StaticResource ColorAccentDark}"
                IconFilePath="aro.back.svg"/>


            <!--  GO BACK HOTSPOT  -->
 
        <touch:ContentView  
                        x:Name="hsLeftIcon1"
                        TappedCommand="{Binding CommandNavbarGoBack}"
                        Margin="10,0,0,0"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        HeightRequest="26"
                        WidthRequest="26"/>


 

            <!--TITLE-->
            <Label 
                Margin="100,0"
                LineBreakMode="WordWrap"
                HorizontalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                VerticalOptions="Center"
                Text="Загрузка.."
                FontFamily="{StaticResource FontText-UI}"
                TextColor="{StaticResource ColorPaper}"
                FontSize="18"/>


             

        </Grid>
 

    </StackLayout>

    </Grid>


