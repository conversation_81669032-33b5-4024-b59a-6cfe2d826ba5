﻿using System;
using System.Net.Http;
using System.Threading;
using AppoMobi.CloudPayments.Models;

namespace AppoMobi.CloudPayments
{
    public static class CloudPaymentsSdk
    {
        public static Func<HttpClient> CreateHttpClient = new Func<HttpClient>(() => new HttpClient());

        /// <summary>
        /// Awaitable API error handler
        /// </summary>
        public static Action<Exception> OnApiException;

        private static bool _isInitialized;
        private static Lazy<ICloudPaymentsClient> _implementation;

        static CloudPaymentsSdk()
        {

        }

        public static void Init(Func<ICloudPaymentsClient> creator)
        {
            if (_isInitialized)
            {
                return;
            }
            _isInitialized = true;
            _implementation = new Lazy<ICloudPaymentsClient>(creator, LazyThreadSafetyMode.PublicationOnly);

        }


        public static ICloudPaymentsClient Current
        {
            get
            {
                if (CreateHttpClient == null)
                {
                    throw new InvalidOperationException("You must set CreateHttpClient before using Client.");
                }

                var value = _implementation?.Value;
                if (value == null)
                {
                    throw new InvalidOperationException("You must call CloudPaymentsSdk.Init() in platform specific code before using it.");
                }
                return value;
            }
        }

        private static ApiHelper _api;

        public static ApiHelper Api
        {
            get
            {
                if (_api == null)
                {
                    _api = new ApiHelper(CreateHttpClient);
                }
                return _api;
            }
        }

        #region Features

        /// <summary>
        /// Gets responce after 3DS
        /// When authentication is done, payer will be returned to TermUrl with the MD and PaRes
        /// parameters passed in the POST method.
        /// </summary>
        public static string RedirectUrl { get; set; } = "https://demo.cloudpayments.ru/WebFormPost/GetWebViewData";

        /// <summary>
        /// Public ID
        /// </summary>
        public static string ClientId { get; set; }

        /// <summary>
        /// Secret
        /// </summary>
        public static string ApiSecret { get; set; }

        
        public static Transaction PendingTransaction { get; set; }


        #endregion

    }

    /// <summary>
    /// Platform features
    /// </summary>
    public interface ICloudPaymentsClient
    {


        string CreateCryptogram(CardInfo cardInfo);

        void Request3DS(Transaction transaction, Action<PayApiResponse<Post3dsRequestArgs>> callback);

    }
}
