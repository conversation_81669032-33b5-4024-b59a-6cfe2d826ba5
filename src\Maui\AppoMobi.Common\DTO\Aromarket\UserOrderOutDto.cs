﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class UserOrderOutDto : WithTokenDto
    {
        //        city_id число   Да ID города
 
        //   user_uid    строка Да  Авторизационный токен пользователя

        //  name_f  строка Да  Фамилия
        [JsonProperty("name_f")]
        public string LastName { get; set; }

        // name_i  строка Да  Имя
        [JsonProperty("name_i")]
        public string FirstName { get; set; }

        //name_o  строка Нет     Отчество
        [JsonProperty("name_o")]
        public string MiddleName { get; set; }

        [JsonProperty("post_name_f")]
        public string PostLastName { get; set; }

        // name_i  строка Да  Имя
        [JsonProperty("post_name_i")]
        public string PostFirstName { get; set; }

        //name_o  строка Нет     Отчество
        [JsonProperty("post_name_o")]
        public string PostMiddleName { get; set; }

        //phone   строка Да  Телефон
        [JsonProperty("phone")]
        public string PhoneNumber { get; set; }

        //delivery_sms    число Нет     Уведомлять по СМС о состояние заказа: 1 или 0
        [JsonProperty("delivery_sms")]
        public int DeliverySms { get; set; }

        //delivery строка  Да ID метода доставки
        [JsonProperty("delivery")]
        public string DeliveryMethod { get; set; }

        //confirmed число   Нет Не перезванивать менеджеру для уточнения, данные заказа указаны верно: 1 или 0, по умолчанию - 0
        [JsonProperty("confirmed")]
        public int Confirmed { get; set; }

        //comment строка  Нет Комментарий к заказу
        [JsonProperty("comment")]
        public string Comments { get; set; }

        //form_pay строка  Да ID формы оплаты
        [JsonProperty("form_pay")]
        public string PaymentMethod { get; set; }

        //promocode_id строка  Нет Номер промокода
        [JsonProperty("promocode_id")]
        public string PromoCode { get; set; }

        //email   строка Нет     Email
        [JsonProperty("email")]
        public string Email { get; set; }

        //items   массив Да  Массив товаров, ключи: product_id - ID товара, quantity - кол-во
        [JsonProperty("items")]
        public List<CartProductDto> Items { get; set; }


        //Поля для метода доставки "delivery" = post

        //post_name_f     строка Да  Фамилия получателя

        //post_name_i строка  Да Имя получателя

        //post_name_o     строка Нет     Отчество получателя

        //post_index строка  Да Адрес доставки: индекс
        [JsonProperty("post_index")]
        public string PostalCode { get; set; }

        //post_city   строка Да  Адрес доставки: город
        [JsonProperty("post_city")]
        public string CityCode { get; set; }

        //street  строка Да  Адрес доставки: улица
        [JsonProperty("street")]
        public string Street { get; set; }

        //house   строка Да  Адрес доставки: дом
        [JsonProperty("house")]
        public string House { get; set; }

        //building    строка Нет     Адрес доставки: здание/корпус
        [JsonProperty("building")]
        public string Building { get; set; }

        //office  строка Нет     Адрес доставки: офис/квартира
        [JsonProperty("office")]
        public string Office { get; set; }


        //Поля для метода доставки "delivery" = courier_boxberry

        //post_index  строка Да  Адрес доставки: индекс


        //street  строка Да  Адрес доставки: улица


        //house   строка Да  Адрес доставки: дом


        //building    строка Нет     Адрес доставки: здание/корпус


        //office  строка Нет     Адрес доставки: офис/квартира




        //Поля для метода доставки "delivery" = courier

        //street  строка Да  Адрес доставки: улица
        //house   строка Да  Адрес доставки: дом
        //building    строка Нет     Адрес доставки: здание/корпус
        //office  строка Нет     Адрес доставки: офис/квартира

        
        //Поля для метода доставки "delivery" = courier_express

        //street  строка Да  Адрес доставки: улица
        //house   строка Да  Адрес доставки: дом
        //building    строка Нет     Адрес доставки: здание/корпус
        //office  строка Нет     Адрес доставки: офис/квартира



        //Поля для метода доставки "delivery" = courier_axiomus

        //street  строка Да  Адрес доставки: улица
        //house   строка Да  Адрес доставки: дом
        //building    строка Нет     Адрес доставки: здание/корпус
        //office  строка Нет     Адрес доставки: офис/квартира


        //Поля для метода доставки "delivery" = carry_boxberry

        //carry_boxberry  строка Да  ID пункта самовывоза Boxberry
        [JsonProperty("carry_boxberry")]
        public string Boxberry { get; set; }


        //Поля для метода доставки "delivery" = carry_boxberry_alt

        //carry_boxberry_alt
        [JsonProperty("carry_boxberry_alt")]
        public string BoxberryAlt { get; set; }

    }
}