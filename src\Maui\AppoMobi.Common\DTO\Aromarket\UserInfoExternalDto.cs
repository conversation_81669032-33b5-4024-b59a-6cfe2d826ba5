﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class UserInfoExternalDto : WithErrorDto
    {
        [JsonProperty("sendnews")]
        public int Subscribe { get; set; }

        [JsonProperty("user_uid")]
        public string UserToken { get; set; }

        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name_f")]
        public string LastName { get; set; }

        [JsonProperty("name_i")]
        public string FirstName { get; set; }

        [JsonProperty("name_o")]
        public string MiddleName { get; set; }

        [JsonProperty("phone")]
        public string Phone { get; set; }

        [JsonProperty("phone_code")]
        public string PhoneCode { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        /// <summary>
        /// Адрес доставки по умолчанию: город
        /// </summary>
        [JsonProperty("city")]
        public string City { get; set; }

        /// <summary>
        ///  	ID города по умолчанию
        /// </summary>
        [JsonProperty("idcity")]
        public string ShopCity { get; set; }

        [JsonProperty("index")]
        public string Index { get; set; }

        [JsonProperty("street")]
        public string Street { get; set; }

        [JsonProperty("house")]
        public string House { get; set; }

        [JsonProperty("building")]
        public string Building { get; set; }

        [JsonProperty("app")]
        public string App { get; set; }

        [JsonProperty("office")]
        public string Office { get; set; }

        [JsonProperty("discount")]
        public double? Discount { get; set; }

        [JsonProperty("count_order_complete")]
        public int? CountOrderComplete { get; set; }

        [JsonProperty("count_order")]
        public int? CountOrders { get; set; }

        [JsonProperty("count_reservation")]
        public int? CountReservation { get; set; }

        [JsonProperty("count_favorite")]
        public int? CountFavorite { get; set; }

        [JsonProperty("favorite_ids")]
        public string Favorites { get; set; }

        [JsonProperty("reservation_ids")]
        public string Reservations { get; set; }
            

        //[JsonProperty("cart")]
        //public List<CartDto> Cart { get; set; }

    }
}