﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public class WithTokenDto : IWithTokenDto
    {
        [JsonProperty("city_id")]
        public string CityId { get; set; }

        [JsonProperty("token")]
        public string Token{ get; set; }

        [JsonProperty("user_uid")]
        public string UserToken { get; set; }

        [JsonProperty("user_id")]
        public string UserId { get; set; }

        [JsonProperty("gen")]
        public int Generation { get; set; } = 1;

        [JsonProperty("country_id")]
        public string Country { get; set; }
    }
}