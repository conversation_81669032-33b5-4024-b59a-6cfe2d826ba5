﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class OrderItemDto
    {
        [JsonProperty("brand_name")]
        public string BrandName { get; set; }

        [JsonProperty("line_name")]
        public string LineName { get; set; }

        [JsonProperty("vintage")]
        public long Vintage { get; set; }

        [JsonProperty("sex")]
        public string Sex { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("volume")]
        public string Volume { get; set; }

        [JsonProperty("text")]
        public string Text { get; set; }

        [JsonProperty("quantity")]
        public int Quantity { get; set; }

        [JsonProperty("special")]
        public decimal Special { get; set; }

        [JsonProperty("price")]
        public decimal Price { get; set; }

        [JsonProperty("cost")]
        public decimal Cost { get; set; }

        [JsonProperty("cost_with_discount")]
        public decimal CostWithDiscount { get; set; }

        [JsonProperty("price_currency")]
        public string PriceCurrency { get; set; }
    }
}