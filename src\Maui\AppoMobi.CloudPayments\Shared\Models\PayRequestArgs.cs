﻿using Newtonsoft.Json;

namespace AppoMobi.CloudPayments.Models
{

    /*
    
    Для оплаты по криптограмме ИЛИ с токеном
    
    во втором случае не надо заполнять имя и криптограмму

     */

    public class PayRequestArgs
    {

        /// <summary>
        /// Криптограмма платежных данных (Обязательный если не указан токен)
        /// </summary>
        public string CardCryptogramPacket { get; set; }

        /// <summary>
        /// Системный айди привязанной карты, обязательный параметр если не задана криптограмма
        /// </summary>
        public string Token { get; set; }


        /// <summary>
        /// Обязательный - Сумма платежа, апи не принимает сумму транзакции меньше 0.01
        /// </summary>
        [JsonRequired]
        public string Amount { get; set; }

        /// <summary>
        /// Валюта: RUB/USD/EUR/GBP (см. справочник). Если параметр не передан, то по умолчанию принимает значение RUB.
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// IP адрес плательщика (Обязательный)
        /// </summary>
        [JsonRequired]
        public string IpAddress { get; set; }

        /// <summary>
        ///Имя держателя карты в латинице(Обязательный для всех платежей кроме оплаты по токену, или Apple Pay и Google Pay) Имя держателя карты в латинице
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Номер счета или заказа в вашей системе (необязательный)
        /// </summary>
        public string InvoiceId { get; set; } 

        /// <summary>
        /// Описание оплаты в свободной форме (необязательный)
        /// </summary>
        public string Description { get; set; } 

        /// <summary>
        /// Идентификатор пользователя в вашей системе (необязательный)
        /// </summary>
        public string AccountId { get; set; }

        /// <summary>
        /// E-mail плательщика на который будет отправлена квитанция об оплате (Необязательный)
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Любые другие данные, которые будут связаны с транзакцией, в том числе инструкции для создания подписки или формирования онлайн-чека. Мы зарезервировали названия следующих параметров и отображаем их содержимое в реестре операций, выгружаемом в Личном Кабинете: name, firstName, middleName, lastName, nick, phone, address, comment, birthDate. (необязательный)
        /// </summary>
        public string JsonData { get; set; } 

    }
}