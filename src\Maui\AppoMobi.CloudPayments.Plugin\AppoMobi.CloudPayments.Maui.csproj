﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net9.0;net9.0-android;net9.0-ios;net9.0-windows10.0.19041.0</TargetFrameworks>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <AssemblyName>AppoMobi.CloudPayments.Maui</AssemblyName>
    <RootNamespace>AppoMobi.CloudPayments.Plugin</RootNamespace>
    <PackageId>AppoMobi.CloudPayments.Maui</PackageId>
    <Description>CloudPayments SDK for .NET MAUI</Description>

    <NeutralLanguage>en</NeutralLanguage>
    <LangVersion>latest</LangVersion>
    <DebugType>portable</DebugType>
    <Version>1.11.001</Version>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)'=='Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)'=='Release' ">
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <!-- Platform-specific file exclusions -->
  <ItemGroup>
    <Compile Remove="**\*.ios.cs" Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) != 'ios'" />
    <Compile Remove="**\*.android.cs" Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) != 'android'" />
    <Compile Remove="**\*.windows.cs" Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) != 'windows'" />
  </ItemGroup>

  <!-- Android Resources -->
  <ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">
    <AndroidResource Include="Resources\**\*.xml" SubType="Designer" Generator="MSBuild:UpdateAndroidResources" />
    <AndroidResource Include="Resources\**\*.json" Generator="MSBuild:UpdateAndroidResources" />
  </ItemGroup>

  <!-- Package References -->
  <ItemGroup>
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
    <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.70" />
    <PackageReference Include="AppoMobi.CloudPayments" Version="1.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
  </ItemGroup>

</Project>