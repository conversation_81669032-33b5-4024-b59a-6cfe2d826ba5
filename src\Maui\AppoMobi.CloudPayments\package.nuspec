<?xml version="1.0" encoding="utf-8"?>

<!--nuget pack F:\Dev\Cases\CloudPayments\Src\Sdk\Plugin\package.nuspec-->

<package >
	<metadata>
    <id>AppoMobi.CloudPayments</id>
    <version>1.0.8</version>
    <authors>AppoMobi</authors>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <!-- <projectUrl>http://project_url_here_or_delete_this_line/</projectUrl> -->
    <!-- <iconUrl>http://icon_url_here_or_delete_this_line/</iconUrl> -->
    <description>CloudPayments SDK implementation for Xamarin.Forms</description>
    <releaseNotes>Initial release</releaseNotes>
    <copyright>$copyright$</copyright>
    <tags>xamarin credit card applepay googlepay</tags>
    <dependencies>

	    <group targetFramework="MonoAndroid10">
		    <dependency id="Xamarin.AndroidX.Browser" version="*******" />
		    <dependency id="Xamarin.AndroidX.Fragment" version="*******" />
		    <dependency id="Xamarin.AndroidX.Legacy.Support.V4" version="*******" />
		    <dependency id="Xamarin.AndroidX.Lifecycle.LiveData" version="*******" />
		    <dependency id="Xamarin.Google.Android.Material" version="*******" />
	    </group>

	    <group targetFramework="Xamarin.iOS10">
		    <!--Xamarin.iOS10 dependencies go here-->
	    </group>
 

		<group targetFramework=".NETStandard2.0">
			<dependency id="Newtonsoft.Json" version="12.0.3" />
		</group>

    </dependencies>
  </metadata>
  
  <!-- Insert below <metadata> element -->
<files>
    <!-- Cross-platform reference assemblies -->
    <file src="Shared\bin\Release\netstandard2.0\AppoMobi.CloudPayments.dll" target="lib\netstandard2.0\AppoMobi.CloudPayments.dll" />    

    <!-- iOS reference assemblies -->
    <!--<file src="Plugin.LoggingLibrary.iOS\bin\Release\Plugin.LoggingLibrary.dll" target="lib\Xamarin.iOS10\Plugin.LoggingLibrary.dll" />
    <file src="Plugin.LoggingLibrary.iOS\bin\Release\Plugin.LoggingLibrary.xml" target="lib\Xamarin.iOS10\Plugin.LoggingLibrary.xml" />-->

    <!-- Android reference assemblies -->
    <file src="Android\bin\Release\AppoMobi.CloudPayments.dll" target="lib\MonoAndroid10\AppoMobi.CloudPayments.dll" />
	<file src="Android\bin\Release\AppoMobi.CloudPayments.Android.dll" target="lib\MonoAndroid10\AppoMobi.CloudPayments.Android.dll" />

	<!--<file src="Plugin.LoggingLibrary.Android\bin\Release\Plugin.LoggingLibrary.dll" target="lib\MonoAndroid10\Plugin.LoggingLibrary.dll" />
    <file src="Plugin.LoggingLibrary.Android\bin\Release\Plugin.LoggingLibrary.xml" target="lib\MonoAndroid10\Plugin.LoggingLibrary.xml" />-->

</files>
  
</package>