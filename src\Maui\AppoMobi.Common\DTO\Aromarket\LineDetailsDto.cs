﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public partial class LineDetailsDto
    {
        [JsonProperty("line")]
        public LineInfoDto Info { get; set; }

        [JsonConverter(typeof(ListCanBeNullConverter<LineProductDto>))] //fix api exporting wrong json
        [JsonProperty("products")]
        public List<LineProductDto> Products { get; set; }

        [JsonProperty("review_count")]
        [JsonConverter(typeof(ParseLongStringConverter))]
        public long ReviewCount { get; set; }

        [JsonConverter(typeof(ListCanBeNullConverter<ReviewItem>))] //fix api exporting wrong json
        [JsonProperty("review_items")]
        public List<ReviewItem> ReviewItems { get; set; }
    }
}