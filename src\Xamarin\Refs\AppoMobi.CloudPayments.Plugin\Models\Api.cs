﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using AppoMobi.CloudPayments.Plugin;
using Newtonsoft.Json;

namespace AppoMobi.CloudPayments.Models
{
    public class ApiHelper
    {
     

        public async Task<bool> TestAsync()
        {
            var url = "https://api.cloudpayments.ru/test";

            try
            {
                var ret = await Request<PayApiResponse<object>>(HttpMethod.Get, url, null, new KeyValuePair<string, string>("X-Request-ID", Guid.NewGuid().ToString()));
                return true;
            }
            catch (Exception e)
            {
                //ProcessException(e);
                throw e;
            }

        }

        public async Task<PayApiResponse<IssuerInfo>> GetCardIssuerInfoAsync(string firstSixDigits)
        {
            var url = $"https://widget.cloudpayments.ru/Home/BinInfo?firstSixDigits={firstSixDigits}";

            try
            {
                var ret = await Request<PayApiResponse<IssuerInfo>>(HttpMethod.Get, url, null, new KeyValuePair<string, string>("X-Request-ID", Guid.NewGuid().ToString()));
                return ret;
            }
            catch (Exception e)
            {
                //ProcessException(e);
                throw e;
            }

        }

        /// <summary>
        /// visit https://www.cardinfo.online for more info
        /// </summary>
        /// <param name="firstSixDigits"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        public async Task<PayApiResponse<IssuerInfo>> GetCardIssuerInfoExternalAsync(string firstSixDigits, string apiKey)
        {
            var url = $"https://api.cardinfo.online?apiKey={apiKey}&input={firstSixDigits}";

            try
            {
                var ret = await Request<PayApiResponse<IssuerInfo>>(HttpMethod.Get, url, null, new KeyValuePair<string, string>("X-Request-ID", Guid.NewGuid().ToString()));
                return ret;
            }
            catch (Exception e)
            {
                //ProcessException(e);
                throw e;
            }

        }

        /*
         *В случае проведения платежа с токеном Google Pay 
         *в качестве имени держателя карты неоходимо указать: "Google Pay" 
         */

        /// <summary>
        /// Оплата по криптограмме
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        public async Task<PayApiResponse<Transaction>> ChargeNewCardAsync(PayRequestArgs options)
        {
            var url = "https://api.cloudpayments.ru/payments/cards/charge";

            try
            {
                var ret = await Request<PayApiResponse<Transaction>>(HttpMethod.Post, url, options, new KeyValuePair<string, string>("X-Request-ID", Guid.NewGuid().ToString()));

                return ret;
            }
            catch (Exception e)
            {
                //ProcessException(e);
                throw e;
            }

        }

        public async Task<PayApiResponse<Transaction>> ChargeExistingCardAsync(PayRequestArgs options)
        {
            var url = "https://api.cloudpayments.ru/payments/tokens/charge";

            try
            {
                var ret = await Request<PayApiResponse<Transaction>>(HttpMethod.Post, url, options, new KeyValuePair<string, string>("X-Request-ID", Guid.NewGuid().ToString()));

                return ret;
            }
            catch (Exception e)
            {
                //ProcessException(e);
                throw e;
            }

        }

        /// <summary>
        /// Для завершения оплаты выполните следующий метод Post3ds
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        public async Task<PayApiResponse<Transaction>> Post3DSAsync(Post3dsRequestArgs options)
        {
            var url = "https://api.cloudpayments.ru/payments/cards/post3ds";

            try
            {
                var ret = await Request<PayApiResponse<Transaction>>(HttpMethod.Post, url, options, new KeyValuePair<string, string>("X-Request-ID", Guid.NewGuid().ToString()));

                return ret;
            }
            catch (Exception e)
            {
                //ProcessException(e);
                throw e;
            }

        }


        #region System

        public ApiHelper(Func<HttpClient> createHttpClient)
        {
            _createHttpClient = createHttpClient;
        }

        protected Func<HttpClient> _createHttpClient;

        protected HttpClient CreateClient()
        {
            var client = _createHttpClient();

            return client;
        }

        protected async Task<TResult> Request<TResult>(
            HttpMethod method, 
            string url, 
            object fromBody, 
            params KeyValuePair<string, string>[] headers) where TResult : new()
        {
            Debug.WriteLine($"[CloudPaymentsSdk API] {String.Format("{0:mm:ss.ff}", DateTime.Now)} Started for {this.GetType().FullName}");

            TResult ret = default(TResult);

            await Task.Delay(10); //unblock UI thread

            var myClient = CreateClient();

            // add custom headers
            foreach (var header in headers)
            {
                if (string.IsNullOrEmpty(header.Value)) continue;

                myClient.DefaultRequestHeaders.Remove(header.Key); //important for other projects
                myClient.DefaultRequestHeaders.Add(header.Key, header.Value);
            }

            // add auth headers
            var byteArray = Encoding.ASCII.GetBytes($"{CloudPaymentsSdk.ClientId}:{CloudPaymentsSdk.ApiSecret}");
            var token = $"Basic {Convert.ToBase64String(byteArray)}";
            myClient.DefaultRequestHeaders.Add("Authorization", token);

            try
            {
                var uri = new Uri(url);
                var escape = Uri.EscapeDataString(uri.Query);

                HttpResponseMessage response;
                string content;

                if (method == HttpMethod.Post)
                {
                    StringContent encodedContent = null;
                    if (fromBody != null)
                    {

                        string json = JsonConvert.SerializeObject(
                            fromBody,
                            new JsonSerializerSettings
                            {
                                DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                                DateFormatHandling = DateFormatHandling.IsoDateFormat
                            });

#if DEBUG
                        var send = JsonConvert.SerializeObject(fromBody);
#endif
                        encodedContent = new StringContent(json, Encoding.UTF8, "application/json");

                        response = await myClient.PostAsync(uri, encodedContent);
                    }
                    else //fixed net core Unsupported Media Type:
                    {
                        var sjonContent = new JsonContent(string.Empty);
                        sjonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                        sjonContent.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("charset", "utf-8"));
                        sjonContent.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("IEEE754Compatible", "true"));

                        response = await myClient.PostAsync(uri, sjonContent);

                    }

                    content = response.Content.ReadAsStringAsync().Result;
                }
                else
                if (method == HttpMethod.Delete)
                {
                    response = await myClient.DeleteAsync(uri);
                    content = await response.Content.ReadAsStringAsync();
                }
                else
                if (method == HttpMethod.Get)
                {
                    response = await myClient.GetAsync(uri);
                    content = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    throw new Exception($"[CloudPaymentsSdk API] Unsupported method {method} for {url}");
                }

                Debug.WriteLine($"[CloudPaymentsSdk API] Response:{response.StatusCode} at {url}");

                //if (typeof(TResult) == typeof(ApiResponse))
                //{
                //    var responseRet = new ApiResponse()
                //    {
                //        StatusCode = response.StatusCode,
                //        Content = content,
                //        Url = url,
                //        Method = method
                //    };
                //    return (TResult)(object)responseRet;
                //}

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    ret = JsonConvert.DeserializeObject<TResult>(content);
                }
                else
                {
                    throw new Exception($"[CloudPaymentsSdk API] Unexpected result: {response.StatusCode} at {url}");
                }

                Debug.WriteLine($"[CloudPaymentsSdk API] {String.Format("{0:mm:ss.ff}", DateTime.Now)} success for {this.GetType().FullName}");

            }
            catch (Exception e)
            {
                Debug.WriteLine($"[CloudPaymentsSdk API] Request exception at {url} {e.Message}");
                
                CloudPaymentsSdk.OnApiException?.Invoke(e);
            }


 

            return ret;

        }



        #endregion

    }

}
