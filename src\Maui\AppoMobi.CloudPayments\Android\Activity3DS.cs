﻿using Android.App;
using Android.OS;
using AndroidX.AppCompat.App;
using AppoMobi.CloudPayments.Models;
using RU.Cloudpayments.Sdk.Three_ds;

namespace AppoMobi.CloudPayments.Android
{ 
    [Activity(Label = "3DS")]
    public class Activity3DS : AppCompatActivity, IThreeDSDialogListener
    {
     

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

         //   this.Title = "Оплата";




        }


        protected override void OnStart()
        {
            base.OnStart();

            var transaction = CloudPaymentsSdk.PendingTransaction;

            var fragment = ThreeDsDialogFragment.NewInstance(
                transaction.AcsUrl,
                transaction.TransactionId,
                transaction.PayerAuthenticationRequest);

            fragment.Show(SupportFragmentManager, "3DS");
        }

        protected override void OnPostCreate(Bundle savedInstanceState)
        {
            base.OnPostCreate(savedInstanceState);


        }

        public void OnAuthorizationCompleted(string md, string paRes)
        {

            // Успешное прохождение аутентификации, 
            // для завершения оплаты выполните запрос API post3ds
            // post3ds(md, paRes); 

            var model = new Post3dsRequestArgs
            {
                PaymentResponse = paRes,
                TransactionId = md
            };
            var result = new PayApiResponse<Post3dsRequestArgs>
            {
                Success = true,
                Model = model
            };
            
            Finish();

            CloudPaymentsPlatform.Request3DSCallback?.Invoke(result);

        }

        public void OnAuthorizationFailed(string html)
        {
            // Неудалось пройти аутентификацию, отобразите ошибку.
            var result = new PayApiResponse<Post3dsRequestArgs>
            {
                Message = "3DS failed"
            };
            
            Finish();

            CloudPaymentsPlatform.Request3DSCallback?.Invoke(result);
        }
    }
}