﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">iPhoneSimulator</Platform>
    <ProjectGuid>{73F0075A-F346-4331-BD42-C07E7B5DB374}</ProjectGuid>
    <ProjectTypeGuids>{FEACFBD2-3405-455C-9665-78FE426C6842};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{440aa056-593a-4519-8708-27081dee632f}</TemplateGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>AppoMobi.CloudPayments.iOS</RootNamespace>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
    <AssemblyName>AppoMobi.CloudPayments.iOS</AssemblyName>
    <MtouchEnableSGenConc>true</MtouchEnableSGenConc>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
    <ProvisioningType>automatic</ProvisioningType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhoneSimulator' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhoneSimulator\Debug</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>x86_64</MtouchArch>
    <MtouchLink>None</MtouchLink>
    <MtouchDebug>true</MtouchDebug>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhoneSimulator' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhoneSimulator\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchLink>None</MtouchLink>
    <MtouchArch>x86_64</MtouchArch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhone' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhone\Debug</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>ARM64</MtouchArch>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchDebug>true</MtouchDebug>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhone' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhone\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchArch>ARM64</MtouchArch>
    <CodesignKey>iPhone Developer</CodesignKey>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Main.cs" />
    <Compile Include="AppDelegate.cs" />
    <None Include="Info.plist" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Content Include="Entitlements.plist" />
    <InterfaceDefinition Include="LaunchScreen.storyboard" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Xamarin.iOS" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Xamarin.Essentials" Version="*******" />
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Contents.json">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon1024.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon167.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon120.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon152.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon180.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon29.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon40.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon58.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon76.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon80.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon87.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon20.png">
        <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon60.png">
        <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\iOS\Xamarin.iOS.CSharp.targets" />
</Project>