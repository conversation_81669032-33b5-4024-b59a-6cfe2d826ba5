﻿<?xml version="1.0" encoding="UTF-8" ?>
<input:SvgSliderBase
    x:Class="AppoMobi.Forms.Controls.Input.SvgSlider"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://xamarin.com/schemas/2014/forms/design"
    xmlns:input="clr-namespace:AppoMobi.Forms.Controls.Input;assembly=AppoMobi.Forms"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg;assembly=AppoMobi.Forms"
    xmlns:views="clr-namespace:AppoMobi.Forms.Xam.Touch;assembly=AppoMobi.Forms"
    x:Name="ThisSlider"
    HeightRequest="70"
    HorizontalOptions="Fill"
    VerticalOptions="Start"
    mc:Ignorable="d">

    <StackLayout
        AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
        AbsoluteLayout.LayoutFlags="All"
        InputTransparent="True"
        Spacing="0">

        <Grid VerticalOptions="Start">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>



            <!--  START DESC  -->
            <input:SliderValueDesc
                x:Name="StartDesc"
                Padding="0"
                BackgroundColor="{StaticResource ColorPrimary}"
                BorderColor="{StaticResource ColorAccent}"
                CornerRadius="0"
                HorizontalOptions="Start"
                Text="{Binding Source={x:Reference ThisSlider}, Path=StartValueDesc}"
                VerticalOptions="Start"
                XCenter="{Binding Source={x:Reference StartThumb}, Path=XCenter}"
                XMaxLimit="{Binding Source={x:Reference EndDesc}, Path=TranslationX}"
                XMinLimit="0">

                <Label
                    Margin="4,2"
                    HorizontalOptions="Start"
                    Text="{Binding Source={x:Reference StartDesc}, Path=Text}"
                    TextColor="{StaticResource ColorPaper}"
                    VerticalOptions="Center" />

            </input:SliderValueDesc>


            <!--  END DESC  -->
            <input:SliderValueDesc
                x:Name="EndDesc"
                Padding="0"
                BackgroundColor="{StaticResource ColorPrimary}"
                BorderColor="{StaticResource ColorAccent}"
                CornerRadius="0"
                HorizontalOptions="Start"
                Text="{Binding Source={x:Reference ThisSlider}, Path=EndValueDesc}"
                VerticalOptions="Start"
                XCenter="{Binding Source={x:Reference EndThumb}, Path=XCenter}"
                XMaxLimit="{Binding Source={x:Reference ThisSlider}, Path=Width}"
                XMinLimit="{Binding Source={x:Reference StartDesc}, Path=RightX}">

                <Label
                    Margin="4,2"
                    HorizontalOptions="Start"
                    Text="{Binding Source={x:Reference EndDesc}, Path=Text}"
                    TextColor="{StaticResource ColorPaper}"
                    VerticalOptions="Center" />

            </input:SliderValueDesc>


        </Grid>

        <!--  MAIN GRID  -->
        <Grid
            x:Name="ThisSliderGrid"
            Margin="0,8,0,0"
            HeightRequest="{Binding Source={x:Reference ThisSlider}, Path=SliderHeight}"
            HorizontalOptions="Fill"
            VerticalOptions="Start">

            <!--  TRAIL  -->
            <BoxView
                BackgroundColor="{StaticResource ColorMixedLight}"
                HeightRequest="2"
                HorizontalOptions="Fill"
                Opacity="0.5"
                VerticalOptions="Center" />

            <!--  SELECTED TRAIL  -->
            <input:SliderTrail
                BackgroundColor="{StaticResource ColorAccent}"
                HeightRequest="2"
                HorizontalOptions="Start"
                VerticalOptions="Center"
                XPos="{Binding Source={x:Reference StartThumb}, Path=TranslationX}"
                XPosEnd="{Binding Source={x:Reference EndThumb}, Path=TranslationX}" />

            <!--  START POINT  -->
            <svg:SvgIcon
                HeightRequest="6"
                HorizontalOptions="Start"
                IconFilePath="controls.thumb.svg"
                TintColor="{StaticResource ColorMixedLight}"
                VerticalOptions="Center"
                WidthRequest="6" />

            <!--  END POINT  -->
            <svg:SvgIcon
                HeightRequest="5"
                HorizontalOptions="End"
                IconFilePath="controls.thumb.svg"
                TintColor="{StaticResource ColorMixedLight}"
                VerticalOptions="Center"
                WidthRequest="5" />

            <!--  START THUMB  -->
            <input:SliderThumb
                x:Name="StartThumb"
                HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
                HorizontalOptions="Start"
                TranslationX="{Binding Source={x:Reference ThisSlider}, Path=StartThumbX}"
                VerticalOptions="Start"
                WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">
                <svg:SvgIcon
                    HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
                    HorizontalOptions="Start"
                    IconFilePath="controls.thumb.svg"
                    VerticalOptions="Start"
                    WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}" />
            </input:SliderThumb>

            <!--  END THUMB  -->
            <input:SliderThumb
                x:Name="EndThumb"
                HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
                HorizontalOptions="Start"
                TranslationX="{Binding Source={x:Reference ThisSlider}, Path=EndThumbX}"
                VerticalOptions="Start"
                WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">
                <svg:SvgIcon
                    HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
                    HorizontalOptions="Start"
                    IconFilePath="controls.thumb.svg"
                    VerticalOptions="Start"
                    WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}" />
            </input:SliderThumb>

        </Grid>


        <!--  BOTTOM INFO  -->
        <Grid Margin="0,2,0,0" VerticalOptions="Start">

            <Label
                FontSize="12"
                HorizontalOptions="Start"
                Text="{Binding Source={x:Reference ThisSlider}, Path=MinDesc}"
                TextColor="{StaticResource ColorMixedLight}" />

            <Label
                FontSize="12"
                HorizontalOptions="End"
                Text="{Binding Source={x:Reference ThisSlider}, Path=MaxDesc}"
                TextColor="{StaticResource ColorMixedLight}" />

        </Grid>


    </StackLayout>

    <!--  INPUT CATCHER  -->
    <views:ContentView
        Margin="0,8,0,0"
        AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
        AbsoluteLayout.LayoutFlags="All"
        Down="OnDown"
        HorizontalOptions="Fill"
        PanStyle="Simultaneously"
        Panned="OnPanned"
        Panning="OnPanning"
        UpCommand="{Binding Source={x:Reference ThisSlider}, Path=CommandUp}"
        VerticalOptions="Fill" />

</input:SvgSliderBase>