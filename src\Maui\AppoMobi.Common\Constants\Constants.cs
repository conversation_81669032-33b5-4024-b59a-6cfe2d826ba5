﻿
using System.Runtime.InteropServices;

namespace AppoMobi.Common
{



//===================================================================
    public static partial class Constants
//===================================================================
    {


        //API options
        public const bool SmsRegistrationDisabled = false;
        public const bool ApiLogToFile = true;

        public const string ServerTimeZone = "Russian Standard Time";
        public static string AllowedLangs = "ru";
        public const string ServerId = "horeca";

        public const double Version = 5;
        public const string ExportBaseFolder = "Files";

        //todo move to cp with thumbnails
        public const double SERVER_VERSION = 5.0;
        public const string ANDROID_VERSION = "5.0";
        public const string IOS_VERSION = "5.00";

        public const string LANG_RU = "RU";
        public const string LANG_FR = "FR";
        public const string LANG_EN = "EN";

        public const string WWW_LostPassword = ServerAuth + "/myaccount/forgotpassword";
        public const string UrlLogin = "token";
        public const string UrlRegister = "register";
        public const string UrlRegisterExternal = "loginex";


        public const string ServerBaseRemoteLegacy = @"https://appomobi.com/app";

        //        public const string ServerSignalRBase = @"http://10.0.2.2:55858";
        // public const string ServerSignalRBase = @"https://appomobi.com/api/v1";



        //public const string ServerBaseLocal = @"http://10.0.2.2:55003";
        public const string ServerBaseRemote = @"https://appomobi.com";

        public const string ServerApiSubfolder = @"/horecaapi/v1";

        public const string ServerApi = ServerBaseRemote + ServerApiSubfolder;
        public const string ServerAuth = ServerBaseRemote + ServerApiSubfolder;
        public const string ServerApp = ServerBaseRemote + @"/horeca";

        //https://appomobi.com/horecaapi/v1/assets/avatars/IMGbb09db007338325.jpg

        //  public const string ServerSignalRBase = ServerBaseRemote + @"/horecaapi/v1" + "/signalr";
        public const string ServerSignalRBase = ServerApi + "/signalr";


        public const string PrivacyBaseLink = ServerApp + "/privacy";

        public const string ServerRedirectLinkMask = ServerBaseRemote + @"/link/{0}/{1}"; //add tenantKey and linkAlias

        public const string UploadsFolderMask = @"/" + ExportBaseFolder + @"/{0}/Uploads"; //add TenantKey
        public const string ExportFolderMask = @"/" + ExportBaseFolder + @"/{0}/Out"; //add TenantKey
        public const string ThumbnailsFolderMask = @"/" + ExportBaseFolder + @"/{0}/Thumbs"; //add TenantKey

        //API_BasePathExport
        public const string ServerContentBaseMask = ServerApi + @"/" + ExportBaseFolder + @"/{0}/Out"; //add tenantKey

        //ThumbnailsBaseUrl
        public const string
            ServerThumbnailsBaseMask = ServerApi + @"/" + ExportBaseFolder + @"/{0}/Thumbs"; //add tenantKey 


        public const string KeyMain = "_main";

        public const string Protected = "protected";
        public const string KeyAppStrings = "appstrings";
        public const string KeyPush = "push";
        public const string KeyCompany = "company";
        public const string KeyProducts = "prods";
        public const string KeyNews = "news";
        public const string KeyGallery = "gallery";
        public const string KeyBlog = "blog";
        public const string KeyUI = "_ui";
        public const string KeyProdCats = "prodcats";
        public const string KeyProdElems = "prodelems";
        public const string KeyCenters = "salons";
        public const string KeySalon = "salon";
        public const string KeyRegions = "regions";
        public const string KeyAppoObject = "appoobject";
        public const string KeyAppo = "appo";

        public const string KeySportOrganizations = "sportorgs";
        public const string KeyTeams = "sportteams";
        public const string KeyArenas = "sportarenas";
        public const string KeySportRegions = "sportregions";
        public const string KeyArenaFeatures = "sportarenaprops";
        public const string KeySportEventElements = "sporteventprops";
        public const string KeySportEvents = "sportevents";

        public const string KeyServiceCats = "servicescats";
        public const string KeyServices = "services";


        public const string ThumbsSubPath = "/Export";
       


        public static string SubPath(string key)
        {
            return @"/" + key;
        }

        //---------------------------------------------------------------------------------------------------------
        public static string
            CombineSectionPath(string tenantKey, string tag, string lang) //can be salons/ + mos/ or just news/
            //---------------------------------------------------------------------------------------------------------
        {
            var subPath = tag.Replace(@"/", "");
            var path = string.Format(ServerContentBaseMask, tenantKey) + @"/" + subPath + @"/" + lang +
                       Constants.API_EndPath;
            return path;
        }

        //---------------------------------------------------------------------------------------------------------
        public static string
            CombineSectionPathWithoutEnd(string tenantKey, string tag) //can be salons/ + mos/ or just news/
            //---------------------------------------------------------------------------------------------------------
        {
            var subPath = tag.Replace(@"/", "");
            var path = string.Format(ServerContentBaseMask, tenantKey) + @"/" + subPath + @"/";
            return path;
        }


        public const string API_EndPath = @"/tmp.json";


        public const string ThumbnailLargeAddUrlProduct = @"/prods/{0}-mini-large.jpg";
        public const string ThumbnailMiniAddUrlProduct = @"/prods/{0}-mini-mini.jpg";
        public const string ThumbnailMicroAddUrlProduct = @"/prods/{0}-mini-micro.jpg";

        public const string ThumbnailLargeAddUrlSalon = @"/salons/{0}-large.jpg";
        public const string ThumbnailMiniAddUrlSalon = @"/salons/{0}-mini.jpg";
        public const string ThumbnailMicroAddUrlSalon = @"/salons/{0}-micro.jpg";

        public const string ThumbnailLargeAddUrlNews = @"/news/{0}-large.jpg";
        public const string ThumbnailMiniAddUrlNews = @"/news/{0}-mini.jpg";
        public const string ThumbnailMicroAddUrlNews = @"/news/{0}-micro.jpg";

        public const string ThumbnailLargeAddUrlProdCat = @"/prodcats/{0}-large.jpg";
        public const string ThumbnailMiniAddUrlProdCat = @"/prodcats/{0}-mini.jpg";
        public const string ThumbnailMicroAddUrlProdCat = @"/prodcats/{0}-micro.jpg";

        public const string ThumbnailLargeAddUrlProdCatMini = @"/prodcats/{0}-mini-large.jpg";
        public const string ThumbnailMiniAddUrlProdCatMini = @"/prodcats/{0}-mini-mini.jpg";
        public const string ThumbnailMicroAddUrlProdCatMini = @"/prodcats/{0}-mini-micro.jpg";



        //---------------------------------------------------------------------------------------------------------
    }


}