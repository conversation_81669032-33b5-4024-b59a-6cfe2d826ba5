﻿using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public class ServerSavedCardDto
    {
        [JsonProperty("token")]
        public string Token { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("num_first")]
        public string NbStart { get; set; }

        [JsonProperty("num_last")]
        public string NbEnd { get; set; }

        [JsonProperty("exp")]
        public string Expiry { get; set; }

        [JsonProperty("default")]
        [Json<PERSON>onverter(typeof(BoolConverter))]
        public bool IsDefault { get; set; }

        [JsonProperty("json_data")]
        public string JsonData { get; set; }
    }
}