﻿using System;
using Newtonsoft.Json;

namespace AppoMobi.Common.DTO.Aromarket
{
    public class RequestNewsDto : WithTokenDto
    {
        [JsonProperty("page")]
        public int PageFrom1 { get; set; }

        [JsonProperty("limit")]
        public int PageSize { get; set; } = 20;

        [JsonProperty("only_new")]
        public bool OnlyNew { get; set; } = true;

        [JsonProperty("time_from")]
        public DateTime? TimeFrom { get; set; }

        [JsonProperty("time_to")]
        public DateTime? TimeTo { get; set; }
    }
}