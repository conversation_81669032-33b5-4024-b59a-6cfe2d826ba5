﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
		Microsoft ResX Schema

		Version 1.3

		The primary goals of this format is to allow a simple XML format 
		that is mostly human readable. The generation and parsing of the 
		various data types are done through the TypeConverter classes 
		associated with the data types.

		Example:

		... ado.net/XML headers & schema ...
		<resheader name="resmimetype">text/microsoft-resx</resheader>
		<resheader name="version">1.3</resheader>
		<resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
		<resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
		<data name="Name1">this is my long string</data>
		<data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
		<data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
			[base64 mime encoded serialized .NET Framework object]
		</data>
		<data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
			[base64 mime encoded string representing a byte array form of the .NET Framework object]
		</data>

		There are any number of "resheader" rows that contain simple 
		name/value pairs.

		Each data row contains a name, and value. The row also contains a 
		type or mimetype. Type corresponds to a .NET class that support 
		text/value conversion through the TypeConverter architecture. 
		Classes that don't support this are serialized and stored with the 
		mimetype set.

		The mimetype is used for serialized objects, and tells the 
		ResXResourceReader how to depersist the object. This is currently not 
		extensible. For a given mimetype the value must be set accordingly:

		Note - application/x-microsoft.net.object.binary.base64 is the format 
		that the ResXResourceWriter will generate, however the reader can 
		read any of the formats listed below.

		mimetype: application/x-microsoft.net.object.binary.base64
		value   : The object must be serialized with 
			: System.Serialization.Formatters.Binary.BinaryFormatter
			: and then encoded with base64 encoding.

		mimetype: application/x-microsoft.net.object.soap.base64
		value   : The object must be serialized with 
			: System.Runtime.Serialization.Formatters.Soap.SoapFormatter
			: and then encoded with base64 encoding.

		mimetype: application/x-microsoft.net.object.bytearray.base64
		value   : The object must be serialized into a byte array 
			: using a System.ComponentModel.TypeConverter
			: and then encoded with base64 encoding.
	-->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>1.3</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.3500.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.3500.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Test" xml:space="preserve">
    <value>Test</value>
  </data>
  <data name="AddNews" xml:space="preserve">
    <value>Add News</value>
  </data>
  <data name="NewsTitleDesc" xml:space="preserve">
    <value>News</value>
  </data>
  <data name="BtnEdit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="BtnDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="BtnDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="News" xml:space="preserve">
    <value>News</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="OwnerTitle" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="EditTitle" xml:space="preserve">
    <value>Editing</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Back to List</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Lang" xml:space="preserve">
    <value>en</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="LangCode" xml:space="preserve">
    <value>En</value>
  </data>
  <data name="LangDesc" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Regions</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>Create New</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="MapZoom" xml:space="preserve">
    <value>Map Zoom</value>
  </data>
  <data name="MapCenterY" xml:space="preserve">
    <value>Map Center Y</value>
  </data>
  <data name="MapCenterX" xml:space="preserve">
    <value>Map Center X</value>
  </data>
  <data name="RegionsTitleDesc" xml:space="preserve">
    <value>Region Inside Mobile App</value>
  </data>
  <data name="TitleDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="CreateTitle" xml:space="preserve">
    <value>Creating</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Are you sure you want to delete this?</value>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="DividerOr" xml:space="preserve">
    <value>or</value>
  </data>
  <data name="AddRegion" xml:space="preserve">
    <value>Add Region</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="YourSecCode" xml:space="preserve">
    <value>Your security code is: </value>
  </data>
  <data name="EmailFrom" xml:space="preserve">
    <value>Art of Foto Control Panel</value>
  </data>
  <data name="EmailCreateAccSubject" xml:space="preserve">
    <value>Art of Foto: confirm account creation</value>
  </data>
  <data name="EmailCreateAccBody" xml:space="preserve">
    <value>Art of Foto has received a request to create a user account&lt;br&gt;
using your email address ({0}).&lt;br&gt;
&lt;br&gt;
To continue creating an account using this email address, visit the &lt;br&gt;
following link:&lt;br&gt;
&lt;br&gt;
&lt;a href="{1}" target="_blank" rel="noopener"&gt;{1}&lt;/a&gt;&lt;br&gt;
&lt;br&gt;
If you do not wish to create an account, or if this request was made in&lt;br&gt;
error you can just ignore this message.&lt;br&gt;
&lt;br&gt;
If the above link do not work, or you have any other issues regarding&lt;br&gt;
your account, please contact <NAME_EMAIL>.&lt;br&gt;
&lt;br&gt;</value>
  </data>
  <data name="AccCreationTitle" xml:space="preserve">
    <value>Account creation</value>
  </data>
  <data name="AccCeationConfirmEmail" xml:space="preserve">
    <value>We have sent you an email to the following email address: {0}.
Please follow the instructions in this email to complete the registration.</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>Parameters</value>
  </data>
  <data name="ImageURL" xml:space="preserve">
    <value>Image (url)</value>
  </data>
  <data name="Author" xml:space="preserve">
    <value>Author</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>Edited By</value>
  </data>
  <data name="EditedTime" xml:space="preserve">
    <value>Edited Time</value>
  </data>
  <data name="ImageHeight" xml:space="preserve">
    <value>Image Height</value>
  </data>
  <data name="ImageWidth" xml:space="preserve">
    <value>Image Width</value>
  </data>
  <data name="ThankYouForConfirmingYourEmailPlease" xml:space="preserve">
    <value>Thank you for confirming your email. Please</value>
  </data>
  <data name="ClickHereToLogIn" xml:space="preserve">
    <value>Click here to Log in</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="YouVeSuccessfullyAuthenticatedWith" xml:space="preserve">
    <value>You've successfully authenticated with</value>
  </data>
  <data name="PleaseEnterAUserNameForThisSiteBelow" xml:space="preserve">
    <value>Please enter a user name for this site below and click the Register button to finish logging in.</value>
  </data>
  <data name="RegisterTitle" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="AssociateYourAccount" xml:space="preserve">
    <value>Associate your {0} account.</value>
  </data>
  <data name="UnsuccessfulLoginWithService" xml:space="preserve">
    <value>Unsuccessful login with service.</value>
  </data>
  <data name="LoginFailure" xml:space="preserve">
    <value>Login Failure</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>Log in</value>
  </data>
  <data name="OrUseAnotherServiceToLogIn" xml:space="preserve">
    <value>Or use another service to log in</value>
  </data>
  <data name="UseALocalAccountToLogIn" xml:space="preserve">
    <value>Use a local account</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Remember Me</value>
  </data>
  <data name="BtnLogIn" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="RegisterAsANewUser" xml:space="preserve">
    <value>Create a new account</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="CreateANewAccount" xml:space="preserve">
    <value>Create a new account</value>
  </data>
  <data name="BtnRegister" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="ToolbarLogin" xml:space="preserve">
    <value>Log In</value>
  </data>
  <data name="ToolbarRegister" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="ToolbarHello" xml:space="preserve">
    <value>Hello, </value>
  </data>
  <data name="ToolbarLogoff" xml:space="preserve">
    <value>(Do not forget to) Log Off</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="MoreInfo" xml:space="preserve">
    <value>More Info</value>
  </data>
  <data name="OnMap" xml:space="preserve">
    <value>On Map</value>
  </data>
  <data name="Centers" xml:space="preserve">
    <value>Centers</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>Mail</value>
  </data>
  <data name="Metro" xml:space="preserve">
    <value>Metro</value>
  </data>
  <data name="ExportedBy" xml:space="preserve">
    <value>ExportNeeded By</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="ExportedTime" xml:space="preserve">
    <value>ExportNeeded</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>Subtitle</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="Uploads" xml:space="preserve">
    <value>Files</value>
  </data>
  <data name="UploadImage" xml:space="preserve">
    <value>Upload image</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>Search by name </value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="NotAllowed" xml:space="preserve">
    <value>Not allowed</value>
  </data>
  <data name="Allowed" xml:space="preserve">
    <value>Allowed</value>
  </data>
  <data name="Needed" xml:space="preserve">
    <value>, needed!</value>
  </data>
  <data name="ToBeExported" xml:space="preserve">
    <value>To be exported</value>
  </data>
  <data name="HelpAllowToBeExportedForMobileAppOrNot" xml:space="preserve">
    <value>Allow to be exported for mobile app or not.</value>
  </data>
  <data name="SortList" xml:space="preserve">
    <value>Sort List</value>
  </data>
  <data name="SortAbc" xml:space="preserve">
    <value>Sort by Abc</value>
  </data>
  <data name="SortDate" xml:space="preserve">
    <value>By Edition Date</value>
  </data>
  <data name="NewsController_Create_ERRORUImageURLNotValid" xml:space="preserve">
    <value>ERROR: Image URL not valid!</value>
  </data>
  <data name="OwnerTitleShort" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="AppoMobiControlPanel" xml:space="preserve">
    <value>Control Panel</value>
  </data>
  <data name="Exports" xml:space="preserve">
    <value>Exports</value>
  </data>
  <data name="CreateExportFor" xml:space="preserve">
    <value>Execute export for:</value>
  </data>
  <data name="ExportType" xml:space="preserve">
    <value>Export Type</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>Access Denied</value>
  </data>
  <data name="DonTHaveTheRights" xml:space="preserve">
    <value>It appears You don't have the rights to acces this section. Please contact support if You think it might be a mistake.
</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Exporting</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>Are you sure you want to export this?</value>
  </data>
  <data name="ExportsController_Index_ExportComplete" xml:space="preserve">
    <value>Export complete successfully!</value>
  </data>
  <data name="BaseURL" xml:space="preserve">
    <value>Base URL</value>
  </data>
  <data name="SalonList" xml:space="preserve">
    <value>Salon List</value>
  </data>
  <data name="InSection" xml:space="preserve">
    <value>inside</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="ShouldNotBeExported" xml:space="preserve">
    <value>Should not be exported</value>
  </data>
  <data name="WasWellExported" xml:space="preserve">
    <value>Was well exported</value>
  </data>
  <data name="ShouldBeExported" xml:space="preserve">
    <value>Should be exported</value>
  </data>
  <data name="InRegion" xml:space="preserve">
    <value>in the region of</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>by</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Products</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>Categories</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="RoleSuperuser" xml:space="preserve">
    <value>Superuser</value>
  </data>
  <data name="RoleAdmin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="RoleEditor" xml:space="preserve">
    <value>Editor</value>
  </data>
  <data name="RoleNoRole" xml:space="preserve">
    <value>Guest</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="SortDefault" xml:space="preserve">
    <value>By Default</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Subcategories" xml:space="preserve">
    <value>Subcategories</value>
  </data>
  <data name="ParentElementToInsertCategoryInto" xml:space="preserve">
    <value>Parent category to insert this element into</value>
  </data>
  <data name="RootCategory" xml:space="preserve">
    <value>Root Category</value>
  </data>
  <data name="CatNewsSlider" xml:space="preserve">
    <value>News Slider</value>
  </data>
  <data name="CatSecRoot" xml:space="preserve">
    <value>Secondary Base Category</value>
  </data>
  <data name="UploadMiniImage" xml:space="preserve">
    <value>Upload MINI-image</value>
  </data>
  <data name="ImageURLForMini" xml:space="preserve">
    <value>MINI-Image URL</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="Volume" xml:space="preserve">
    <value>Volume</value>
  </data>
  <data name="Recommendation" xml:space="preserve">
    <value>Thalion Recommendation</value>
  </data>
  <data name="ILike" xml:space="preserve">
    <value>I Like</value>
  </data>
  <data name="Units" xml:space="preserve">
    <value>Units</value>
  </data>
  <data name="Keywords" xml:space="preserve">
    <value>Keywords</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="ShowList" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search..</value>
  </data>
  <data name="ProductsController_CreateDropdownList_ANYCAT" xml:space="preserve">
    <value>Any Category</value>
  </data>
  <data name="SortCode" xml:space="preserve">
    <value>By Code</value>
  </data>
  <data name="SortCat" xml:space="preserve">
    <value>By Category</value>
  </data>
  <data name="RoleMerchandiser" xml:space="preserve">
    <value>Merchandiser</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="CatRoot2" xml:space="preserve">
    <value>Base Category 2</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>Enter reason</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>Featured Image</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>Body</value>
  </data>
  <data name="Face" xml:space="preserve">
    <value>Face</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="OurChoice" xml:space="preserve">
    <value>Our Choice</value>
  </data>
  <data name="ForgotYourPassword" xml:space="preserve">
    <value>Forgot password?</value>
  </data>
  <data name="NoRUTranslation" xml:space="preserve">
    <value>No RU translation</value>
  </data>
  <data name="ErrorNotFound" xml:space="preserve">
    <value>Not Found Error</value>
  </data>
  <data name="ErrorUnknown" xml:space="preserve">
    <value>Unknown Error</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>Links</value>
  </data>
  <data name="Redirect" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="Clicks" xml:space="preserve">
    <value>Clicks Total</value>
  </data>
  <data name="AreYouSureToDelete" xml:space="preserve">
    <value>Are You sure to delete</value>
  </data>
  <data name="Treatment" xml:space="preserve">
    <value>Treatment</value>
  </data>
  <data name="Treatments" xml:space="preserve">
    <value>Treatments</value>
  </data>
  <data name="ErrorPleaseCheckRequirementsForFieldsBelow" xml:space="preserve">
    <value>Error: Please check requirements for fields below.</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Items</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>Management</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="ResetChanges" xml:space="preserve">
    <value>Cancel Changes</value>
  </data>
  <data name="DoNotSave" xml:space="preserve">
    <value>Do Not Save</value>
  </data>
  <data name="List" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="EditorSNotesInternalUseOnly" xml:space="preserve">
    <value>Editor's notes, internal use only</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ControlPanelHtml" xml:space="preserve">
    <value>&lt;strong&gt;Art of Foto&lt;/strong&gt; Control Panel</value>
  </data>
  <data name="RememberMe2" xml:space="preserve">
    <value>Remember Me?</value>
  </data>
  <data name="DidYouRememberYourPassword" xml:space="preserve">
    <value>Did you remember your password?</value>
  </data>
  <data name="BtnResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="PleaseCheckYourEmailToResetYourPassword" xml:space="preserve">
    <value>Please check your email to reset your password.</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="DoYouHaveAnAccount" xml:space="preserve">
    <value>Do you have an account?</value>
  </data>
  <data name="RegisterAccount" xml:space="preserve">
    <value>Register Account</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="AgreeToTerms" xml:space="preserve">
    <value>Agree to &lt;strong&gt;the terms&lt;/strong&gt;</value>
  </data>
  <data name="YouMustAcceptTermsAndConditions" xml:space="preserve">
    <value>You must accept terms and conditions.</value>
  </data>
  <data name="PasswordAndConfirmationPasswordDoNotMatch" xml:space="preserve">
    <value>The password and confirmation password do not match.</value>
  </data>
  <data name="StringLengthError" xml:space="preserve">
    <value>The {0} must be at least {2} characters long.</value>
  </data>
  <data name="BadUsernameOrPassword" xml:space="preserve">
    <value>The username or password you entered is incorrect.</value>
  </data>
  <data name="EmailAlreadyTaken" xml:space="preserve">
    <value>Email '{0}' is already taken. </value>
  </data>
  <data name="MailSubjectResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="ResetYourPasswordMailBody" xml:space="preserve">
    <value>Please reset your password by clicking &lt;a href="{0}"&gt;here&lt;/a&gt;.</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="PleaseEnterYourNewPasswordBelow" xml:space="preserve">
    <value>Please enter your new password below.</value>
  </data>
  <data name="DidYouRememberYourOLDPassword" xml:space="preserve">
    <value>Did you remember your old password?</value>
  </data>
  <data name="AccountController_ResetPassword_InvalidToken" xml:space="preserve">
    <value>Your reset link has expired.</value>
  </data>
  <data name="YourNewPasswordHasBeenSet" xml:space="preserve">
    <value>Your new password has been set.</value>
  </data>
  <data name="Class" xml:space="preserve">
    <value>Class</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Privacy</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Published</value>
  </data>
  <data name="FastLoginWith" xml:space="preserve">
    <value>Fast Login With</value>
  </data>
  <data name="OrEnterYourCredentials" xml:space="preserve">
    <value>or enter your credentials</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="ExternalLogins" xml:space="preserve">
    <value>External Logins</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="ChangeYourPassword" xml:space="preserve">
    <value>Change your password</value>
  </data>
  <data name="ManageAccount" xml:space="preserve">
    <value>Manage Account</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>Change</value>
  </data>
  <data name="TwoFactorAuthentication" xml:space="preserve">
    <value>Two-Factor Authentication</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Your phone number</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Enabled</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="Manage" xml:space="preserve">
    <value>Manage</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Disable</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="ManageYourExternalLogins" xml:space="preserve">
    <value>Manage your external logins</value>
  </data>
  <data name="X_Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Key</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ThankYou" xml:space="preserve">
    <value>Thank You!</value>
  </data>
  <data name="EmailConfirmed" xml:space="preserve">
    <value>Thank you for confirming your email. You can now use your credentials to log in.</value>
  </data>
  <data name="AccNotActiveForCLient" xml:space="preserve">
    <value>Your account has not been yet assigned to an existing Art of Foto client. Please contact your tech support.</value>
  </data>
  <data name="NothingWasFound" xml:space="preserve">
    <value>Nothing was found!</value>
  </data>
  <data name="ClientControlPanel" xml:space="preserve">
    <value>Client Control Panel</value>
  </data>
  <data name="ThankYouForBeingPatient" xml:space="preserve">
    <value>Thank you for being patient. We are doing some work on the site that will be back soon.</value>
  </data>
  <data name="UnderConstruction" xml:space="preserve">
    <value>Under construction</value>
  </data>
  <data name="SorryWeReDoingSomeWorkOnTheSite" xml:space="preserve">
    <value>Sorry, we're doing some work on the site</value>
  </data>
  <data name="Desktop" xml:space="preserve">
    <value>Desktop</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="ProductElements" xml:space="preserve">
    <value>Product Elements</value>
  </data>
  <data name="Specifications" xml:space="preserve">
    <value>Components</value>
  </data>
  <data name="DescriptionRU" xml:space="preserve">
    <value>Description RU</value>
  </data>
  <data name="DescriptionEN" xml:space="preserve">
    <value>Description EN</value>
  </data>
  <data name="DescriptionFR" xml:space="preserve">
    <value>Description FR</value>
  </data>
  <data name="DisabledEntryDesc" xml:space="preserve">
    <value>Will not be exported outside control panel if turned On</value>
  </data>
  <data name="UploadFileFieldDesc" xml:space="preserve">
    <value>You can upload a file or enter an existing URL in the next field</value>
  </data>
  <data name="NoAction" xml:space="preserve">
    <value>No Action</value>
  </data>
  <data name="OpenProductInApp" xml:space="preserve">
    <value>Open product in app</value>
  </data>
  <data name="NavigateToUrl" xml:space="preserve">
    <value>Navigate to url</value>
  </data>
  <data name="FieldMustBeUnique" xml:space="preserve">
    <value>Field '{0}' value must be unique</value>
  </data>
  <data name="ContentLanguages" xml:space="preserve">
    <value>Content Languages</value>
  </data>
  <data name="Enter2LettersLanguageCodes" xml:space="preserve">
    <value>Enter 2-letters language codes</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="CompanyInfo" xml:space="preserve">
    <value>Company Info</value>
  </data>
  <data name="OrUploadFromDisk" xml:space="preserve">
    <value>Modify..</value>
  </data>
  <data name="SortOutDate" xml:space="preserve">
    <value>By Release Date</value>
  </data>
  <data name="SortPriority" xml:space="preserve">
    <value>Sort Priority</value>
  </data>
  <data name="ShowOnPage" xml:space="preserve">
    <value>Show on page</value>
  </data>
  <data name="ExportSection" xml:space="preserve">
    <value>Fast Export</value>
  </data>
  <data name="PlsConfirmExport" xml:space="preserve">
    <value>Are You sure to export this section now?</value>
  </data>
  <data name="BaseControllerContent__IndexGet_ExportCompletedWithSuccess" xml:space="preserve">
    <value>Export completed with success.</value>
  </data>
  <data name="CEOSiteDesc" xml:space="preserve">
    <value></value>
  </data>
  <data name="SiteLoading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="PushMessages" xml:space="preserve">
    <value>Push Messages</value>
  </data>
  <data name="NewsMenu" xml:space="preserve">
    <value>Your News</value>
  </data>
  <data name="NavigateToWww" xml:space="preserve">
    <value>Navigate to url inside app</value>
  </data>
  <data name="SimpleMessage" xml:space="preserve">
    <value>Simple message</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>Send now</value>
  </data>
  <data name="SaveForLater" xml:space="preserve">
    <value>Save for later</value>
  </data>
  <data name="PushEngagedUsers" xml:space="preserve">
    <value>Engaged users</value>
  </data>
  <data name="PushActiveUsers" xml:space="preserve">
    <value>Active users</value>
  </data>
  <data name="PushInactiveUsers" xml:space="preserve">
    <value>Inactive users</value>
  </data>
  <data name="OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage" xml:space="preserve">
    <value>Text cannot be empty for the English language.</value>
  </data>
  <data name="Android" xml:space="preserve">
    <value>Android</value>
  </data>
  <data name="AppleIOS" xml:space="preserve">
    <value>Apple iOS</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>Dev</value>
  </data>
  <data name="DevicesTotal" xml:space="preserve">
    <value>Devices total</value>
  </data>
  <data name="SavedForLater" xml:space="preserve">
    <value>Saved for later</value>
  </data>
  <data name="maskSentToDevices" xml:space="preserve">
    <value>Sent to {0} devices</value>
  </data>
  <data name="PushMessagesWereNotConfigured" xml:space="preserve">
    <value>Push messages were not configured for You yet.</value>
  </data>
  <data name="Tenants" xml:space="preserve">
    <value>Tenants</value>
  </data>
  <data name="ClientGlobalSettings" xml:space="preserve">
    <value>Client Global Settings</value>
  </data>
  <data name="ChangesSaved" xml:space="preserve">
    <value>Changes saved</value>
  </data>
  <data name="NewsController_NewsController_ByRegion" xml:space="preserve">
    <value>By display region</value>
  </data>
  <data name="MessagableDevicesTotal" xml:space="preserve">
    <value>Messagable devices total</value>
  </data>
  <data name="TotalInstallations" xml:space="preserve">
    <value>Total installations</value>
  </data>
  <data name="CreateAPassword" xml:space="preserve">
    <value>Create a password</value>
  </data>
  <data name="YourSecCodeLoginMask" xml:space="preserve">
    <value>Your security code is {0}. Use it to login to Art of Foto Control Panel.</value>
  </data>
  <data name="IncorrectEmailAddressOrPhoneNumber" xml:space="preserve">
    <value>Incorrect email address or phone number.</value>
  </data>
  <data name="AUserWithThisPhoneNumberWasNotFoundPleaseRegister" xml:space="preserve">
    <value>A user with this phone number was not found. Please register.</value>
  </data>
  <data name="PleaseCheckYouDevice" xml:space="preserve">
    <value>Please check you device</value>
  </data>
  <data name="WeHaveSentAVerificationCodeToYourNumber" xml:space="preserve">
    <value>We have sent a verification code to your number</value>
  </data>
  <data name="UserWithThisPhoneNumberAlreadyRegistered" xml:space="preserve">
    <value>User with this phone number already registered.</value>
  </data>
  <data name="WrongCodeEntered" xml:space="preserve">
    <value>Wrong code entered.</value>
  </data>
  <data name="StatusConfirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="StatusPendingConfirmation" xml:space="preserve">
    <value>Pending confirmation</value>
  </data>
  <data name="StatusDisapproved" xml:space="preserve">
    <value>Disapproved</value>
  </data>
  <data name="BookingSystem" xml:space="preserve">
    <value>Booking System</value>
  </data>
  <data name="BookingFrontDesk" xml:space="preserve">
    <value>Front Desk</value>
  </data>
  <data name="BookingSchedule" xml:space="preserve">
    <value>Working Schedule</value>
  </data>
  <data name="BookingRequests" xml:space="preserve">
    <value>Booking Requests</value>
  </data>
  <data name="BookingObjects" xml:space="preserve">
    <value>Booking Objects</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Add Event</value>
  </data>
  <data name="InsertEventName" xml:space="preserve">
    <value>Insert Event Name</value>
  </data>
  <data name="DragAndDropEventsOnTheCalendar" xml:space="preserve">
    <value>Drag and Drop Events on the Calendar</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>Connection error</value>
  </data>
  <data name="LatestMobileAppVersion" xml:space="preserve">
    <value>Latest mobile app version</value>
  </data>
  <data name="OutdatedMobileAppVersion" xml:space="preserve">
    <value>Outdated mobile app version</value>
  </data>
  <data name="StartEvent" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="EndEvent" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>All day</value>
  </data>
  <data name="_2Weeks" xml:space="preserve">
    <value>2 weeks</value>
  </data>
  <data name="AppoController_Bookable_BlockDayForBooking" xml:space="preserve">
    <value>Booking prohibited</value>
  </data>
  <data name="AreYouSureToDeleteThisEvent" xml:space="preserve">
    <value>Are you sure to delete this event ?</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>Just a quick second..</value>
  </data>
  <data name="EditEvent" xml:space="preserve">
    <value>Edit Event</value>
  </data>
  <data name="EventCard" xml:space="preserve">
    <value>Event Card</value>
  </data>
  <data name="Confirned" xml:space="preserve">
    <value>confirned</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>confirmation pending</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Object</value>
  </data>
  <data name="ServicesCategories" xml:space="preserve">
    <value>Services Categories</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Full Name</value>
  </data>
  <data name="MapX" xml:space="preserve">
    <value>MapX</value>
  </data>
  <data name="KeyHint" xml:space="preserve">
    <value>Required unique key, used in mobile app.</value>
  </data>
  <data name="ReservedField" xml:space="preserve">
    <value>Not used.</value>
  </data>
  <data name="DbNews_UrlProductCodeEtc" xml:space="preserve">
    <value>Url, product code (field "Key") etc..</value>
  </data>
  <data name="DbNews_WhatToDoWhenNewsFrameIsClickedInApp" xml:space="preserve">
    <value>What to do when news frame is clicked in app</value>
  </data>
  <data name="DbNews_NewsText" xml:space="preserve">
    <value>News text</value>
  </data>
  <data name="DbNews_LanguageAreaTheNewsWillBeShownIn" xml:space="preserve">
    <value>Language area the news will be shown in.</value>
  </data>
  <data name="DbNews_ImageToBeShownInTheNews" xml:space="preserve">
    <value>Image to be shown in the news.</value>
  </data>
  <data name="InternationalTitlesLanguage" xml:space="preserve">
    <value>International Titles Language</value>
  </data>
  <data name="PriorityDesc" xml:space="preserve">
    <value>The higher the priority the higher item will be shown in list</value>
  </data>
  <data name="EnabledModules" xml:space="preserve">
    <value>Enabled Modules</value>
  </data>
  <data name="NeedAllUsersRelog" xml:space="preserve">
    <value>Log off users</value>
  </data>
  <data name="NeedAllUsersRelogDesc" xml:space="preserve">
    <value>Make all users of the control panel related to this client relog so that the visual changes take effect.</value>
  </data>
  <data name="HowToUse" xml:space="preserve">
    <value>How to use</value>
  </data>
  <data name="RefCodeDesc" xml:space="preserve">
    <value>Reference code</value>
  </data>
  <data name="TargetPlatfrom" xml:space="preserve">
    <value>Platform</value>
  </data>
  <data name="TitleDesc" xml:space="preserve">
    <value>Displayed title </value>
  </data>
  <data name="MessageTextDesc" xml:space="preserve">
    <value>Message body</value>
  </data>
  <data name="TargetSegment" xml:space="preserve">
    <value>Recipients</value>
  </data>
  <data name="TenantNameDesc" xml:space="preserve">
    <value>Client name in the control panel</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Working Days</value>
  </data>
  <data name="WorkingTimeStart" xml:space="preserve">
    <value>Working Hours From</value>
  </data>
  <data name="WorkingTimeEnd" xml:space="preserve">
    <value>Working Hours To</value>
  </data>
  <data name="SexRestriction" xml:space="preserve">
    <value>Sex Restriction</value>
  </data>
  <data name="WorkingTimePauseEnd" xml:space="preserve">
    <value>Break end</value>
  </data>
  <data name="LandingForClients" xml:space="preserve">
    <value>Clients</value>
  </data>
  <data name="LandingEnterHere" xml:space="preserve">
    <value>enter here</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Teams</value>
  </data>
  <data name="Goalkeepers" xml:space="preserve">
    <value>Goalkeepers</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Coaches</value>
  </data>
  <data name="Since" xml:space="preserve">
    <value>Since</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Rating</value>
  </data>
  <data name="SortRating" xml:space="preserve">
    <value>By Rating</value>
  </data>
  <data name="VK" xml:space="preserve">
    <value>VKontakte</value>
  </data>
  <data name="ArenaFeaturesDesc" xml:space="preserve">
    <value>More Info</value>
  </data>
  <data name="DifficultyLevel" xml:space="preserve">
    <value>Difficulty Level</value>
  </data>
  <data name="PriceDetailsDesc" xml:space="preserve">
    <value>"per hour" etc..</value>
  </data>
  <data name="PriceDetails" xml:space="preserve">
    <value>Price Details</value>
  </data>
  <data name="WeekDays" xml:space="preserve">
    <value>Days Of Week</value>
  </data>
  <data name="GenerateDropDowns_Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="WorkingTimePauseStart" xml:space="preserve">
    <value>Break start</value>
  </data>
  <data name="TimeStart" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="TimeEnd" xml:space="preserve">
    <value>End Time</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>Event details</value>
  </data>
  <data name="_Empty" xml:space="preserve">
    <value> </value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>Events</value>
  </data>
  <data name="EventsElements" xml:space="preserve">
    <value>Events elements</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>Organizations</value>
  </data>
  <data name="Organization" xml:space="preserve">
    <value>Organization</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>Event Type</value>
  </data>
  <data name="BaseControllerContent_GenerateDropDowns_Rally" xml:space="preserve">
    <value>Rally</value>
  </data>
  <data name="Championship" xml:space="preserve">
    <value>Championship</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Schedule Type</value>
  </data>
  <data name="ByDaysOfWeek" xml:space="preserve">
    <value>By days of week</value>
  </data>
  <data name="WithFixedDate" xml:space="preserve">
    <value>With fixed dates</value>
  </data>
  <data name="ScheduleTypeDesc" xml:space="preserve">
    <value>If with fixed dates, days of the week are not used and vice versa.</value>
  </data>
  <data name="Schedules" xml:space="preserve">
    <value>Schedules</value>
  </data>
  <data name="NeedRelogUser" xml:space="preserve">
    <value>Log off user</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Date of Birth</value>
  </data>
  <data name="ValidUsernameRequired" xml:space="preserve">
    <value>Valid username required</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Working Hours</value>
  </data>
  <data name="BookingStatus_Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="BookingStatus_Pending" xml:space="preserve">
    <value>BookingStatus_Pending</value>
  </data>
  <data name="BookingStatus_Confirmed" xml:space="preserve">
    <value>BookingStatus_Confirmed</value>
  </data>
  <data name="BookingStatus_Rejected" xml:space="preserve">
    <value>BookingStatus_Rejected</value>
  </data>
  <data name="BookingStatus_Archived" xml:space="preserve">
    <value>BookingStatus_Archived</value>
  </data>
  <data name="BookingRequest" xml:space="preserve">
    <value>Booking Request</value>
  </data>
  <data name="DaysOfWeek_Sunday" xml:space="preserve">
    <value>Sunday</value>
  </data>
  <data name="DaysOfWeek_Monday" xml:space="preserve">
    <value>Monday</value>
  </data>
  <data name="DaysOfWeek_Tuesday" xml:space="preserve">
    <value>Tuesday</value>
  </data>
  <data name="DaysOfWeek_Wednesday" xml:space="preserve">
    <value>Wednesday</value>
  </data>
  <data name="DaysOfWeek_Thursday" xml:space="preserve">
    <value>Thursday</value>
  </data>
  <data name="DaysOfWeek_Friday" xml:space="preserve">
    <value>Friday</value>
  </data>
  <data name="DaysOfWeek_Saturday" xml:space="preserve">
    <value>Saturday</value>
  </data>
  <data name="WorkingTimeDetailed" xml:space="preserve">
    <value>Working Hours Detaled</value>
  </data>
  <data name="AppoConfirmAuto" xml:space="preserve">
    <value>Auto-confirm bookings</value>
  </data>
  <data name="AppoConfirmAutoDesc" xml:space="preserve">
    <value>auto/manual</value>
  </data>
  <data name="AppoExplicitBookable" xml:space="preserve">
    <value>Explicit bookable time</value>
  </data>
  <data name="AppoExplicitBookableDesc" xml:space="preserve">
    <value>Whether bookable time should be indicated in bookable time for each object to be available</value>
  </data>
  <data name="btnBook" xml:space="preserve">
    <value>Book</value>
  </data>
  <data name="Gallery" xml:space="preserve">
    <value>Gallery</value>
  </data>
  <data name="YourName" xml:space="preserve">
    <value>Your name</value>
  </data>
  <data name="BtnBookNow" xml:space="preserve">
    <value>Book Now!</value>
  </data>
  <data name="BookOnline" xml:space="preserve">
    <value>Book Online</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Go Back</value>
  </data>
  <data name="NameTitle" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="YourFName" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="YourLName" xml:space="preserve">
    <value>Family name</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>String</value>
  </data>
  <data name="UpdatingData" xml:space="preserve">
    <value>Updating data..</value>
  </data>
  <data name="AppoNoTimeDesc" xml:space="preserve">
    <value>For the given conditions there is no available time. Try changing the conditions below:</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>Oops!</value>
  </data>
  <data name="Canceled" xml:space="preserve">
    <value>Canceled</value>
  </data>
  <data name="ClientId" xml:space="preserve">
    <value>Client Id</value>
  </data>
  <data name="AppoTimeDescWho" xml:space="preserve">
    <value>{0} will be waiting for You at {1}</value>
  </data>
  <data name="BookingDateTimeDescFormat" xml:space="preserve">
    <value>we'll be waiting for You at {0}</value>
  </data>
  <data name="AppoTimeDescPending" xml:space="preserve">
    <value>Please await confirmation for {0}</value>
  </data>
  <data name="ConfirmationPendingTitle" xml:space="preserve">
    <value>Confirmation pending</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="PatternUrl" xml:space="preserve">
    <value>PatternUrl</value>
  </data>
  <data name="WallpaperUrl" xml:space="preserve">
    <value>WallpaperUrl</value>
  </data>
  <data name="ControlPanel" xml:space="preserve">
    <value>Control Panel</value>
  </data>
  <data name="AppStrings" xml:space="preserve">
    <value>Strings</value>
  </data>
  <data name="TweakApp" xml:space="preserve">
    <value>Tweak Mobile</value>
  </data>
  <data name="NoTimeAvailable" xml:space="preserve">
    <value>No time available</value>
  </data>
  <data name="ForBookingOnly" xml:space="preserve">
    <value>For Booking Only</value>
  </data>
  <data name="Sections" xml:space="preserve">
    <value>Sections</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>Article</value>
  </data>
  <data name="SeeAlso" xml:space="preserve">
    <value>See also: </value>
  </data>
  <data name="PriceMask" xml:space="preserve">
    <value>Price Mask</value>
  </data>
  <data name="Appearence" xml:space="preserve">
    <value>Appearence</value>
  </data>
  <data name="SortNotes" xml:space="preserve">
    <value>By Notes</value>
  </data>
  <data name="OurContacts" xml:space="preserve">
    <value>Contact Us</value>
  </data>
  <data name="HowToGet" xml:space="preserve">
    <value>Find route</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>Go Back</value>
  </data>
  <data name="BookingObjectsShort" xml:space="preserve">
    <value>Objects</value>
  </data>
  <data name="ExplainDate_Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="ExplainDate_Tomm" xml:space="preserve">
    <value>Tomorrow</value>
  </data>
  <data name="ExplainDate_X" xml:space="preserve">
    <value>In {0} days</value>
  </data>
  <data name="ExplainDate_X1" xml:space="preserve">
    <value>In {0} days</value>
  </data>
  <data name="ExplainDate_X2" xml:space="preserve">
    <value>In {0} days</value>
  </data>
  <data name="Authenticating" xml:space="preserve">
    <value>Authenticating..</value>
  </data>
  <data name="YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins" xml:space="preserve">
    <value>You have tried too many times, please try again in {0} mins.</value>
  </data>
  <data name="RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater" xml:space="preserve">
    <value>Registration failed. Please check you have provided a valid phone number or try again later.</value>
  </data>
  <data name="ПроверьтеКорректностьВведенныхДанных" xml:space="preserve">
    <value>Please check the data You entered is valid.</value>
  </data>
  <data name="BookingFailed" xml:space="preserve">
    <value>Booking failed.</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>Verifying code..</value>
  </data>
  <data name="WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking" xml:space="preserve">
    <value>We have sent you a confirmation code by SMS. Please enter it below to process your booking:</value>
  </data>
  <data name="BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry" xml:space="preserve">
    <value>Booking failed. Maybe someone has already taken that time, please retry.</value>
  </data>
  <data name="FailedToVerifyCode" xml:space="preserve">
    <value>Failed to verify code.</value>
  </data>
  <data name="ReloadingBookingData" xml:space="preserve">
    <value>Reloading booking data..</value>
  </data>
  <data name="BookingDateTimeDesc" xml:space="preserve">
    <value>{0} at {1}</value>
  </data>
  <data name="CodeFromSMS" xml:space="preserve">
    <value>Code From SMS</value>
  </data>
  <data name="BookingFrontDeskStatusType_Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="BookingFrontDeskStatusType_Canceled" xml:space="preserve">
    <value>Canceled</value>
  </data>
  <data name="BookingFrontDeskStatusType_Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="Settings_SelectLanguage" xml:space="preserve">
    <value>Language selection</value>
  </data>
  <data name="ClickToUploadOrDropFileHere" xml:space="preserve">
    <value>Click to upload or drop file here..</value>
  </data>
  <data name="LoadingOriginalImage" xml:space="preserve">
    <value>Loading original image..</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="WithoutDescription" xml:space="preserve">
    <value>Without description.</value>
  </data>
  <data name="Galleries" xml:space="preserve">
    <value>Galleries</value>
  </data>
  <data name="SystemNameHint" xml:space="preserve">
    <value>Not visible in mobile app, system name used for selecting this item in lists etc</value>
  </data>
  <data name="ExplainDateWithInterval" xml:space="preserve">
    <value>We await You {0}</value>
  </data>
  <data name="BookingTimeDescAt" xml:space="preserve">
    <value>At {0}</value>
  </data>
  <data name="Blog" xml:space="preserve">
    <value>Blog</value>
  </data>
  <data name="OpenBlogArticle" xml:space="preserve">
    <value>Open blog article</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>Release Date</value>
  </data>
  <data name="SplashLogo" xml:space="preserve">
    <value>Splash Logo</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Company Image</value>
  </data>
  <data name="DisplayedOverOurContacts" xml:space="preserve">
    <value>Displayed over Our Contacts</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="QuizzQuestionLevel_Easy" xml:space="preserve">
    <value>Easy</value>
  </data>
  <data name="QuizzQuestionLevel_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="QuizzQuestionLevel_Hard" xml:space="preserve">
    <value>Hard</value>
  </data>
  <data name="QuizzQuestionLevel_Superhard" xml:space="preserve">
    <value>Superhard</value>
  </data>
  <data name="QuizzQuestionImageType_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="QuizzQuestionImageType_Avatar" xml:space="preserve">
    <value>Avatar</value>
  </data>
  <data name="Answers" xml:space="preserve">
    <value>Answers</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Answer</value>
  </data>
  <data name="Correct" xml:space="preserve">
    <value>Correct</value>
  </data>
  <data name="QuizzQuestions" xml:space="preserve">
    <value>Quizz Questions</value>
  </data>
  <data name="SortByLevel" xml:space="preserve">
    <value>By Level</value>
  </data>
  <data name="QRCodeImageUrl" xml:space="preserve">
    <value>QR Code Image Url</value>
  </data>
  <data name="Quizz" xml:space="preserve">
    <value>Quizz</value>
  </data>
  <data name="QuestionDurationTime" xml:space="preserve">
    <value>Time for one question</value>
  </data>
  <data name="Quizzes" xml:space="preserve">
    <value>Quizzes</value>
  </data>
  <data name="QuestionDurationTimeSecs" xml:space="preserve">
    <value>Time for all questions in secs</value>
  </data>
  <data name="Brands" xml:space="preserve">
    <value>Brands</value>
  </data>
  <data name="PromoActons" xml:space="preserve">
    <value>Promo Actons</value>
  </data>
  <data name="IncludeQuestionsWithTags" xml:space="preserve">
    <value>Include with tags</value>
  </data>
  <data name="ExcludeQuestionsWithTags" xml:space="preserve">
    <value>Exclude with tags</value>
  </data>
  <data name="SearchKeywords" xml:space="preserve">
    <value>Search keywords</value>
  </data>
  <data name="PleaseSaveThisRecordToBeAbleToAddSubRecords" xml:space="preserve">
    <value>Please save this record to be able to add sub-records.</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="PromoPrizes" xml:space="preserve">
    <value>Prizes</value>
  </data>
  <data name="CorrectAnswersPercent" xml:space="preserve">
    <value>Correct answers percent</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Discount</value>
  </data>
  <data name="PromoAction" xml:space="preserve">
    <value>Promo Action</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="PromoStatus_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="PromoStatus_Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="PromoStatus_Incoming" xml:space="preserve">
    <value>Incoming</value>
  </data>
  <data name="PromoStatus_Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="SortByStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="ExplainSeconds_0" xml:space="preserve">
    <value>seconds</value>
  </data>
  <data name="ExplainSeconds_1" xml:space="preserve">
    <value>seconds</value>
  </data>
  <data name="ExplainSeconds_X1" xml:space="preserve">
    <value>seconds</value>
  </data>
  <data name="ExplainSeconds_X2" xml:space="preserve">
    <value>seconds</value>
  </data>
  <data name="ExplainSeconds_X" xml:space="preserve">
    <value>seconds</value>
  </data>
  <data name="Success_" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="CouponPercent" xml:space="preserve">
    <value>Coupon Percent</value>
  </data>
  <data name="LinkMoreInfo" xml:space="preserve">
    <value>More Info Link</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="QuestionsTotal" xml:space="preserve">
    <value>Questions Total To Show</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="IncludeQuestionsWithTagsDesc" xml:space="preserve">
    <value>* - include all questions. Can also specify other custom tags to be included.</value>
  </data>
  <data name="OpenPromoInApp" xml:space="preserve">
    <value>Open promoaction in app</value>
  </data>
  <data name="MaxPrizes" xml:space="preserve">
    <value>Prizes Total</value>
  </data>
  <data name="PrizesLeft" xml:space="preserve">
    <value>Prizes Left</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="CustomerConnectResult_Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="CustomerConnectResult_Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="CustomerConnectResult_Denied" xml:space="preserve">
    <value>Denied</value>
  </data>
  <data name="CustomerConnectResult_NetworkError" xml:space="preserve">
    <value>Network Error</value>
  </data>
  <data name="CustomerConnectResult_UnknownError" xml:space="preserve">
    <value>Unknown Error</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="TotalConns" xml:space="preserve">
    <value>Total requests</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Request</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>Requests</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Oder</value>
  </data>
  <data name="TotalConnsOk" xml:space="preserve">
    <value>Confirmed requests</value>
  </data>
  <data name="CustomerConnectResult_Used" xml:space="preserve">
    <value>Expired</value>
  </data>
  <data name="TimeCalculator_Sec" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="TimeCalculator_Min" xml:space="preserve">
    <value>m</value>
  </data>
  <data name="TimeCalculator_Hour" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="UnitsDescMm" xml:space="preserve">
    <value>Millimeters</value>
  </data>
  <data name="UnitsDescInches" xml:space="preserve">
    <value>Inches</value>
  </data>
  <data name="UnitsKeyMm" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="UnitsKeyInches" xml:space="preserve">
    <value>in</value>
  </data>
  <data name="ChooseUnits" xml:space="preserve">
    <value>Choose units</value>
  </data>
  <data name="MenuPageAbout" xml:space="preserve">
    <value>About</value>
  </data>
  <data name="MenuPageContacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="MenuPageNews" xml:space="preserve">
    <value>News</value>
  </data>
  <data name="MenuPageSalons" xml:space="preserve">
    <value>Find A Center</value>
  </data>
  <data name="PageNewsTitle" xml:space="preserve">
    <value>Latest News</value>
  </data>
  <data name="PageSalonsTitle" xml:space="preserve">
    <value>Your Salon</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value>   </value>
  </data>
  <data name="ButtonRegionChange" xml:space="preserve">
    <value>Change Region</value>
  </data>
  <data name="ButtonNavigate" xml:space="preserve">
    <value>Find Route</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>My Salon</value>
  </data>
  <data name="PageFindSalon" xml:space="preserve">
    <value>Our Centers</value>
  </data>
  <data name="ErrorConnRegions" xml:space="preserve">
    <value>Connection error. Please try again later.</value>
  </data>
  <data name="ErrorConnSalons" xml:space="preserve">
    <value>Connection error. Please try again later.</value>
  </data>
  <data name="ErrorConnNews" xml:space="preserve">
    <value>Connection error. Please try again later.</value>
  </data>
  <data name="ErrorConnection" xml:space="preserve">
    <value>Connection error. Please check your Internet connection and try again.</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PageFavSalon" xml:space="preserve">
    <value>My Favorite Center</value>
  </data>
  <data name="FavoriteEmpty1" xml:space="preserve">
    <value>Welcome!</value>
  </data>
  <data name="NeedInternet" xml:space="preserve">
    <value>Data loading failed.. 
Please check your internet connection.</value>
  </data>
  <data name="ErrorCannotNavigate" xml:space="preserve">
    <value>Please ensure that you have a map application installed.</value>
  </data>
  <data name="BrowseSite" xml:space="preserve">
    <value>Browse Website</value>
  </data>
  <data name="ShowOnMap" xml:space="preserve">
    <value>Show Us On Map</value>
  </data>
  <data name="Call" xml:space="preserve">
    <value>Call</value>
  </data>
  <data name="SaveItToFavorites" xml:space="preserve">
    <value>Save for quick access!</value>
  </data>
  <data name="ButtonAddToFavs" xml:space="preserve">
    <value>Add to favorites</value>
  </data>
  <data name="ButtonConnect" xml:space="preserve">
    <value>Reconnect</value>
  </data>
  <data name="ButtonHowToGetToUs" xml:space="preserve">
    <value>Instructions</value>
  </data>
  <data name="AreYouSureRemoveFromFavs" xml:space="preserve">
    <value>Really remove center from favorites?</value>
  </data>
  <data name="RemoveFromFavs" xml:space="preserve">
    <value>Remove From Favorites</value>
  </data>
  <data name="FavDescBlabla" xml:space="preserve">
    <value>Now You can quickly access this center data from the Favorites section.</value>
  </data>
  <data name="AboutSalon" xml:space="preserve">
    <value>About the Center</value>
  </data>
  <data name="GPSPermissionsNeedOn" xml:space="preserve">
    <value>We would need access to your GPS position to be able to help find us. Enable access now?</value>
  </data>
  <data name="GPSPleaseTurnOn" xml:space="preserve">
    <value>Your GPS is turned off. Please turn it on for us to be able to assist You.</value>
  </data>
  <data name="HowToGetThereMetroTitle" xml:space="preserve">
    <value>By Metro:</value>
  </data>
  <data name="PageContactsInfo" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Contact Us</value>
  </data>
  <data name="WeOnMap" xml:space="preserve">
    <value>Show On Map</value>
  </data>
  <data name="Settings_Copyright" xml:space="preserve">
    <value>© 2019-2025 Art of Foto and respective content owners</value>
  </data>
  <data name="GettingGPSCoords" xml:space="preserve">
    <value>Finding your location..</value>
  </data>
  <data name="PageSalonList" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="PageSalonListRegion" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="ButtonProSalons" xml:space="preserve">
    <value>Info For Centers</value>
  </data>
  <data name="ButtonProPpl" xml:space="preserve">
    <value>Info For Specialists</value>
  </data>
  <data name="ButtonProPartners" xml:space="preserve">
    <value>Partners Login Here</value>
  </data>
  <data name="PageHowToGetThereInstructions" xml:space="preserve">
    <value>How To Find Us</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FavoriteEmpty2" xml:space="preserve">
    <value>Add your favorite THALION center to this page for quick access.</value>
  </data>
  <data name="NavigateTo" xml:space="preserve">
    <value>Find Route</value>
  </data>
  <data name="FavReplaceConfirm" xml:space="preserve">
    <value>Replace existing favorite with this one?</value>
  </data>
  <data name="ToSalonList" xml:space="preserve">
    <value>View Full Salon List</value>
  </data>
  <data name="km" xml:space="preserve">
    <value>km</value>
  </data>
  <data name="ButtonFindYourSalon" xml:space="preserve">
    <value>Find Your Salon</value>
  </data>
  <data name="FavGratz" xml:space="preserve">
    <value>Congratulations!</value>
  </data>
  <data name="ButtonGotIt" xml:space="preserve">
    <value>Yay cool</value>
  </data>
  <data name="ErrorTitle" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ErrorConSalon" xml:space="preserve">
    <value>Connection error. Please try again later.</value>
  </data>
  <data name="iSalonList" xml:space="preserve">
    <value>Centers</value>
  </data>
  <data name="X_AboutUs" xml:space="preserve">
    <value>About Us</value>
  </data>
  <data name="iRegion" xml:space="preserve">
    <value>On Map</value>
  </data>
  <data name="PageTitleSettings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="SettingsInterface" xml:space="preserve">
    <value>Interface</value>
  </data>
  <data name="Settings_NoTitlesInTabs" xml:space="preserve">
    <value>No text bottom menu</value>
  </data>
  <data name="SettingsStartFav" xml:space="preserve">
    <value>Show page My Salon (Favorite) on program start</value>
  </data>
  <data name="MenuPageHome" xml:space="preserve">
    <value>Homepage</value>
  </data>
  <data name="SettingsAnimation" xml:space="preserve">
    <value>Disable background  animations for battery saving</value>
  </data>
  <data name="BackToSalonList" xml:space="preserve">
    <value>Go Back To Salon List</value>
  </data>
  <data name="SettingsTutorial" xml:space="preserve">
    <value>Always show welcome tutorial at program start</value>
  </data>
  <data name="MenuSomeMore" xml:space="preserve">
    <value>Even More..</value>
  </data>
  <data name="ShowWelcomeSlides" xml:space="preserve">
    <value>Show Welcome Slides</value>
  </data>
  <data name="StartUp" xml:space="preserve">
    <value>START NOW</value>
  </data>
  <data name="UpdateNeded" xml:space="preserve">
    <value>We have released an update, please update the app!</value>
  </data>
  <data name="Bye" xml:space="preserve">
    <value>See You Soon!</value>
  </data>
  <data name="Settings_SilentPush" xml:space="preserve">
    <value>Push messages remain silent</value>
  </data>
  <data name="AskHideWelcome" xml:space="preserve">
    <value>Do You want to hide this welcome message?</value>
  </data>
  <data name="Tutorial_1_Find" xml:space="preserve">
    <value>Find</value>
  </data>
  <data name="Tutorial_2_Add" xml:space="preserve">
    <value>Set</value>
  </data>
  <data name="Tutorial_3_Share" xml:space="preserve">
    <value>Сlick twice</value>
  </data>
  <data name="Tutorial_4_Follow" xml:space="preserve">
    <value>Follow</value>
  </data>
  <data name="Tutorial_1_Find_Desc" xml:space="preserve">
    <value></value>
  </data>
  <data name="Tutorial_2_Add_Desc" xml:space="preserve">
    <value></value>
  </data>
  <data name="Tutorial_3_Share_Desc" xml:space="preserve">
    <value>on the section icon to return to its root</value>
  </data>
  <data name="Tutorial_4_Follow_Desc" xml:space="preserve">
    <value></value>
  </data>
  <data name="WebBack" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="SortKm" xml:space="preserve">
    <value>Sort by Km</value>
  </data>
  <data name="OnMapSalon" xml:space="preserve">
    <value>On Map</value>
  </data>
  <data name="PageSettings_PageSettings_Version" xml:space="preserve">
    <value>version</value>
  </data>
  <data name="MenuProducts" xml:space="preserve">
    <value>Product Catalog</value>
  </data>
  <data name="SubCatsHere" xml:space="preserve">
    <value>Sub-categories:</value>
  </data>
  <data name="AllProductsHere" xml:space="preserve">
    <value>All products from category</value>
  </data>
  <data name="Conseil" xml:space="preserve">
    <value>THALION TIP</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>Search Results</value>
  </data>
  <data name="TapToRead" xml:space="preserve">
    <value>tap to read</value>
  </data>
  <data name="SearchProd" xml:space="preserve">
    <value>Search Products</value>
  </data>
  <data name="EnterString" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>HOT</value>
  </data>
  <data name="Tutorial_5_Products" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="Tutorial_5_Products_Desc" xml:space="preserve">
    <value></value>
  </data>
  <data name="YouHaveSearched" xml:space="preserve">
    <value>You have searched for</value>
  </data>
  <data name="PleaseEnterMoreCharacters" xml:space="preserve">
    <value>Please enter more characters!</value>
  </data>
  <data name="SearchSalonLabel" xml:space="preserve">
    <value>Search Centers</value>
  </data>
  <data name="BtnAppSettings" xml:space="preserve">
    <value>System Settings</value>
  </data>
  <data name="ButtonLater" xml:space="preserve">
    <value>Later</value>
  </data>
  <data name="NiftyGPS_AlertGPSisOff_TurnGPSOn" xml:space="preserve">
    <value>Turn GPS On</value>
  </data>
  <data name="PageSalonList_SortList2_SortedByDistance" xml:space="preserve">
    <value>Sorted by Distance</value>
  </data>
  <data name="PageSalonList_SortList1_SortedByAlphabet" xml:space="preserve">
    <value>Sorted by Alphabet</value>
  </data>
  <data name="SliderAnnounce" xml:space="preserve">
    <value>HOT PRODUCTS</value>
  </data>
  <data name="WishListDesc" xml:space="preserve">
    <value>Products from the catalog can be added to Wish List. 
The list is useful for later shopping with your beauty center or to share with your cosmetician or friends. </value>
  </data>
  <data name="WishListTitle" xml:space="preserve">
    <value>My Wish List</value>
  </data>
  <data name="AboutTheCompany" xml:space="preserve">
    <value>About Us</value>
  </data>
  <data name="AskForConfirmationWhenRemovingItemFromWishList" xml:space="preserve">
    <value>Ask for confirmation when removing items from lists</value>
  </data>
  <data name="OtherCategories" xml:space="preserve">
    <value>OTHER CATEGORIES</value>
  </data>
  <data name="GotoProducts" xml:space="preserve">
    <value>Go To Catalog</value>
  </data>
  <data name="Поделиться" xml:space="preserve">
    <value>Share</value>
  </data>
  <data name="MenuProductsShort" xml:space="preserve">
    <value>Products</value>
  </data>
  <data name="INTHECATEGORY" xml:space="preserve">
    <value>GO TO CATEGORY</value>
  </data>
  <data name="CardProductFull_SetupCell_Ref" xml:space="preserve">
    <value>Ref.</value>
  </data>
  <data name="PageWishList_UpdateFavs_ToCatalogue" xml:space="preserve">
    <value>To Catalogue</value>
  </data>
  <data name="PageWishList_OnBtnShare_МойСписокЖеланийTHALION" xml:space="preserve">
    <value>My Wish List</value>
  </data>
  <data name="ClearList" xml:space="preserve">
    <value>Clear List</value>
  </data>
  <data name="HowToBuyProducts" xml:space="preserve">
    <value>Our products for home use can be purchased in THALION certified centers only.</value>
  </data>
  <data name="HowToBuyNotFound" xml:space="preserve">
    <value>If your THALION favorite center does not have some of the products you’d want in stock, please let us know and we will assist you with the purchase.</value>
  </data>
  <data name="WhereToBuy" xml:space="preserve">
    <value>Where To Buy</value>
  </data>
  <data name="ContactUs2" xml:space="preserve">
    <value>Contact Us</value>
  </data>
  <data name="CardProductFull_Fav_OnDown_ConfirmFavDelete" xml:space="preserve">
    <value>Remove item from wish list?</value>
  </data>
  <data name="PageWishList_OnBtnClearList_ConfirmClearList" xml:space="preserve">
    <value>Are You sure to clear your Wish List?</value>
  </data>
  <data name="GPSPleaseTurnOniOS" xml:space="preserve">
    <value>We would need your GPS coordinates to help You with geo-position.</value>
  </data>
  <data name="NumDesc_Items_Format" xml:space="preserve">
    <value>You have {0} {1} in your list.</value>
  </data>
  <data name="NumDesc_Items_0" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="NumDesc_Items_1" xml:space="preserve">
    <value>item</value>
  </data>
  <data name="NumDesc_Items_with1" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="NumDesc_Items_with2" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="NumDesc_Items_with0" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>Learn More</value>
  </data>
  <data name="ItemAddedToWishList" xml:space="preserve">
    <value>Item added to Wish List</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Press BACK once again to quit the application</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="CatRoot" xml:space="preserve">
    <value>View Catalog</value>
  </data>
  <data name="ToCatRoot" xml:space="preserve">
    <value>To Summary</value>
  </data>
  <data name="iOSTabsStartup_Setup_WhereToFind" xml:space="preserve">
    <value>Where To Find</value>
    
  </data>
  <data name="PrevCategory" xml:space="preserve">
    <value>Prev Category</value>
  </data>
  <data name="NextCategory" xml:space="preserve">
    <value>Show Me More..</value>
  </data>
  <data name="SeaAlso" xml:space="preserve">
    <value>SEE ALSO</value>
  </data>
  <data name="BackToCatalog" xml:space="preserve">
    <value>Product Catalog</value>
  </data>
  <data name="iOSTabsStartup_Setup_Favorites" xml:space="preserve">
    <value>Favorites</value>
  </data>
  <data name="iOSTabsStartup_Setup_MyPreferences" xml:space="preserve">
    <value>My Favorites</value>
  </data>
  <data name="DoYouWantUsToGPS" xml:space="preserve">
    <value>If You would you like to find THALION centers closest to You please be positive at the next window.</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Hello</value>
  </data>
  <data name="btnTryAgain" xml:space="preserve">
    <value>Try Again</value>
  </data>
  <data name="btnCheckSettings" xml:space="preserve">
    <value>Check Settings</value>
  </data>
  <data name="ProcessingYourBooking" xml:space="preserve">
    <value>Processing your booking..</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>About..</value>
  </data>
  <data name="X_TimeCalcShort" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="X_TimeCalcFull" xml:space="preserve">
    <value>Time Calculator</value>
  </data>
  <data name="X_BellowsShort" xml:space="preserve">
    <value>Bellows</value>
  </data>
  <data name="X_BellowsFull" xml:space="preserve">
    <value>Bellows Extension</value>
  </data>
  <data name="X_FocalLength" xml:space="preserve">
    <value>Focal Length</value>
  </data>
  <data name="X_BellowsDesc" xml:space="preserve">
    <value>Here you can obtain a precise value for setting your f/stop using a bellows camera</value>
  </data>
  <data name="Millisecs" xml:space="preserve">
    <value>ms</value>
  </data>
  <data name="NumericDoubleDot" xml:space="preserve">
    <value>.</value>
  </data>
  <data name="OfflineCompanyDesc" xml:space="preserve">
    <value>The _Art of Foto_ Project was founded at the beginning of 2011. The idea behind the gallery is to preserve Russia’s photographic heritage and, at the same time, to support contemporary analogue photography, as well as to help it to develop, both artistically and technologically. We opened the Art of Foto gallery, a darkroom and a large-format photo studio in St. Petersburg in 2015.

The Art of Foto Gallery is a collection of photographs that have historical and artistic value. At the moment, we have works produced by well-known masters such as __Yevgeny Khaldei, Sergei Leontiev, Emmanuel Evzerikhin, Valentin Samarin, Nadya Kuznetsova, Vadim Egorovsky, Sergei Sveshnikov, Valery Plotnikov, Lyudmila Tabolina, Valentin Baranovsky, Sergei Podrogkov, Valery Aksenov, Pavel Apletin__ and others.

The primary goal of the gallery is to promote talented Russian photographers, both at home and abroad. We actively support photographers in St. Petersburg and Moscow whose works are likely to be added to the artistic heritage of Russia. 

Members of the Art of Foto Artists' Union organize annual exhibitions of hand printed black and white photographs in Russia and Europe aiming at creating a positive image of traditional and contemporary Russian photography among connoisseurs and experts worlwide.

The following people collaborated on the app: Nikolay Kovalsky, Roman Zemlyachev, Vadim Levin, Alexander Khokhlov, Alexander Fedoseev, Anton Smuglienko, Igor Smolnikov, Yury Oprya, Anton Ivanov, Yulia Krivoruchko, Tatyana Danilova, Dmitry Konishchev, Jan Schlengel, Dmitry Kochergin, Lena Shaidullina, 李翔 (Li Xiang).</value>
  </data>
  <data name="OfflineCompanyAddress" xml:space="preserve">
    <value>Bolshaya Konyushennaya Street, 1, Sankt-Peterburg, Russia, 191186</value>
  </data>
  <data name="OfflineMapDesc" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="Collapse" xml:space="preserve">
    <value>Collapse</value>
  </data>
  <data name="Expand" xml:space="preserve">
    <value>Expand</value>
  </data>
  <data name="HelpCalculator" xml:space="preserve">
    <value>C - press once to reset the current value, twice for full reset
% - used in combination with the previously entered operation.
For example: press + then % then enter a decimal number.
Result: you have added the percentages just entered to the existing time.</value>
  </data>
  <data name="X_BellowsHelp" xml:space="preserve">
    <value>You can switch between mm and inches by clicking on the corresponding text at the right side of the entry field.</value>
  </data>
  <data name="X_EnableSound" xml:space="preserve">
    <value>Sound</value>
  </data>
  <data name="X_EnableHoursInput" xml:space="preserve">
    <value>Enable hours input</value>
  </data>
  <data name="X_TimerStartedAt" xml:space="preserve">
    <value>Timer started at {0}</value>
  </data>
  <data name="X_TimerFinishedFor" xml:space="preserve">
    <value>Timer finished for {0}</value>
  </data>
  <data name="X_35mmHelp" xml:space="preserve">
    <value>You can switch between mm and inches by clicking on the corresponding text at the right side of the entry field.</value>
  </data>
  <data name="X_DeveloperHelp" xml:space="preserve">
    <value>This is a Developer module help.</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>parts</value>
  </data>
  <data name="Milliliters" xml:space="preserve">
    <value>ml</value>
  </data>
  <data name="X_DeveloperShort" xml:space="preserve">
    <value>Developer</value>
  </data>
  <data name="X_DeveloperFull" xml:space="preserve">
    <value>Mix Developer</value>
  </data>
  <data name="X_35MmShort" xml:space="preserve">
    <value>35 mm</value>
  </data>
  <data name="X_35mmFull" xml:space="preserve">
    <value>Convert 35mm</value>
  </data>
  <data name="X_35mmDesc" xml:space="preserve">
    <value>Here you can get the focal length of your lens expressed in 35mm equivalent.</value>
  </data>
  <data name="X_FrameFormat" xml:space="preserve">
    <value>Frame format:</value>
  </data>
  <data name="X_WithinVolume" xml:space="preserve">
    <value>Within Volume</value>
  </data>
  <data name="X_FromGiven" xml:space="preserve">
    <value>From Given</value>
  </data>
  <data name="X_ResultMl" xml:space="preserve">
    <value>Result (ml.)</value>
  </data>
  <data name="X_SolutionA" xml:space="preserve">
    <value>Part A</value>
  </data>
  <data name="X_SolutionB" xml:space="preserve">
    <value>Part B</value>
  </data>
  <data name="X_Water" xml:space="preserve">
    <value>Water</value>
  </data>
  <data name="X_DeveloperDescA" xml:space="preserve">
    <value>The calculation of the components of the developer, based on the specified volumes.</value>
  </data>
  <data name="X_DeveloperDescB" xml:space="preserve">
    <value>Automatic selection of volumes to obtain the specified amount of developer.</value>
  </data>
  <data name="X_35mmResult" xml:space="preserve">
    <value>Converted</value>
  </data>
  <data name="X_BellowsResult" xml:space="preserve">
    <value>f/Stop</value>
  </data>
  <data name="X_BellowsResultDesc" xml:space="preserve">
    <value>Your result is {0:0.00}</value>
  </data>
  <data name="Settings_ChooseYourTabsMinMax" xml:space="preserve">
    <value>Choose Your Tabs  ({0}/{1} min {2})</value>
  </data>
  <data name="Settings_FavsTabs" xml:space="preserve">
    <value>Select tabs to be shown</value>
  </data>
  <data name="Settings_SelectTheme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="X_ThemeDark" xml:space="preserve">
    <value>Dark</value>
  </data>
  <data name="X_ThemeLight" xml:space="preserve">
    <value>Light</value>
  </data>
  <data name="X_AboutFooter" xml:space="preserve">
    <value>App developed by AppoMobi</value>
  </data>
  <data name="X_35mmResultDesc" xml:space="preserve">
    <value>K = {0}, diagonal is {1}.

{2}</value>
  </data>
  <data name="X_SolutionResult" xml:space="preserve">
    <value>Ready solution</value>
  </data>
  <data name="AskForRating_Question" xml:space="preserve">
    <value>Enjoying {0}?</value>
  </data>
  <data name="AskForRating_ThanksForNegative" xml:space="preserve">
    <value>Thank you for your feedback, we will try to improve our app!</value>
  </data>
  <data name="AskForRating_GooglePlay" xml:space="preserve">
    <value>Thank you for rating us on GooglePlay, we will be very grateful!</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Required</value>
  </data>
  <data name="X_NoFilter" xml:space="preserve">
    <value>No Filter</value>
  </data>
  <data name="X_ReciprocityHint" xml:space="preserve">
    <value>Calculation of exposure, taking into account the influence of the Schwarzschild effect</value>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>Overflow</value>
  </data>
  <data name="X_AdjustedTime" xml:space="preserve">
    <value>Corrected Exposure</value>
  </data>
  <data name="X_Mins" xml:space="preserve">
    <value>Mins</value>
  </data>
  <data name="X_Secs" xml:space="preserve">
    <value>Secs</value>
  </data>
  <data name="TimeCalculator_Day" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="FilmNotes_Kodak" xml:space="preserve">
    <value>Data from 2016
The manufacturer recommends making a correction when developing:
&gt; 2 seconds: -10%
&gt; 50 sec: -20%
&gt; 20 min: -30%</value>
  </data>
  <data name="TestOne" xml:space="preserve">
    <value>Eat some stuff</value>
  </data>
  <data name="X_UnknownFormula" xml:space="preserve">
    <value>Formula was not communicated by brand...</value>
  </data>
  <data name="X_DevelopmentUnrecommended" xml:space="preserve">
    <value>This development duration is not recommended by the manufacturer.</value>
  </data>
  <data name="X_Reciprocity" xml:space="preserve">
    <value>Reciprocity</value>
  </data>
  <data name="X_ReciprocityHelp" xml:space="preserve">
    <value>WARNING

The value of amendments when shooting with the filter by manufacturer and type averaged lighting. 

Recommend that you test the filters and film to obtain a stable and accurate result.</value>
  </data>
  <data name="X_OwnFormula" xml:space="preserve">
    <value>Due to the absence of data from the manufacturer own formula is used</value>
  </data>
  <data name="X_Unneeded" xml:space="preserve">
    <value>According to the vendor correction is not needed in this range</value>
  </data>
  <data name="X_OurNews" xml:space="preserve">
    <value>Our News</value>
  </data>
  <data name="X_NotesKodak3200" xml:space="preserve">
    <value>Data from 2002
According to the manufacturer, it is not necessary to make adjustments below 1 second, for more than 1 second we are using our own formula</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Camera</value>
  </data>
  <data name="CameraHelp" xml:space="preserve">
    <value>The camera is intended to view negative time, you can change the BW/Color and save the frame to the gallery in HQ.</value>
  </data>
  <data name="CameraFull" xml:space="preserve">
    <value>Negative Camera</value>
  </data>
  <data name="PermissionsError" xml:space="preserve">
    <value>This module cannot work without permissions. Please authorize the app in system settings or uninstall the app and install from scratch to get the system permissions request again.</value>
  </data>
  <data name="NoPermissions" xml:space="preserve">
    <value>No Permissions</value>
  </data>
  <data name="Viewfinder" xml:space="preserve">
    <value>Viewfinder</value>
  </data>
  <data name="ViewfinderFull" xml:space="preserve">
    <value>Viewfinder</value>
  </data>
  <data name="Selection" xml:space="preserve">
    <value>Selection</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="LensesFor" xml:space="preserve">
    <value>Lenses for "{0}"</value>
  </data>
  <data name="ChangeFormat" xml:space="preserve">
    <value>Change format</value>
  </data>
  <data name="EditPresets" xml:space="preserve">
    <value>Edit presets</value>
  </data>
  <data name="CameraZoomHelp" xml:space="preserve">
    <value>This module is designed for approximate simulation of analog viewfinders. You can zoom the screen with your fingers. Green values can be tapped.</value>
  </data>
  <data name="Preset" xml:space="preserve">
    <value>Preset</value>
  </data>
  <data name="Films" xml:space="preserve">
    <value>Films</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="NoLensAdded" xml:space="preserve">
    <value>No lens added</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="AddLens" xml:space="preserve">
    <value>Add Lens (mm)</value>
  </data>
  <data name="OptionScreenOn" xml:space="preserve">
    <value>Screen always on</value>
  </data>
  <data name="Adjustment" xml:space="preserve">
    <value>Adjustment</value>
  </data>
  <data name="X_OptionUseGeo" xml:space="preserve">
    <value>Geotag pictures</value>
  </data>
  <data name="X_NeedMoreForGeo" xml:space="preserve">
    <value>Permissions required to geotag photos</value>
  </data>
  <data name="X_OptionSpecialCameraFolder" xml:space="preserve">
    <value>Use Art Of Foto folder</value>
  </data>
  <data name="Reconnect" xml:space="preserve">
    <value>Reconnect</value>
  </data>
  <data name="BtnOpen" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="LightPad" xml:space="preserve">
    <value>Light Table</value>
  </data>
  <data name="LightPadShort" xml:space="preserve">
    <value>Light Table</value>
  </data>
  <data name="Exposure" xml:space="preserve">
    <value>Exposure</value>
  </data>
  <data name="Aperture" xml:space="preserve">
    <value>Aperture</value>
  </data>
  <data name="Shutter" xml:space="preserve">
    <value>Shutter</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>Retry</value>
  </data>
  <data name="SpotMeter" xml:space="preserve">
    <value>Spot Meter</value>
  </data>
  <data name="SpotMeterShort" xml:space="preserve">
    <value>Spot Meter</value>
  </data>
  <data name="Correction" xml:space="preserve">
    <value>Correction</value>
  </data>
  <data name="HelpLightPad" xml:space="preserve">
    <value>A light table for viewing transparent materials on the lumen, for example, films for evaluating the quality of the manure, when there is no more professional device at hand.</value>
  </data>
  <data name="HelpExposure" xml:space="preserve">
    <value>Measurement of exposure based on data from the device camera.</value>
  </data>
  <data name="HelpSpot" xml:space="preserve">
    <value>Pointed light frozen (exposure in the reflected light).</value>
  </data>
  <data name="LightMeter" xml:space="preserve">
    <value>Exposure Meter</value>
  </data>
  <data name="LightMeterShort" xml:space="preserve">
    <value>Exposure</value>
  </data>
  <data name="TimerShort" xml:space="preserve">
    <value>Process Timer</value>
  </data>
  <data name="Timer" xml:space="preserve">
    <value>Process Timer</value>
  </data>
  <data name="HelpTimer" xml:space="preserve">
    <value>Timer for the manure and printing of photographs. Light radiated by the device can lead to light, reduce the brightness of the screen to a minimum and be sure to test photosensitive materials before use.</value>
  </data>
  <data name="WarningTimer" xml:space="preserve">
    <value>All steps of the developing process can be edited in the settings.</value>
  </data>
  <data name="WarningSpot" xml:space="preserve">
    <value>The calculated exposition is reference and approximate in nature, based on data from the device camera, for accurate measurement you use professional exhibiters.</value>
  </data>
  <data name="WarningExposure" xml:space="preserve">
    <value>The calculated exposition is reference and approximate in nature, based on data from EXIF, and use professional exhibiters for accurate measurement.</value>
  </data>
  <data name="EditStep" xml:space="preserve">
    <value>Edit Step</value>
  </data>
  <data name="NewStep" xml:space="preserve">
    <value>New Step</value>
  </data>
  <data name="TimerStepSoak" xml:space="preserve">
    <value>Soak</value>
  </data>
  <data name="TimerStepDeveloper" xml:space="preserve">
    <value>Developer</value>
  </data>
  <data name="TimerStopBath" xml:space="preserve">
    <value> Stop Bath</value>
  </data>
  <data name="TimerStepFixer" xml:space="preserve">
    <value>Fixer</value>
  </data>
  <data name="TimerStepWash" xml:space="preserve">
    <value>Wash</value>
  </data>
  <data name="SkinRed" xml:space="preserve">
    <value>Red</value>
  </data>
  <data name="SkinDefault" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="Skin" xml:space="preserve">
    <value>Skin</value>
  </data>
  <data name="Steps" xml:space="preserve">
    <value>Steps</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="Pause" xml:space="preserve">
    <value>Pause</value>
  </data>
  <data name="Resume" xml:space="preserve">
    <value>Resume</value>
  </data>
  <data name="White" xml:space="preserve">
    <value>White</value>
  </data>
</root>