﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace AppoMobi.CloudPayments.Models
{
    public class CardInfo 
    {
        public CardInfo()
        {

        }

        public CardInfo(string number, string expirationDate, string cvv)
        {
            Number = number;
            ExpirationDate = expirationDate;
            CVV = cvv;
        }

        public string CVV { get; set; }

        public string Number { get; set; }

        public string ExpirationDate { get; set; }


        [JsonIgnore]
        public string Cryptogram
        {
            get
            {
                var test = makeCardCryptogramPacket(this.Number, this.ExpirationDate, this.CVV,
                    CloudPaymentsSdk.ClientId);

                var real = CloudPaymentsSdk.Current.CreateCryptogram(this);

                if (test.Length != real.Length)
                {
                    var stop = "len shit";
                    return "";
                }
                
                return test;
            }
        }

        #region SDK port from iOS to Net

        //      protected const string PublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArBZ1NNjvszen6BNWsgyDUJvDUZDtvR4jKNQtEwW1iW7hqJr0TdD8hgTxw3DfH+Hi/7ZjSNdH5EfChvgVW9wtTxrvUXCOyJndReq7qNMo94lHpoSIVW82dp4rcDB4kU+q+ekh5rj9Oj6EReCTuXr3foLLBVpH0/z1vtgcCfQzsLlGkSTwgLqASTUsuzfI8viVUbxE1a+600hN0uBh/CYKoMnCp/EhxV8g7eUmNsWjZyiUrV8AA/5DgZUCB+jqGQT/Dhc8e21tAkQ3qan/jQ5i/QYocA/4jW3WQAldMLj0PA36kINEbuDKq8qRh25v+k4qyjb7Xp4W2DywmNtG3Q20MQIDAQAB";

        protected const string PublicKey =
            "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArBZ1NNjvszen6BNWsgyD\nUJvDUZDtvR4jKNQtEwW1iW7hqJr0TdD8hgTxw3DfH+Hi/7ZjSNdH5EfChvgVW9wt\nTxrvUXCOyJndReq7qNMo94lHpoSIVW82dp4rcDB4kU+q+ekh5rj9Oj6EReCTuXr3\nfoLLBVpH0/z1vtgcCfQzsLlGkSTwgLqASTUsuzfI8viVUbxE1a+600hN0uBh/CYK\noMnCp/EhxV8g7eUmNsWjZyiUrV8AA/5DgZUCB+jqGQT/Dhc8e21tAkQ3qan/jQ5i\n/QYocA/4jW3WQAldMLj0PA36kINEbuDKq8qRh25v+k4qyjb7Xp4W2DywmNtG3Q20\nMQIDAQAB\n-----END PUBLIC KEY-----";


        protected const string kPublicKeyVersion = "04";

        public static RSAParameters SetPublicKey(string modulus, string exponent)
        {
            RSAParameters result = new RSAParameters();
            result.Modulus = Convert.FromBase64String(modulus);
            result.Exponent = Convert.FromBase64String(exponent);
            
            return result;
        }



        //public static string ConvertToXmlPublicJavaKey(string publicJavaKey)
        //{
        //    RsaKeyParameters publicKeyParam = (RsaKeyParameters)PublicKeyFactory.CreateKey(Convert.FromBase64String(publicJavaKey));
        //    string xmlpublicKey = string.Format("<RSAKeyValue><Modulus>{0}</Modulus><Exponent>{1}</Exponent></RSAKeyValue>",
        //        Convert.ToBase64String(publicKeyParam.Modulus.ToByteArrayUnsigned()),
        //        Convert.ToBase64String(publicKeyParam.Exponent.ToByteArrayUnsigned()));
        //    return xmlpublicKey;
        //}

        public string makeCardCryptogramPacket(string cardNumberString, string expDateString, string CVVString, string merchantPublicIDString)
        {

            // ExpDate must be in YYMM format
            var cleanCard = RemoveNonDigits(cardNumberString);
            String shortNumber = cleanCard.Substring(0, 6) + cleanCard.Substring(cleanCard.Length - 4, 4);


            //todo clean it
            var cleanExp = RemoveNonDigits(expDateString);
            var cardExpirationDateString = cleanExp.Substring(2, 2) + cleanExp.Substring(0, 2);

            // create cryptogram
            var cleanCardNumber = RemoveNonDigits(cardNumberString);
            var decryptedCryptogram = $"{cleanCardNumber}@{cardExpirationDateString}@{CVVString}@{merchantPublicIDString}";

            Debug.WriteLine($"\n{PublicKey}\n");

            var engine = TFRSAEncryption.ImportPublicKey(PublicKey);

            
            var pem = TFRSAEncryption.ExportPublicKey(engine);

            Debug.WriteLine($"\n{pem}\n");

            var encyption = new TFRSAEncryption();
            var cryptogramString= encyption.RsaEncryptCloudPaymentsWithPemPublicKey(decryptedCryptogram, PublicKey);

            if (!string.IsNullOrEmpty(cryptogramString))
            {
                cryptogramString = cryptogramString.Replace("\n", "");
                cryptogramString = cryptogramString.Replace("\r", "");
            }

            var packetString = "02";
            packetString += shortNumber;
            packetString += cardExpirationDateString;
            packetString += kPublicKeyVersion;
            packetString += cryptogramString;

            return packetString;
        }

        public static string EncryptRSA(string strText, string pemPublicKey)
        {

            

            //var pemKey = PemKeyUtils.DecodeOpenSSLPublicKey(pemPublicKey);

            //var importedKey = PemKeyUtils.DecodeX509PublicKeyToParameters(pemKey);

            //var xmlKey = $"<RSAKeyValue><Modulus>{publicKey}</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>";

            var testData = Encoding.ASCII.GetBytes(strText);

            using (var rsa = new RSACryptoServiceProvider(1024))
            {
                try
                {
                    // client encrypting data with public key issued by server                    
                    //rsa.FromXmlString(xmlKey);
 
                    rsa.ImportParameters(SetPublicKey(PublicKey.Substring(0, PublicKey.Length-4), PublicKey.Substring(PublicKey.Length-4, 4)));

                    //rsa.ImportParameters(importedKey);

                    var encryptedData = rsa.Encrypt(testData, true);
                    
                    var base64Encrypted = Convert.ToBase64String(encryptedData);


                    var exportKey = ExportPublicKeyToPEMFormat(rsa);

                    Debug.WriteLine($"[RSA] \n{exportKey}");

                    return base64Encrypted;
                }
                catch(Exception e)
                {
                    var stop = e;
                    throw e;
                }
                finally
                {
                    rsa.PersistKeyInCsp = false;
                }
            }
        }
        /*
        string EncryptRSA(string plainTextString,string publicKey)
    {
        size_t cipherBufferSize = SecKeyGetBlockSize(publicKey);
    uint8_t* cipherBuffer = malloc(cipherBufferSize);
    uint8_t* nonce = (uint8_t*)[plainTextString cStringUsingEncoding: NSASCIIStringEncoding];
    SecKeyEncrypt(publicKey,
        kSecPaddingOAEP,
        nonce,
        strlen((char*) nonce ),
    &cipherBuffer[0],
    &cipherBufferSize);
    NSData* encryptedData = [NSData dataWithBytes: cipherBuffer length: cipherBufferSize];
    return [NSDataENBase64 base64StringFromData:encryptedData];
}
        */

    bool IsCardNumberValid(string cardNumberString)
        {
            var cleanCardNumber = RemoveNonDigits(cardNumberString);
    
            if (string.IsNullOrEmpty(cleanCardNumber))
            return false;

            return true;
        }


        public string RemoveNonDigits(string aCreditCardNo)
        {

            var clean =   Regex.Replace(aCreditCardNo, "[^0-9]", "");

            return clean;
        }





        #endregion

        #region JAVA port

        /*
        protected string CardCryptogram(string cardNumber, string cardExp, string cardCvv, string publicId)
        {
            cardNumber = CleanCreditCardNo(cardNumber);
            string shortNumber = cardNumber.Substring(0, 6) + cardNumber.Substring(cardNumber.Length - 4, cardNumber.Length);
            string exp = cardExp.Substring(2, 4) + cardExp.Substring(0, 2);
            string s = cardNumber + "@" + exp + "@" + cardCvv + "@" + publicId;

            Encoding ascii = Encoding.ASCII;
            byte[] asciiBytes = ascii.GetBytes(s);

            byte[] bytes = asciiBytes;
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA1AndMGF1Padding");
            SecureRandom random = new SecureRandom();
            cipher.init(Cipher.ENCRYPT_MODE, getRSAKey(), random);
            byte[] crypto = cipher.doFinal(bytes);
            string crypto64 = "02" +
                              shortNumber +
                              exp + PublicKey +
                              Base64.encodeToString(crypto, Base64.DEFAULT);
            string[] cr_array = crypto64.split("\n");
            crypto64 = "";
            for (int i = 0; i<cr_array.length; i++) {
                crypto64 += cr_array[i];
            }
            return crypto64;
        }
        */

        #endregion

        #region TOOLS

        public static String ExportPublicKeyToPEMFormat(RSACryptoServiceProvider csp)
        {
            TextWriter outputStream = new StringWriter();

            var parameters = csp.ExportParameters(false);
            using (var stream = new MemoryStream())
            {
                var writer = new BinaryWriter(stream);
                writer.Write((byte)0x30); // SEQUENCE
                using (var innerStream = new MemoryStream())
                {
                    var innerWriter = new BinaryWriter(innerStream);
                    EncodeIntegerBigEndian(innerWriter, new byte[] { 0x00 }); // Version
                    EncodeIntegerBigEndian(innerWriter, parameters.Modulus);
                    EncodeIntegerBigEndian(innerWriter, parameters.Exponent);

                    //All Parameter Must Have Value so Set Other Parameter Value Whit Invalid Data  (for keeping Key Structure  use "parameters.Exponent" value for invalid data)
                    EncodeIntegerBigEndian(innerWriter, parameters.Exponent); // instead of parameters.D
                    EncodeIntegerBigEndian(innerWriter, parameters.Exponent); // instead of parameters.P
                    EncodeIntegerBigEndian(innerWriter, parameters.Exponent); // instead of parameters.Q
                    EncodeIntegerBigEndian(innerWriter, parameters.Exponent); // instead of parameters.DP
                    EncodeIntegerBigEndian(innerWriter, parameters.Exponent); // instead of parameters.DQ
                    EncodeIntegerBigEndian(innerWriter, parameters.Exponent); // instead of parameters.InverseQ

                    var length = (int)innerStream.Length;
                    EncodeLength(writer, length);
                    writer.Write(innerStream.GetBuffer(), 0, length);
                }

                var base64 = Convert.ToBase64String(stream.GetBuffer(), 0, (int)stream.Length).ToCharArray();
                outputStream.WriteLine("-----BEGIN PUBLIC KEY-----");
                // Output as Base64 with lines chopped at 64 characters
                for (var i = 0; i < base64.Length; i += 64)
                {
                    outputStream.WriteLine(base64, i, Math.Min(64, base64.Length - i));
                }
                outputStream.WriteLine("-----END PUBLIC KEY-----");

                return outputStream.ToString();

            }
        }
        private static void EncodeIntegerBigEndian(BinaryWriter stream, byte[] value, bool forceUnsigned = true)
        {
            stream.Write((byte)0x02); // INTEGER
            var prefixZeros = 0;
            for (var i = 0; i < value.Length; i++)
            {
                if (value[i] != 0) break;
                prefixZeros++;
            }
            if (value.Length - prefixZeros == 0)
            {
                EncodeLength(stream, 1);
                stream.Write((byte)0);
            }
            else
            {
                if (forceUnsigned && value[prefixZeros] > 0x7f)
                {
                    // Add a prefix zero to force unsigned if the MSB is 1
                    EncodeLength(stream, value.Length - prefixZeros + 1);
                    stream.Write((byte)0);
                }
                else
                {
                    EncodeLength(stream, value.Length - prefixZeros);
                }
                for (var i = prefixZeros; i < value.Length; i++)
                {
                    stream.Write(value[i]);
                }
            }
        }

        private static void EncodeLength(BinaryWriter stream, int length)
        {
            if (length < 0) throw new ArgumentOutOfRangeException("length", "Length must be non-negative");
            if (length < 0x80)
            {
                // Short form
                stream.Write((byte)length);
            }
            else
            {
                // Long form
                var temp = length;
                var bytesRequired = 0;
                while (temp > 0)
                {
                    temp >>= 8;
                    bytesRequired++;
                }
                stream.Write((byte)(bytesRequired | 0x80));
                for (var i = bytesRequired - 1; i >= 0; i--)
                {
                    stream.Write((byte)(length >> (8 * i) & 0xff));
                }
            }
        }
        #endregion

    }
}
