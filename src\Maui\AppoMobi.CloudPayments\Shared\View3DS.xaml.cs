﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AppoMobi.CloudPayments.Models;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace AppoMobi.CloudPayments
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class View3DS : WebView, IDisposable
    {
        public View3DS()
        {
            InitializeComponent();
        }

        private bool disposed;
        public void Dispose()
        {
            if (!disposed)
            {
                disposed = true;
                //todo

                Renderer = null;
            }
        }

        #region INPUT

        public Action<PayApiResponse<Post3dsRequestArgs>> On3DsComplete { get; set; }

        public Transaction Transaction { get; set; }

        public bool HideResult { get; set; } = true;

        /// <summary>
        /// On 3DS actions completed
        /// </summary>
        public Action<PayApiResponse<Post3dsRequestArgs>> Request3DSCallback { get; set; }

        public IView3DS Renderer { get; set; }

        #endregion

        public static string EscapeString(string value)
        {
            return Uri.EscapeDataString(value);
        }

        public void Go()
        {
            var url = $"{Transaction.AcsUrl}?PaReq={EscapeString(Transaction.PayerAuthenticationRequest)}"
                      +$"&MD={EscapeString(Transaction.TransactionId)}"+
                      $"&TermUrl={EscapeString(CloudPaymentsSdk.RedirectUrl)}";

            urlPost = url;
            if (Renderer != null) //check if renderer was already created otherwise postpone
            {
                Renderer.Post(url);
                urlPost = null;
            }
        }

        protected string urlPost;

        protected override void OnPropertyChanged(string propertyName)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == "Renderer")
            {
                if (!string.IsNullOrEmpty(urlPost)) //post was orderer before renderer was created
                {
                    Renderer.Post(urlPost);
                    urlPost = null;
                }
            }
        }
    }


    public interface IView3DS
    {
        void Post(string url);
    }

}